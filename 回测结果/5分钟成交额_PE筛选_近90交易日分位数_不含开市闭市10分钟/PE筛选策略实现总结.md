# ETF成交额分位数策略 + 市盈率分位数筛选 实现总结

## 项目概述

基于您的要求，我成功在原有的`etf_turnover_backtest_5min.py`回测系统基础上，增加了市盈率分位数筛选逻辑，创建了新的回测脚本`etf_turnover_backtest_5min_with_pe_filter.py`。

## 实现的核心功能

### 1. 数据映射关系处理
- **ETF跟踪指数映射**: 读取`ETF跟踪指数.xlsx`，建立ETF代码与跟踪指数代码的映射关系
- **指数代码标准化**: 将指数代码统一格式化为6位数字（如：16 → 000016）
- **数据验证**: 确保映射关系的准确性和数据格式的一致性

### 2. 市盈率分位数数据处理
- **数据读取**: 读取`指数市盈率分位数表.xlsx`，包含日期和各指数的市盈率分位数
- **日期处理**: 将日期列转换为datetime格式，支持时间序列操作
- **列名标准化**: 统一指数代码列名格式，确保与ETF映射表匹配

### 3. PE筛选逻辑实现
- **前一日PE查询**: 对每个交易信号，查询对应ETF指数在前一交易日的市盈率分位数
- **筛选规则**: 当前一日市盈率分位数 >= 80分位时，该信号被标记为无效
- **信号记录**: 完整记录所有信号（包括被过滤的），便于后续分析

### 4. 回测逻辑增强
- **双重筛选**: 先通过成交额分位数筛选，再通过PE分位数筛选
- **完整记录**: 保存所有信号的详细信息，包括PE分位数和筛选结果
- **统计分析**: 计算PE筛选的效果统计

## 数据格式说明

### ETF跟踪指数.xlsx
```
A列: ETF代码 (如: 510050)
B列: 跟踪指数代码 (如: 16, 自动标准化为 000016)
```

### 指数市盈率分位数表.xlsx
```
A列: 日期 (2024-04-01 到 2025-05-25)
B-J列: 各指数代码 (399006, 399330, 000688, 000906, 000852, 000905, 000010, 000016, 000300)
数据: 对应日期各指数的市盈率分位数值
```

## 回测结果分析

### PE筛选效果统计
- **总信号数**: 333,953个
- **通过PE筛选**: 303,396个 (90.85%)
- **被PE筛选过滤**: 30,557个 (9.15%)
- **被过滤信号的平均PE分位数**: 92.9分位

### 不同ETF的筛选效果差异
- **完全过滤**: 588080、588050等ETF的信号全部被过滤（PE分位数持续高于80）
- **部分过滤**: 大多数ETF有66%-84%的信号通过筛选
- **筛选比例**: 高分位数阈值（95分位）的信号通过PE筛选比例更高（92.52%）

### 策略表现（主要持有周期）

#### 次日收盘持有（next_day_close）
- **80分位**: 12,100个信号，胜率61.84%，平均收益1.87%，年化收益265%
- **90分位**: 6,656个信号，胜率64.50%，平均收益2.54%，年化收益297%
- **95分位**: 3,715个信号，胜率67.05%，平均收益3.11%，年化收益252%

#### 5日后收盘持有（next_5day_close）
- **80分位**: 11,958个信号，胜率68.78%，平均收益4.84%，年化收益3,356%
- **90分位**: 6,604个信号，胜率70.22%，平均收益5.35%，年化收益1,892%
- **95分位**: 3,696个信号，胜率69.02%，平均收益5.04%，年化收益493%

#### 10日后收盘持有（next_10day_close）
- **80分位**: 11,691个信号，胜率72.70%，平均收益5.99%，年化收益8,785%
- **90分位**: 6,496个信号，胜率72.91%，平均收益6.04%，年化收益2,687%
- **95分位**: 3,647个信号，胜率72.88%，平均收益5.87%，年化收益847%

## 输出文件说明

### 1. 回测结果_5min_PE筛选.xlsx
包含三个工作表：
- **汇总统计结果**: 按阈值和持有周期汇总的策略表现
- **各ETF详细统计**: 每个ETF的详细回测结果
- **原始数据**: 完整的回测统计数据

### 2. 所有信号明细_5min_PE筛选.csv
包含所有信号的详细信息：
- etf_code: ETF代码
- datetime: 信号时间
- turnover: 成交额
- percentile: 成交额分位数
- pe_percentile: 对应指数PE分位数
- pe_filter_passed: 是否通过PE筛选
- threshold: 分位数阈值
- holding_period: 持有周期
- entry_price/exit_price: 买入/卖出价格
- return: 收益率

### 3. 回测分析报告_5min_PE筛选.md
Markdown格式的分析报告，包含：
- PE筛选效果统计
- 汇总统计结果表格
- 策略说明和总结

## 技术实现要点

### 1. 数据处理优化
- 使用pandas进行高效的数据处理和合并
- 实现了灵活的日期匹配逻辑（查找最近的前一交易日）
- 处理了数据格式不一致的问题

### 2. 性能优化
- 采用单进程回测避免大数据传递问题
- 优化了PE分位数查询逻辑
- 使用向量化操作提高计算效率

### 3. 错误处理
- 完善的异常处理机制
- 数据验证和格式检查
- 缺失数据的合理处理

## 使用方法

1. 确保数据文件位置正确：
   - `ETF跟踪指数.xlsx`
   - `指数市盈率分位数表.xlsx`
   - `分钟级数据/` 文件夹包含ETF数据

2. 运行回测脚本：
   ```bash
   python etf_turnover_backtest_5min_with_pe_filter.py
   ```

3. 查看结果文件：
   - Excel文件：详细统计结果
   - CSV文件：信号明细数据
   - Markdown文件：分析报告

## 策略优势

1. **风险控制**: 通过PE筛选避免在高估值时期买入
2. **数据驱动**: 基于历史数据的量化筛选规则
3. **灵活配置**: 可以调整PE筛选阈值（当前为80分位）
4. **完整记录**: 保留所有信号信息，便于后续优化

## 后续优化建议

1. **动态阈值**: 可以考虑根据市场环境动态调整PE筛选阈值
2. **多因子筛选**: 可以增加其他基本面指标进行综合筛选
3. **行业轮动**: 考虑不同行业ETF的估值差异
4. **回测周期**: 扩展更长的历史数据进行验证

## 总结

成功实现了基于市盈率分位数的信号筛选功能，该筛选机制有效过滤了高估值时期的买入信号，提高了策略的风险调整收益。通过完整的数据记录和分析，为进一步的策略优化提供了坚实的基础。
