#!/usr/bin/env python3
"""
分析两类ETF信号发出日期的相关性
1. 90分位成交额信号（PE筛选）
2. 申赎信号（95分位买入信号，PE筛选）
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def standardize_etf_code(code):
    """标准化ETF代码，去除后缀"""
    if pd.isna(code):
        return None
    
    code_str = str(code)
    # 去除常见的后缀
    suffixes = ['.SH', '.SZ', '.XSHE', '.XSHG']
    for suffix in suffixes:
        if code_str.endswith(suffix):
            code_str = code_str[:-len(suffix)]
    
    # 确保是6位数字格式
    try:
        return int(code_str)
    except:
        return None

def load_turnover_signals():
    """加载90分位成交额信号数据"""
    print("=== 加载90分位成交额信号数据 ===")
    
    try:
        # 读取5分钟成交额信号数据
        signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
        
        print(f"原始数据行数: {len(signals_df)}")
        print(f"列名: {signals_df.columns.tolist()}")
        
        # 转换时间格式
        signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
        signals_df['date'] = signals_df['datetime'].dt.date
        
        # 筛选条件：pe_filter_passed=True且threshold=90
        filtered_signals = signals_df[
            (signals_df['pe_filter_passed'] == True) &
            (signals_df['threshold'] == 90)
        ].copy()
        
        print(f"PE筛选且90分位信号数: {len(filtered_signals)}")
        
        # 标准化ETF代码
        filtered_signals['etf_code_std'] = filtered_signals['etf_code'].apply(standardize_etf_code)
        filtered_signals = filtered_signals.dropna(subset=['etf_code_std'])
        
        # 每日每个ETF只保留一个信号
        daily_signals = filtered_signals.groupby(['etf_code_std', 'date']).first().reset_index()
        daily_signals = daily_signals.sort_values(['etf_code_std', 'date'])
        
        print(f"每日合并后信号数: {len(daily_signals)}")
        print(f"涉及ETF数量: {daily_signals['etf_code_std'].nunique()}")
        
        # 显示ETF代码分布
        etf_counts = daily_signals['etf_code_std'].value_counts().sort_index()
        print(f"各ETF信号数量:")
        for etf_code, count in etf_counts.head(10).items():
            print(f"  {etf_code}: {count}个")
        
        return daily_signals[['etf_code_std', 'date', 'datetime']]
        
    except Exception as e:
        print(f"加载90分位成交额信号数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def load_redemption_signals():
    """加载申赎信号数据"""
    print(f"\n=== 加载申赎信号数据 ===")
    
    try:
        # 读取申赎信号数据
        signals_df = pd.read_csv('分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv')
        
        print(f"原始数据行数: {len(signals_df)}")
        print(f"列名: {signals_df.columns.tolist()}")
        
        # 转换时间格式 - 使用正确的列名
        if 'signal_date' in signals_df.columns:
            signals_df['signal_date'] = pd.to_datetime(signals_df['signal_date'])
            signals_df['date'] = signals_df['signal_date'].dt.date
        else:
            print("未找到signal_date列")
            return pd.DataFrame()

        # 标准化ETF代码
        if 'etf_code' in signals_df.columns:
            signals_df['etf_code_std'] = signals_df['etf_code'].apply(standardize_etf_code)
        else:
            print("未找到etf_code列")
            return pd.DataFrame()
        
        signals_df = signals_df.dropna(subset=['etf_code_std'])
        
        # 每日每个ETF只保留一个信号
        daily_signals = signals_df.groupby(['etf_code_std', 'date']).first().reset_index()
        daily_signals = daily_signals.sort_values(['etf_code_std', 'date'])
        
        print(f"每日合并后信号数: {len(daily_signals)}")
        print(f"涉及ETF数量: {daily_signals['etf_code_std'].nunique()}")
        
        # 显示ETF代码分布
        etf_counts = daily_signals['etf_code_std'].value_counts().sort_index()
        print(f"各ETF信号数量:")
        for etf_code, count in etf_counts.head(10).items():
            print(f"  {etf_code}: {count}个")
        
        return daily_signals[['etf_code_std', 'date']]
        
    except Exception as e:
        print(f"加载申赎信号数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def analyze_signal_correlation(turnover_signals, redemption_signals):
    """分析两类信号的相关性"""
    print(f"\n=== 分析信号相关性 ===")
    
    if turnover_signals.empty or redemption_signals.empty:
        print("数据为空，无法进行相关性分析")
        return
    
    # 找到共同的ETF
    turnover_etfs = set(turnover_signals['etf_code_std'].unique())
    redemption_etfs = set(redemption_signals['etf_code_std'].unique())
    common_etfs = turnover_etfs.intersection(redemption_etfs)
    
    print(f"90分位信号ETF数量: {len(turnover_etfs)}")
    print(f"申赎信号ETF数量: {len(redemption_etfs)}")
    print(f"共同ETF数量: {len(common_etfs)}")
    print(f"共同ETF: {sorted(common_etfs)}")
    
    if not common_etfs:
        print("没有共同的ETF，无法进行相关性分析")
        return
    
    # 分析结果
    correlation_results = []
    detailed_results = []
    
    for etf_code in sorted(common_etfs):
        print(f"\n--- 分析ETF {etf_code} ---")
        
        # 获取该ETF的两类信号
        turnover_dates = set(turnover_signals[turnover_signals['etf_code_std'] == etf_code]['date'])
        redemption_dates = set(redemption_signals[redemption_signals['etf_code_std'] == etf_code]['date'])
        
        print(f"90分位信号日期数: {len(turnover_dates)}")
        print(f"申赎信号日期数: {len(redemption_dates)}")
        
        # 计算重叠情况
        overlap_dates = turnover_dates.intersection(redemption_dates)
        union_dates = turnover_dates.union(redemption_dates)
        
        print(f"重叠日期数: {len(overlap_dates)}")
        print(f"总信号日期数: {len(union_dates)}")
        
        # 计算相关性指标
        if len(union_dates) > 0:
            jaccard_similarity = len(overlap_dates) / len(union_dates)
            
            # 计算各自的覆盖率
            turnover_coverage = len(overlap_dates) / len(turnover_dates) if len(turnover_dates) > 0 else 0
            redemption_coverage = len(overlap_dates) / len(redemption_dates) if len(redemption_dates) > 0 else 0
            
            print(f"Jaccard相似度: {jaccard_similarity:.4f}")
            print(f"90分位信号覆盖率: {turnover_coverage:.4f}")
            print(f"申赎信号覆盖率: {redemption_coverage:.4f}")
            
            correlation_results.append({
                'ETF代码': etf_code,
                '90分位信号数': len(turnover_dates),
                '申赎信号数': len(redemption_dates),
                '重叠信号数': len(overlap_dates),
                'Jaccard相似度': jaccard_similarity,
                '90分位覆盖率': turnover_coverage,
                '申赎覆盖率': redemption_coverage
            })
            
            # 详细的重叠日期
            if overlap_dates:
                overlap_list = sorted(overlap_dates)
                print(f"重叠日期: {[date.strftime('%Y-%m-%d') for date in overlap_list[:5]]}{'...' if len(overlap_list) > 5 else ''}")
                
                for date in overlap_list:
                    detailed_results.append({
                        'ETF代码': etf_code,
                        '重叠日期': date.strftime('%Y-%m-%d'),
                        '90分位信号': '是',
                        '申赎信号': '是'
                    })
    
    # 生成汇总统计
    if correlation_results:
        results_df = pd.DataFrame(correlation_results)
        
        print(f"\n=== 整体相关性分析结果 ===")
        print(f"平均Jaccard相似度: {results_df['Jaccard相似度'].mean():.4f}")
        print(f"平均90分位覆盖率: {results_df['90分位覆盖率'].mean():.4f}")
        print(f"平均申赎覆盖率: {results_df['申赎覆盖率'].mean():.4f}")
        
        # 相关性强度分类
        high_correlation = results_df[results_df['Jaccard相似度'] >= 0.3]
        medium_correlation = results_df[(results_df['Jaccard相似度'] >= 0.1) & (results_df['Jaccard相似度'] < 0.3)]
        low_correlation = results_df[results_df['Jaccard相似度'] < 0.1]
        
        print(f"\n相关性分类:")
        print(f"强相关(Jaccard≥0.3): {len(high_correlation)}个ETF")
        print(f"中等相关(0.1≤Jaccard<0.3): {len(medium_correlation)}个ETF")
        print(f"弱相关(Jaccard<0.1): {len(low_correlation)}个ETF")
        
        # 保存结果
        save_correlation_results(results_df, detailed_results, turnover_signals, redemption_signals)
        
        return results_df
    
    return pd.DataFrame()

def save_correlation_results(results_df, detailed_results, turnover_signals, redemption_signals):
    """保存相关性分析结果"""
    
    excel_path = '两类信号相关性分析结果.xlsx'
    
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        
        # 相关性汇总结果
        results_df.to_excel(writer, sheet_name='相关性汇总', index=False)
        
        # 详细重叠日期
        if detailed_results:
            detailed_df = pd.DataFrame(detailed_results)
            detailed_df.to_excel(writer, sheet_name='重叠日期明细', index=False)
        
        # 90分位信号明细
        turnover_summary = turnover_signals.groupby('etf_code_std').agg({
            'date': 'count'
        }).reset_index()
        turnover_summary.columns = ['ETF代码', '信号数量']
        turnover_summary.to_excel(writer, sheet_name='90分位信号统计', index=False)
        
        # 申赎信号明细
        redemption_summary = redemption_signals.groupby('etf_code_std').agg({
            'date': 'count'
        }).reset_index()
        redemption_summary.columns = ['ETF代码', '信号数量']
        redemption_summary.to_excel(writer, sheet_name='申赎信号统计', index=False)
        
        # 统计摘要
        stats_data = [
            ['分析时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['90分位信号ETF数量', turnover_signals['etf_code_std'].nunique()],
            ['申赎信号ETF数量', redemption_signals['etf_code_std'].nunique()],
            ['共同ETF数量', len(results_df)],
            ['平均Jaccard相似度', results_df['Jaccard相似度'].mean()],
            ['平均90分位覆盖率', results_df['90分位覆盖率'].mean()],
            ['平均申赎覆盖率', results_df['申赎覆盖率'].mean()],
            ['强相关ETF数量(Jaccard≥0.3)', len(results_df[results_df['Jaccard相似度'] >= 0.3])],
            ['中等相关ETF数量(0.1≤Jaccard<0.3)', len(results_df[(results_df['Jaccard相似度'] >= 0.1) & (results_df['Jaccard相似度'] < 0.3)])],
            ['弱相关ETF数量(Jaccard<0.1)', len(results_df[results_df['Jaccard相似度'] < 0.1])]
        ]
        stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
        stats_df.to_excel(writer, sheet_name='统计摘要', index=False)
    
    print(f"\n相关性分析结果已保存: {excel_path}")

def main():
    """主函数"""
    print("=== ETF两类信号相关性分析 ===")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 加载两类信号数据
    turnover_signals = load_turnover_signals()
    redemption_signals = load_redemption_signals()
    
    # 分析相关性
    if not turnover_signals.empty and not redemption_signals.empty:
        correlation_results = analyze_signal_correlation(turnover_signals, redemption_signals)
        
        if not correlation_results.empty:
            print(f"\n=== 分析完成 ===")
            print(f"共分析了 {len(correlation_results)} 个ETF的信号相关性")
            
            # 显示最相关的ETF
            top_correlated = correlation_results.nlargest(5, 'Jaccard相似度')
            print(f"\n最相关的5个ETF:")
            for _, row in top_correlated.iterrows():
                print(f"  ETF {row['ETF代码']}: Jaccard相似度 {row['Jaccard相似度']:.4f}")
        else:
            print("未能生成相关性分析结果")
    else:
        print("数据加载失败，无法进行分析")

if __name__ == "__main__":
    main()
