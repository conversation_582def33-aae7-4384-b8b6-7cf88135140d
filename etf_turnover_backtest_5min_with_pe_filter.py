# -*- coding: utf-8 -*-
"""
ETF成交额分位数回测系统 (5分钟级别) - 增加市盈率分位数筛选
"""

import os
import sys
import pandas as pd
import numpy as np
import time
import importlib
from concurrent.futures import ProcessPoolExecutor
from collections import defaultdict

# 检查必要的模块是否已安装
def check_required_modules():
    """检查必要的Python模块是否已安装"""
    required_modules = {
        'pandas': 'pd',
        'numpy': 'np',
        'xlsxwriter': None,
        'openpyxl': None,
    }

    missing_modules = []

    for module_name, alias in required_modules.items():
        try:
            if alias:
                if alias not in globals():
                    importlib.import_module(module_name)
                    print(f"模块 {module_name} 已安装但未正确导入，现已导入")
            else:
                importlib.import_module(module_name)
        except ImportError:
            missing_modules.append(module_name)

    if missing_modules:
        print("错误: 缺少以下必要的Python模块:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n请使用以下命令安装缺失的模块:")
        print(f"pip install {' '.join(missing_modules)}")
        return False

    return True

# 设置pandas显示选项
pd.set_option('display.max_rows', 100)
pd.set_option('display.max_columns', 20)
pd.set_option('display.width', 1000)

# 数据文件夹路径
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), '分钟级数据')

# 持有周期定义
HOLDING_PERIODS = {
    '1min': 1,
    '2min': 2,
    '3min': 3,
    '5min': 5,
    '10min': 10,
    'next_day_open': 'next_day_open',
    'next_day_close': 'next_day_close',
    'next_2day_open': 'next_2day_open',
    'next_2day_close': 'next_2day_close',
    'next_3day_close': 'next_3day_close',
    'next_5day_close': 'next_5day_close',
    'next_10day_close': 'next_10day_close',
    'next_15day_close': 'next_15day_close',
    'next_30day_close': 'next_30day_close'
}

# 分位数阈值
PERCENTILE_THRESHOLDS = [80, 90, 95]

def load_pe_data():
    """
    加载市盈率分位数数据和ETF映射关系

    Returns:
        tuple: (pe_data, etf_mapping)
    """
    try:
        # 读取ETF跟踪指数映射表
        etf_mapping_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ETF跟踪指数.xlsx')
        etf_mapping = pd.read_excel(etf_mapping_path)

        # 读取指数市盈率分位数表
        pe_data_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '指数市盈率分位数表.xlsx')
        pe_data = pd.read_excel(pe_data_path)

        # 处理日期列
        pe_data['日期'] = pd.to_datetime(pe_data['日期'])

        # 标准化指数代码格式
        etf_mapping['跟踪指数代码'] = etf_mapping['跟踪指数代码'].astype(str).str.zfill(6)

        # 标准化PE数据的列名（指数代码）
        pe_columns = {}
        for col in pe_data.columns:
            if col != '日期':
                # 统一格式为6位数字
                if str(col).isdigit():
                    pe_columns[col] = str(col).zfill(6)
                else:
                    pe_columns[col] = str(col)

        pe_data = pe_data.rename(columns=pe_columns)

        print(f"成功加载ETF映射关系: {len(etf_mapping)} 条记录")
        print(f"成功加载市盈率数据: {len(pe_data)} 条记录")
        print(f"ETF映射表列名: {list(etf_mapping.columns)}")
        print(f"市盈率数据列名: {list(pe_data.columns)}")

        return pe_data, etf_mapping

    except Exception as e:
        print(f"加载市盈率数据时出错: {e}")
        return None, None

def get_pe_percentile(etf_code, date, pe_data, etf_mapping):
    """
    获取指定ETF在指定日期前一日的市盈率分位数

    Args:
        etf_code: ETF代码
        date: 当前日期
        pe_data: 市盈率分位数数据
        etf_mapping: ETF映射关系

    Returns:
        float: 市盈率分位数，如果无法获取则返回NaN
    """
    try:
        # 获取ETF对应的指数代码
        etf_row = etf_mapping[etf_mapping['ETF代码'] == int(etf_code)]
        if etf_row.empty:
            return np.nan

        index_code = etf_row.iloc[0]['跟踪指数代码']

        # 确保指数代码在PE数据中存在
        if index_code not in pe_data.columns:
            return np.nan

        # 获取前一日的市盈率分位数
        prev_date = date - pd.Timedelta(days=1)

        # 查找最接近的前一个交易日
        available_dates = pe_data['日期'].dt.date
        target_date = prev_date.date()

        # 找到小于等于目标日期的最大日期
        valid_dates = available_dates[available_dates <= target_date]
        if valid_dates.empty:
            return np.nan

        closest_date = valid_dates.max()

        # 获取该日期的市盈率分位数
        pe_row = pe_data[pe_data['日期'].dt.date == closest_date]
        if pe_row.empty:
            return np.nan

        pe_percentile = pe_row.iloc[0][index_code]
        return pe_percentile

    except Exception as e:
        print(f"获取ETF {etf_code} 在日期 {date} 的市盈率分位数时出错: {e}")
        return np.nan

def format_percentage(value, decimals=2, default_na_str="NaN"):
    """Helper function to format percentage values, handling NaNs."""
    if pd.isna(value):
        return default_na_str
    return f"{value:.{decimals}f}%"

def aggregate_to_5min(df):
    """
    将1分钟数据聚合为5分钟数据
    """
    if df.empty:
        return pd.DataFrame()

    df = df.set_index('datetime')
    agg_rules = {
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum',
        'turnover': 'sum',
        'turnover_rate': 'sum',
        'date': 'first',
        'time': 'first'
    }

    df_5min = df.resample('5min').agg(agg_rules)
    df_5min = df_5min.dropna(subset=['open'])
    df_5min = df_5min.reset_index()
    df_5min['time'] = df_5min['datetime'].dt.time
    df_5min['date'] = df_5min['datetime'].dt.date

    return df_5min

def load_etf_data(file_path):
    """
    加载ETF分钟级数据并聚合到5分钟级别
    """
    try:
        df = pd.read_excel(file_path)

        if len(df.columns) >= 8:
            df.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume', 'turnover', 'turnover_rate']
            df['datetime'] = pd.to_datetime(df['datetime'])
            df['date'] = df['datetime'].dt.date
            df['time'] = df['datetime'].dt.time
            df = df.sort_values(['date', 'time'])
            df_5min = aggregate_to_5min(df)
            return df_5min
        else:
            print(f"警告: {file_path} 列数不足")
            return None
    except Exception as e:
        print(f"加载 {file_path} 时出错: {e}")
        return None

def calculate_percentile(current_value, historical_values):
    """
    计算当前值在历史值中的分位数
    """
    if np.isnan(current_value) or len(historical_values) == 0:
        return np.nan

    historical_values = historical_values[~np.isnan(historical_values)]

    if len(historical_values) == 0:
        return np.nan

    percentile = 100 * (np.sum(historical_values < current_value) / len(historical_values))
    return percentile

def get_previous_trading_days(df, current_date, n_days=3):
    """
    获取当前日期前n个交易日
    """
    all_dates = sorted(df['date'].unique())
    current_idx = all_dates.index(current_date) if current_date in all_dates else -1

    if current_idx < n_days:
        return all_dates[:current_idx]
    else:
        return all_dates[current_idx-n_days:current_idx]

def calculate_returns(df, entry_indices, holding_period):
    """
    计算给定买入点和持有周期的收益率
    """
    returns = []
    dates = df['date'].unique()

    for idx in entry_indices:
        if idx + 1 >= len(df):
            continue

        entry_price = df.iloc[idx + 1]['open']
        if np.isnan(entry_price):
            continue

        # 当日分钟级持有
        if isinstance(holding_period, int):
            if idx + holding_period + 1 < len(df) and df.iloc[idx]['date'] == df.iloc[idx + holding_period]['date']:
                exit_price = df.iloc[idx + holding_period]['close']
                if not np.isnan(exit_price):
                    returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})
        # 跨日持有
        else:
            entry_date = df.iloc[idx]['date']
            entry_date_idx = np.where(dates == entry_date)[0][0]

            if holding_period == 'next_day_open' and entry_date_idx + 1 < len(dates):
                next_day = dates[entry_date_idx + 1]
                next_day_data = df[df['date'] == next_day]
                if not next_day_data.empty:
                    exit_price = next_day_data.iloc[0]['open']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})

            elif holding_period == 'next_day_close' and entry_date_idx + 1 < len(dates):
                next_day = dates[entry_date_idx + 1]
                next_day_data = df[df['date'] == next_day]
                if not next_day_data.empty:
                    exit_price = next_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})

            elif holding_period == 'next_2day_open' and entry_date_idx + 2 < len(dates):
                next_2day = dates[entry_date_idx + 2]
                next_2day_data = df[df['date'] == next_2day]
                if not next_2day_data.empty:
                    exit_price = next_2day_data.iloc[0]['open']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})

            elif holding_period == 'next_2day_close' and entry_date_idx + 2 < len(dates):
                next_2day = dates[entry_date_idx + 2]
                next_2day_data = df[df['date'] == next_2day]
                if not next_2day_data.empty:
                    exit_price = next_2day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})

            elif holding_period == 'next_3day_close' and entry_date_idx + 3 < len(dates):
                future_day = dates[entry_date_idx + 3]
                future_day_data = df[df['date'] == future_day]
                if not future_day_data.empty:
                    exit_price = future_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})

            elif holding_period == 'next_5day_close' and entry_date_idx + 5 < len(dates):
                future_day = dates[entry_date_idx + 5]
                future_day_data = df[df['date'] == future_day]
                if not future_day_data.empty:
                    exit_price = future_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})

            elif holding_period == 'next_10day_close' and entry_date_idx + 10 < len(dates):
                future_day = dates[entry_date_idx + 10]
                future_day_data = df[df['date'] == future_day]
                if not future_day_data.empty:
                    exit_price = future_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})

            elif holding_period == 'next_15day_close' and entry_date_idx + 15 < len(dates):
                future_day = dates[entry_date_idx + 15]
                future_day_data = df[df['date'] == future_day]
                if not future_day_data.empty:
                    exit_price = future_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})

            elif holding_period == 'next_30day_close' and entry_date_idx + 30 < len(dates):
                future_day = dates[entry_date_idx + 30]
                future_day_data = df[df['date'] == future_day]
                if not future_day_data.empty:
                    exit_price = future_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})

    return returns

def backtest_single_etf_with_pe_filter(file_path, pe_data, etf_mapping):
    """
    对单个ETF进行回测，增加市盈率分位数筛选

    Args:
        file_path: ETF数据文件路径
        pe_data: 市盈率分位数数据
        etf_mapping: ETF映射关系

    Returns:
        回测结果字典
    """
    # 提取ETF代码
    etf_code = os.path.basename(file_path).split('分钟级数据')[0]
    print(f"正在回测 {etf_code}...")

    # 加载数据
    df = load_etf_data(file_path)
    if df is None or df.empty:
        print(f"无法加载 {etf_code} 的数据")
        return {}

    # 回测结果
    results = {}
    detailed_signals_list = []

    # 按日期分组处理
    all_dates = sorted(df['date'].unique())

    # 从第91个交易日开始回测(确保有90天历史数据)
    for date_idx, current_date in enumerate(all_dates):
        if date_idx < 90:
            continue

        # 获取前90个交易日
        prev_days = all_dates[date_idx-90:date_idx]

        # 当日数据
        current_day_data = df[df['date'] == current_date]

        # 前90天数据
        prev_days_data = df[df['date'].isin(prev_days)]

        # 对当日每个5分钟K线进行回测
        for i, row in current_day_data.iterrows():
            current_turnover = row['turnover']

            # 跳过NaN值
            if np.isnan(current_turnover):
                continue

            # 获取当前5分钟K线的起始时间
            current_kline_time = row['time']

            # 剔除开盘后两个5分钟周期和收盘前两个5分钟周期的数据
            time_9_30 = pd.Timestamp('09:30:00').time()
            time_9_35 = pd.Timestamp('09:35:00').time()
            time_14_50 = pd.Timestamp('14:50:00').time()
            time_14_55 = pd.Timestamp('14:55:00').time()

            if (current_kline_time == time_9_30 or current_kline_time == time_9_35) or \
               (current_kline_time == time_14_50 or current_kline_time == time_14_55):
                continue

            # 计算成交额分位数
            percentile = calculate_percentile(current_turnover, prev_days_data['turnover'].values)

            # 跳过NaN分位数
            if np.isnan(percentile):
                continue

            # 获取市盈率分位数进行筛选
            current_datetime = pd.to_datetime(f"{current_date} {current_kline_time}")
            pe_percentile = get_pe_percentile(etf_code, current_datetime, pe_data, etf_mapping)

            # 市盈率分位数筛选：如果前一日对应指数市盈率分位数大于等于80分位，则信号无效
            pe_filter_passed = True
            if not np.isnan(pe_percentile) and pe_percentile >= 80:
                pe_filter_passed = False

            # 根据不同分位数阈值生成买入信号
            for threshold in PERCENTILE_THRESHOLDS:
                if percentile > threshold:
                    threshold_key = f"大于{threshold}分位"

                    # 初始化结果字典
                    if threshold_key not in results:
                        results[threshold_key] = {period: [] for period in HOLDING_PERIODS.keys()}

                    # 对不同持有周期计算收益（只有通过PE筛选的信号才计算收益）
                    if pe_filter_passed:
                        for period_name, period_value in HOLDING_PERIODS.items():
                            trade_details_list = calculate_returns(df, [i], period_value)
                            if trade_details_list:
                                for trade_detail in trade_details_list:
                                    signal_record = {
                                        'etf_code': etf_code,
                                        'datetime': df.loc[i, 'datetime'],
                                        'turnover': current_turnover,
                                        'percentile': percentile,
                                        'pe_percentile': pe_percentile,
                                        'pe_filter_passed': pe_filter_passed,
                                        'threshold': int(threshold_key.split('大于')[1].split('分位')[0]),
                                        'holding_period': period_name,
                                        'entry_price': trade_detail['entry_price'],
                                        'exit_price': trade_detail['exit_price'],
                                        'return': trade_detail['return_value']
                                    }
                                    detailed_signals_list.append(signal_record)
                                    results[threshold_key][period_name].append(trade_detail['return_value'])
                    else:
                        # 记录被PE筛选过滤掉的信号
                        signal_record = {
                            'etf_code': etf_code,
                            'datetime': df.loc[i, 'datetime'],
                            'turnover': current_turnover,
                            'percentile': percentile,
                            'pe_percentile': pe_percentile,
                            'pe_filter_passed': pe_filter_passed,
                            'threshold': threshold,
                            'holding_period': 'filtered_out',
                            'entry_price': np.nan,
                            'exit_price': np.nan,
                            'return': np.nan
                        }
                        detailed_signals_list.append(signal_record)

    # 计算每个分位数阈值和持有周期的统计结果
    final_results = {}
    for threshold_key, period_results in results.items():
        final_results[threshold_key] = {}
        for period_name, returns in period_results.items():
            if returns:
                win_rate = np.sum(np.array(returns) > 0) / len(returns) * 100
                avg_return = np.mean(returns) * 100

                # 计算年化收益率
                annualized_return_pct = np.nan
                if returns:
                    current_threshold_numeric = int(threshold_key.split('大于')[1].split('分位')[0])

                    strategy_signals_for_period = [
                        s for s in detailed_signals_list
                        if s['threshold'] == current_threshold_numeric and s['holding_period'] == period_name and s['pe_filter_passed']
                    ]

                    if strategy_signals_for_period:
                        signal_datetimes = [s['datetime'] for s in strategy_signals_for_period]
                        unique_signal_days = 0
                        if signal_datetimes:
                            unique_signal_days = pd.to_datetime(pd.Series(signal_datetimes)).dt.date.nunique()

                        total_days_in_etf_data = df['date'].nunique()

                        if unique_signal_days > 0 and total_days_in_etf_data > 0:
                            avg_return_decimal = avg_return / 100
                            triggers_per_year_estimate = (unique_signal_days / total_days_in_etf_data) * 252

                            if (1 + avg_return_decimal) > 0:
                                annualized_return_value = ((1 + avg_return_decimal) ** triggers_per_year_estimate) - 1
                                annualized_return_pct = annualized_return_value * 100
                            else:
                                annualized_return_pct = -100.0

                final_results[threshold_key][period_name] = {
                    'count': len(returns),
                    'win_rate': win_rate,
                    'avg_return': avg_return,
                    'annualized_return': annualized_return_pct
                }
            else:
                final_results[threshold_key][period_name] = {
                    'count': 0,
                    'win_rate': np.nan,
                    'avg_return': np.nan,
                    'annualized_return': np.nan
                }

    return {etf_code: {'summary_stats': final_results, 'detailed_signals': detailed_signals_list}}

def run_backtest_with_pe_filter():
    """
    运行所有ETF的回测，增加市盈率分位数筛选
    """
    start_time = time.time()

    # 加载市盈率数据和ETF映射关系
    pe_data, etf_mapping = load_pe_data()
    if pe_data is None or etf_mapping is None:
        print("无法加载市盈率数据或ETF映射关系，退出回测")
        return

    # 获取所有ETF数据文件
    etf_files = [os.path.join(DATA_DIR, f) for f in os.listdir(DATA_DIR) if f.endswith('.xlsx')]

    print(f"找到 {len(etf_files)} 个ETF数据文件")

    # 单进程回测（避免多进程传递大数据的问题）
    all_results = {}
    for file_path in etf_files:
        result = backtest_single_etf_with_pe_filter(file_path, pe_data, etf_mapping)
        all_results.update(result)

    # 准备Excel写入器和数据收集
    output_excel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '回测结果_5min_PE筛选.xlsx')
    output_md_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '回测分析报告_5min_PE筛选.md')
    excel_writer = pd.ExcelWriter(output_excel_path, engine='xlsxwriter')

    all_detailed_signals_list = []
    summary_stats_list = []
    etf_summary_stats_list = []

    for etf_code, results_data in all_results.items():
        etf_summary = results_data['summary_stats']
        etf_detailed_signals = results_data['detailed_signals']
        all_detailed_signals_list.extend(etf_detailed_signals)

        for threshold, period_results in etf_summary.items():
            for period, stats in period_results.items():
                if stats['count'] > 0:
                    summary_stats_list.append({
                        'etf_code': etf_code,
                        'threshold': threshold,
                        'period': period,
                        'count': stats['count'],
                        'win_rate': stats['win_rate'],
                        'avg_return': stats['avg_return'],
                        'annualized_return': stats.get('annualized_return', np.nan)
                    })
                    etf_summary_stats_list.append({
                        'etf_code': etf_code,
                        'threshold': threshold,
                        'period': period,
                        'count': stats['count'],
                        'win_rate': stats['win_rate'],
                        'avg_return': stats['avg_return'],
                        'annualized_return': stats.get('annualized_return', np.nan)
                    })

    # 1. 所有信号明细 (保存为CSV)
    output_csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '所有信号明细_5min_PE筛选.csv')
    if all_detailed_signals_list:
        detailed_signals_df = pd.DataFrame(all_detailed_signals_list)
        detailed_signals_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
        print(f"\n所有信号明细已保存至: {output_csv_path}")

        # 统计PE筛选效果
        total_signals = len(detailed_signals_df)
        passed_signals = len(detailed_signals_df[detailed_signals_df['pe_filter_passed'] == True])
        filtered_signals = len(detailed_signals_df[detailed_signals_df['pe_filter_passed'] == False])

        print(f"PE筛选统计:")
        print(f"  总信号数: {total_signals}")
        print(f"  通过PE筛选: {passed_signals} ({passed_signals/total_signals*100:.2f}%)")
        print(f"  被PE筛选过滤: {filtered_signals} ({filtered_signals/total_signals*100:.2f}%)")
    else:
        with open(output_csv_path, 'w', encoding='utf-8-sig') as f:
            f.write('')
        print(f"\n没有详细信号生成，已创建空的CSV文件: {output_csv_path}")

    # 2. 各ETF详细统计
    if etf_summary_stats_list:
        etf_summary_df = pd.DataFrame(etf_summary_stats_list)
        etf_summary_df.to_excel(excel_writer, sheet_name='各ETF详细统计', index=False)
    else:
        pd.DataFrame().to_excel(excel_writer, sheet_name='各ETF详细统计', index=False)

    # 3. 汇总统计结果
    overall_summary_list = []
    if summary_stats_list:
        summary_df_for_overall_calc = pd.DataFrame(summary_stats_list)
        for (threshold, period), group in summary_df_for_overall_calc.groupby(['threshold', 'period']):
            if not group.empty:
                total_count = group['count'].sum()
                weighted_win_rate = np.average(group['win_rate'], weights=group['count']) if total_count > 0 else np.nan
                weighted_avg_return = np.average(group['avg_return'], weights=group['count']) if total_count > 0 else np.nan

                valid_annualized_returns = group.dropna(subset=['annualized_return'])
                weighted_annualized_return = np.nan
                if not valid_annualized_returns.empty and valid_annualized_returns['count'].sum() > 0:
                    weighted_annualized_return = np.average(valid_annualized_returns['annualized_return'],
                                                            weights=valid_annualized_returns['count'])

                overall_summary_list.append({
                    'threshold': threshold,
                    'period': period,
                    'total_signals': total_count,
                    'avg_win_rate': weighted_win_rate,
                    'avg_return': weighted_avg_return,
                    'annualized_return': weighted_annualized_return
                })

    if overall_summary_list:
        overall_summary_df = pd.DataFrame(overall_summary_list)
        overall_summary_df = overall_summary_df.sort_values(by=['threshold', 'period'])
        overall_summary_df.to_excel(excel_writer, sheet_name='汇总统计结果', index=False)
    else:
        pd.DataFrame().to_excel(excel_writer, sheet_name='汇总统计结果', index=False)

    excel_writer.close()
    print(f"\n回测结果已保存至: {output_excel_path}")

    # 生成Markdown分析报告
    with open(output_md_path, 'w', encoding='utf-8') as f:
        f.write("# ETF成交额分位数回测分析报告 (5分钟级别 + PE筛选)\n\n")
        f.write(f"**回测执行时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}  \n")
        f.write(f"**数据文件夹**: {DATA_DIR}  \n")
        f.write(f"**参与回测的ETF文件数量**: {len(etf_files)}  \n")
        f.write(f"**PE筛选规则**: 前一日对应指数市盈率分位数 >= 80分位时信号无效  \n\n")

        if all_detailed_signals_list:
            detailed_signals_df = pd.DataFrame(all_detailed_signals_list)
            total_signals = len(detailed_signals_df)
            passed_signals = len(detailed_signals_df[detailed_signals_df['pe_filter_passed'] == True])
            filtered_signals = len(detailed_signals_df[detailed_signals_df['pe_filter_passed'] == False])

            f.write("## PE筛选效果统计\n\n")
            f.write(f"- **总信号数**: {total_signals}\n")
            f.write(f"- **通过PE筛选**: {passed_signals} ({passed_signals/total_signals*100:.2f}%)\n")
            f.write(f"- **被PE筛选过滤**: {filtered_signals} ({filtered_signals/total_signals*100:.2f}%)\n\n")

        f.write("## 汇总统计结果\n\n")
        if overall_summary_list:
            overall_report_df = pd.DataFrame(overall_summary_list)
            overall_report_df = overall_report_df.sort_values(by=['threshold', 'period'])

            f.write("| 阈值 | 持有周期 | 总信号数 | 平均胜率 | 平均收益率 | 年化收益率 |\n")
            f.write("| ---- | -------- | -------- | -------- | ---------- | ------------ |\n")

            for _, row in overall_report_df.iterrows():
                avg_win_rate_str = format_percentage(row['avg_win_rate'])
                avg_return_str = format_percentage(row['avg_return'], 4)
                annualized_return_str = format_percentage(row.get('annualized_return', np.nan))
                f.write(f"| {row['threshold']} | {row['period']} | {row['total_signals']} | {avg_win_rate_str} | {avg_return_str} | {annualized_return_str} |\n")
            f.write("\n")
        else:
            f.write("*无汇总统计结果可显示。*\n\n")

        f.write("## 说明\n\n")
        f.write("本回测在原有成交额分位数策略基础上，增加了市盈率分位数筛选：\n")
        f.write("- 当ETF对应指数前一日市盈率分位数 >= 80分位时，该信号被过滤掉\n")
        f.write("- 只有通过PE筛选的信号才会进入实际的收益计算\n")
        f.write("- 详细的信号数据（包括被过滤的信号）保存在CSV文件中\n\n")
        f.write("*报告生成完毕。*\n")

    print(f"分析报告已保存至: {output_md_path}")

    end_time = time.time()
    print(f"\n回测完成，耗时: {end_time - start_time:.2f}秒")

if __name__ == "__main__":
    if check_required_modules():
        run_backtest_with_pe_filter()
    else:
        print("由于缺少必要模块，回测无法执行。请安装所需模块后重试。")
