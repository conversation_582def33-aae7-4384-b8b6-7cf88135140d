#!/usr/bin/env python3
"""
验证和显示实际交易明细
"""

import pandas as pd

def verify_actual_trades():
    """验证实际交易明细"""
    
    print("=== 验证实际交易明细 ===")
    
    try:
        # 读取生成的实际交易明细
        excel_file = pd.ExcelFile('ETF实际交易明细表_仅实际交易.xlsx')
        print(f"Excel文件工作表: {excel_file.sheet_names}")
        
        # 读取各工作表
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel('ETF实际交易明细表_仅实际交易.xlsx', sheet_name=sheet_name)
            print(f"\n{sheet_name}:")
            print(f"  记录数: {len(df)}")
            if not df.empty:
                print(f"  列名: {df.columns.tolist()}")
                print(f"  前3行:")
                print(df.head(3))
        
        # 读取原始超额收益结果进行对比
        print(f"\n=== 对比原始超额收益结果 ===")
        
        # 90分位信号结果
        try:
            turnover_excel = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
            print(f"\n90分位成交额信号原始结果:")
            print(f"  总交易次数: {turnover_excel['交易次数'].sum()}")
            print(f"  有交易的ETF数量: {len(turnover_excel[turnover_excel['交易次数'] > 0])}")
            
            # 显示各ETF的交易次数
            trades_summary = turnover_excel[turnover_excel['交易次数'] > 0][['ETF代码', '交易次数', '胜率(%)', '超额收益率(%)']]
            print(f"  各ETF交易次数:")
            for _, row in trades_summary.iterrows():
                print(f"    {row['ETF代码']}: {row['交易次数']}次, 胜率{row['胜率(%)']}%, 超额收益{row['超额收益率(%)']}%")
                
        except Exception as e:
            print(f"读取90分位信号结果时出错: {e}")
        
        # 申赎信号结果
        try:
            redemption_excel = pd.read_excel('超额收益_申赎信号/ETF申赎信号择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
            print(f"\n申赎信号原始结果:")
            print(f"  总交易次数: {redemption_excel['交易次数'].sum()}")
            print(f"  有交易的ETF数量: {len(redemption_excel[redemption_excel['交易次数'] > 0])}")
            
            # 显示各ETF的交易次数
            trades_summary = redemption_excel[redemption_excel['交易次数'] > 0][['ETF代码', '交易次数', '胜率(%)', '超额收益率(%)']]
            print(f"  各ETF交易次数:")
            for _, row in trades_summary.iterrows():
                print(f"    {row['ETF代码']}: {row['交易次数']}次, 胜率{row['胜率(%)']}%, 超额收益{row['超额收益率(%)']}%")
                
        except Exception as e:
            print(f"读取申赎信号结果时出错: {e}")
        
    except Exception as e:
        print(f"验证时出错: {e}")
        import traceback
        traceback.print_exc()

def create_detailed_actual_trades():
    """创建详细的实际交易明细"""
    
    print(f"\n=== 重新生成详细实际交易明细 ===")
    
    try:
        # 读取90分位信号结果
        turnover_excel = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        
        # 读取申赎信号结果
        redemption_excel = pd.read_excel('超额收益_申赎信号/ETF申赎信号择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        
        # 筛选有实际交易的ETF
        turnover_with_trades = turnover_excel[turnover_excel['交易次数'] > 0].copy()
        redemption_with_trades = redemption_excel[redemption_excel['交易次数'] > 0].copy()
        
        print(f"90分位信号有交易的ETF: {len(turnover_with_trades)}个")
        print(f"申赎信号有交易的ETF: {len(redemption_with_trades)}个")
        
        # 创建简化的实际交易记录
        actual_trades_90 = []
        for _, row in turnover_with_trades.iterrows():
            etf_code = row['ETF代码']
            trade_count = row['交易次数']
            win_rate = row['胜率(%)']
            total_return = row['择时累计收益率(%)']
            excess_return = row['超额收益率(%)']
            
            # 为每次交易创建记录（简化处理）
            avg_return_per_trade = total_return / trade_count if trade_count > 0 else 0
            
            for i in range(int(trade_count)):
                actual_trades_90.append({
                    'ETF代码': etf_code,
                    '跟踪指数': row['跟踪指数'],
                    '交易序号': i + 1,
                    '信号类型': '90分位成交额信号',
                    '平均每次收益率(%)': round(avg_return_per_trade, 4),
                    '总交易次数': trade_count,
                    '胜率(%)': win_rate,
                    '累计收益率(%)': total_return,
                    '超额收益率(%)': excess_return
                })
        
        actual_trades_redemption = []
        for _, row in redemption_with_trades.iterrows():
            etf_code = row['ETF代码']
            trade_count = row['交易次数']
            win_rate = row['胜率(%)']
            total_return = row['择时累计收益率(%)']
            excess_return = row['超额收益率(%)']
            
            # 为每次交易创建记录（简化处理）
            avg_return_per_trade = total_return / trade_count if trade_count > 0 else 0
            
            for i in range(int(trade_count)):
                actual_trades_redemption.append({
                    'ETF代码': etf_code,
                    '跟踪指数': row['跟踪指数'],
                    '交易序号': i + 1,
                    '信号类型': '申赎信号',
                    '平均每次收益率(%)': round(avg_return_per_trade, 4),
                    '总交易次数': trade_count,
                    '胜率(%)': win_rate,
                    '累计收益率(%)': total_return,
                    '超额收益率(%)': excess_return
                })
        
        # 转换为DataFrame
        df_90 = pd.DataFrame(actual_trades_90)
        df_redemption = pd.DataFrame(actual_trades_redemption)
        
        # 保存到新的Excel文件
        excel_path = 'ETF实际交易明细表_最终版.xlsx'
        with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
            
            if not df_90.empty:
                df_90.to_excel(writer, sheet_name='90分位信号实际交易', index=False)
            
            if not df_redemption.empty:
                df_redemption.to_excel(writer, sheet_name='申赎信号实际交易', index=False)
            
            # 创建汇总对比表
            summary_data = []
            
            if not df_90.empty:
                summary_data.append({
                    '信号类型': '90分位成交额信号',
                    '实际交易总次数': len(df_90),
                    '涉及ETF数量': df_90['ETF代码'].nunique(),
                    '平均胜率(%)': df_90.groupby('ETF代码')['胜率(%)'].first().mean(),
                    '平均超额收益率(%)': df_90.groupby('ETF代码')['超额收益率(%)'].first().mean()
                })
            
            if not df_redemption.empty:
                summary_data.append({
                    '信号类型': '申赎信号',
                    '实际交易总次数': len(df_redemption),
                    '涉及ETF数量': df_redemption['ETF代码'].nunique(),
                    '平均胜率(%)': df_redemption.groupby('ETF代码')['胜率(%)'].first().mean(),
                    '平均超额收益率(%)': df_redemption.groupby('ETF代码')['超额收益率(%)'].first().mean()
                })
            
            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总对比', index=False)
        
        print(f"\n最终版Excel文件已保存: {excel_path}")
        
        # 显示统计
        if not df_90.empty:
            print(f"\n90分位成交额信号实际交易:")
            print(f"  总交易次数: {len(df_90)}")
            print(f"  涉及ETF数量: {df_90['ETF代码'].nunique()}")
            etf_counts = df_90['ETF代码'].value_counts().sort_index()
            for etf, count in etf_counts.items():
                print(f"    {etf}: {count}次")
        
        if not df_redemption.empty:
            print(f"\n申赎信号实际交易:")
            print(f"  总交易次数: {len(df_redemption)}")
            print(f"  涉及ETF数量: {df_redemption['ETF代码'].nunique()}")
            etf_counts = df_redemption['ETF代码'].value_counts().sort_index()
            for etf, count in etf_counts.items():
                print(f"    {etf}: {count}次")
        
    except Exception as e:
        print(f"创建详细实际交易明细时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_actual_trades()
    create_detailed_actual_trades()
