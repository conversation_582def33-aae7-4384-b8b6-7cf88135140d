# -*- coding: utf-8 -*-
"""
重新计算年化收益率 V2 - 使用更合理的计算方法
基于实际交易频率和持有周期进行年化计算
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_and_process_signals():
    """加载并处理信号数据"""
    signals_path = '回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv'
    signals_df = pd.read_csv(signals_path)
    signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
    signals_df['date'] = signals_df['datetime'].dt.date
    
    # 筛选有效信号
    valid_signals = signals_df[
        (signals_df['pe_filter_passed'] == True) & 
        (signals_df['return'].notna())
    ].copy()
    
    # 按日期、ETF、阈值、持有周期分组，每组只保留最早的信号
    merged_signals = valid_signals.groupby([
        'etf_code', 'date', 'threshold', 'holding_period'
    ]).first().reset_index()
    
    print(f"原始信号数: {len(signals_df)}")
    print(f"有效信号数: {len(valid_signals)}")
    print(f"合并后信号数: {len(merged_signals)}")
    
    return merged_signals

def calculate_realistic_annualized_returns(merged_signals):
    """
    计算更现实的年化收益率
    方法：基于实际的交易频率和持有周期
    """
    results = []
    
    # 计算数据覆盖的总天数
    all_dates = sorted(merged_signals['date'].unique())
    total_days = len(all_dates)
    start_date = all_dates[0]
    end_date = all_dates[-1]
    
    print(f"数据覆盖期间: {start_date} 到 {end_date}")
    print(f"总交易日数: {total_days}")
    
    for (threshold, period), group in merged_signals.groupby(['threshold', 'holding_period']):
        if len(group) == 0:
            continue
            
        threshold_key = f'大于{threshold}分位'
        
        # 基本统计
        returns = group['return'].values
        total_signals = len(returns)
        win_rate = np.sum(returns > 0) / len(returns) * 100
        avg_return = np.mean(returns) * 100
        median_return = np.median(returns) * 100
        
        # 计算年化收益率的新方法
        annualized_return = calculate_annualized_return_v2(
            group, avg_return, period, total_days
        )
        
        results.append({
            'threshold': threshold_key,
            'period': period,
            'total_signals': total_signals,
            'avg_win_rate': win_rate,
            'avg_return': avg_return,
            'median_return': median_return,
            'annualized_return': annualized_return
        })
    
    return pd.DataFrame(results)

def calculate_annualized_return_v2(group, avg_return_pct, period, total_days):
    """
    计算年化收益率的新方法
    考虑实际的交易频率和持有周期
    """
    try:
        # 1. 计算平均交易频率（每年交易次数）
        unique_signal_days = group['date'].nunique()
        etf_count = group['etf_code'].nunique()
        
        # 每个ETF平均每年的交易次数
        trades_per_etf_per_year = (unique_signal_days / etf_count) * (252 / total_days)
        
        # 2. 根据持有周期调整年化计算
        if period.startswith('next_') and period.endswith('day_close'):
            # 提取持有天数
            if period == 'next_day_close':
                holding_days = 1
            elif period == 'next_2day_close':
                holding_days = 2
            elif period == 'next_3day_close':
                holding_days = 3
            elif period == 'next_5day_close':
                holding_days = 5
            elif period == 'next_10day_close':
                holding_days = 10
            elif period == 'next_15day_close':
                holding_days = 15
            elif period == 'next_30day_close':
                holding_days = 30
            else:
                holding_days = 1
        elif period.endswith('min'):
            # 分钟级持有，按当日计算
            holding_days = 1
        else:
            holding_days = 1
        
        # 3. 计算年化收益率
        if trades_per_etf_per_year > 0 and avg_return_pct/100 > -1:
            # 方法1：基于交易频率的复利计算（适用于低频交易）
            if trades_per_etf_per_year <= 20:
                annual_return = ((1 + avg_return_pct/100) ** trades_per_etf_per_year) - 1
                annualized_return = annual_return * 100
            else:
                # 方法2：基于持有期收益率的年化（适用于高频交易）
                periods_per_year = 252 / holding_days
                if periods_per_year > 0:
                    annual_return = ((1 + avg_return_pct/100) ** periods_per_year) - 1
                    annualized_return = annual_return * 100
                else:
                    annualized_return = avg_return_pct
            
            # 限制极端值
            if annualized_return > 10000:  # 限制最大年化收益率为10000%
                annualized_return = avg_return_pct * min(trades_per_etf_per_year, 252/holding_days)
                
        else:
            annualized_return = 0.0
            
        return annualized_return
        
    except Exception as e:
        print(f"计算年化收益率时出错: {e}")
        return 0.0

def generate_comparison_report(new_results_df):
    """生成对比报告"""
    
    # 读取原始结果
    try:
        original_path = '回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/回测结果_5min_PE筛选.xlsx'
        original_df = pd.read_excel(original_path, sheet_name='汇总统计结果')
        has_original = True
    except:
        print("无法读取原始结果文件")
        has_original = False
        original_df = None
    
    # 创建输出目录
    output_dir = '年化收益率修正结果'
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存新结果
    excel_path = os.path.join(output_dir, '修正后的汇总统计结果.xlsx')
    
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        new_results_df.to_excel(writer, sheet_name='修正后汇总统计', index=False)
        
        if has_original:
            # 创建对比表
            comparison_data = []
            for _, new_row in new_results_df.iterrows():
                threshold = new_row['threshold']
                period = new_row['period']
                
                original_row = original_df[
                    (original_df['threshold'] == threshold) & 
                    (original_df['period'] == period)
                ]
                
                if not original_row.empty:
                    orig = original_row.iloc[0]
                    comparison_data.append({
                        'threshold': threshold,
                        'period': period,
                        'original_signals': orig.get('total_signals', 0),
                        'new_signals': new_row['total_signals'],
                        'signal_reduction': orig.get('total_signals', 0) - new_row['total_signals'],
                        'original_annualized': orig.get('annualized_return', 0),
                        'new_annualized': new_row['annualized_return'],
                        'annualized_change': new_row['annualized_return'] - orig.get('annualized_return', 0)
                    })
            
            if comparison_data:
                comparison_df = pd.DataFrame(comparison_data)
                comparison_df.to_excel(writer, sheet_name='新旧对比', index=False)
    
    print(f"修正结果已保存: {excel_path}")
    
    # 生成Markdown报告
    md_content = f"""# 年化收益率修正报告

## 修正说明

### 原始问题
1. 同一天多个信号被重复计算
2. 年化收益率计算方法不合理，导致数值过高

### 修正方法
1. **信号去重**: 每日每个ETF每个策略组合最多保留一个信号
2. **年化计算优化**: 
   - 低频交易(≤20次/年): 使用复利公式
   - 高频交易(>20次/年): 基于持有期年化
   - 设置合理的上限，避免极端值

## 修正后的主要结果

| 阈值 | 持有周期 | 信号数 | 胜率(%) | 平均收益率(%) | 年化收益率(%) |
|------|----------|--------|---------|---------------|---------------|
"""
    
    # 添加主要结果
    main_periods = ['next_day_close', 'next_5day_close', 'next_10day_close', 'next_30day_close']
    for _, row in new_results_df.iterrows():
        if row['period'] in main_periods:
            md_content += f"| {row['threshold']} | {row['period']} | {row['total_signals']} | {row['avg_win_rate']:.2f} | {row['avg_return']:.4f} | {row['annualized_return']:.2f} |\n"
    
    md_content += f"""

## 关键改进

1. **信号数量**: 从303,396个压缩到36,552个，避免重复计算
2. **年化收益率**: 使用更合理的计算方法，结果更贴近实际
3. **数据质量**: 提高了策略评估的准确性

## 计算逻辑

```python
# 交易频率计算
trades_per_etf_per_year = (unique_signal_days / etf_count) * (252 / total_days)

# 年化收益率计算
if trades_per_etf_per_year <= 20:
    # 低频交易：复利计算
    annual_return = ((1 + avg_return/100) ** trades_per_etf_per_year) - 1
else:
    # 高频交易：基于持有期年化
    periods_per_year = 252 / holding_days
    annual_return = ((1 + avg_return/100) ** periods_per_year) - 1
```

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    md_path = os.path.join(output_dir, '年化收益率修正报告.md')
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(md_content)
    
    print(f"Markdown报告已保存: {md_path}")

def main():
    """主函数"""
    print("=== 年化收益率修正计算 V2 ===")
    
    # 1. 加载和处理数据
    merged_signals = load_and_process_signals()
    
    # 2. 重新计算年化收益率
    new_results_df = calculate_realistic_annualized_returns(merged_signals)
    
    # 3. 生成报告
    generate_comparison_report(new_results_df)
    
    # 4. 显示关键结果
    print("\n=== 修正后的主要结果 ===")
    main_periods = ['next_day_close', 'next_5day_close', 'next_10day_close', 'next_30day_close']
    
    for period in main_periods:
        print(f"\n{period}:")
        period_data = new_results_df[new_results_df['period'] == period]
        for _, row in period_data.iterrows():
            print(f"  {row['threshold']}: 信号{row['total_signals']}个, 胜率{row['avg_win_rate']:.2f}%, 年化{row['annualized_return']:.2f}%")
    
    print("\n✅ 年化收益率修正完成！")

if __name__ == "__main__":
    main()
