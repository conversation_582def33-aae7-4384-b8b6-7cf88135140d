#!/usr/bin/env python3
import pandas as pd

print("=== 测试数据读取 ===")

try:
    # 测试ETF映射数据
    print("1. 测试ETF映射数据...")
    etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
    print(f"ETF映射数据: {len(etf_mapping)} 行")
    print("列名:", etf_mapping.columns.tolist())
    print("前3行:")
    print(etf_mapping.head(3))
    
    # 测试指数行情数据
    print("\n2. 测试指数行情数据...")
    index_data = pd.read_excel('指数行情序列.xlsx')
    print(f"指数行情数据: {len(index_data)} 行")
    print("列名:", index_data.columns.tolist())
    print("前3行:")
    print(index_data.head(3))
    
    # 测试90分位信号数据
    print("\n3. 测试90分位信号数据...")
    signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
    print(f"90分位信号数据: {len(signals_df)} 行")
    print("列名:", signals_df.columns.tolist())
    
    # 筛选90分位且通过PE筛选的信号
    filtered_signals = signals_df[
        (signals_df['pe_filter_passed'] == True) &
        (signals_df['threshold'] == 90) &
        (signals_df['holding_period'] == 'next_15day_close')
    ]
    print(f"筛选后信号数: {len(filtered_signals)}")
    print(f"涉及ETF: {sorted(filtered_signals['etf_code'].unique())}")
    
    # 测试申赎信号数据
    print("\n4. 测试申赎信号数据...")
    redemption_df = pd.read_csv('分析结果_PE筛选_20240430_20250430/PE筛选后信号数据汇总表.csv')
    print(f"申赎信号数据: {len(redemption_df)} 行")
    print("列名:", redemption_df.columns.tolist())
    
    # 筛选申赎信号
    redemption_signals = redemption_df[redemption_df['PE筛选后信号1_买入'] == 1]
    print(f"筛选后申赎信号数: {len(redemption_signals)}")
    if not redemption_signals.empty:
        etf_codes = redemption_signals['ETF代码'].str.split('.').str[0].astype(int).unique()
        print(f"涉及ETF: {sorted(etf_codes)}")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
