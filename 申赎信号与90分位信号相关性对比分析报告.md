# 申赎信号与90分位信号相关性对比分析报告

## 报告概述

**分析时间**: 2025-06-12 13:37:49  
**对比目的**: 比较申赎信号与90分位成交额信号在跟踪相同指数ETF间的相关性差异  
**分析方法**: Jaccard相似度系数对比

## 数据源对比

### 申赎信号（95分位买入，PE筛选）
- **数据源**: `分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv`
- **信号特征**: 基于申赎净额95分位阈值
- **筛选条件**: PE筛选通过

### 90分位成交额信号（PE筛选）
- **数据源**: `回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv`
- **信号特征**: 基于5分钟成交额90分位阈值
- **筛选条件**: PE筛选通过且threshold=90

## 申赎信号分析结果

### 基础统计
- **总ETF数量**: 22个
- **有申赎信号ETF**: 22个 (100%)
- **可分析相关性ETF对**: 20对
- **平均Jaccard相似度**: 0.2863

### 申赎信号相关性强度分类

- **强相关 (Jaccard≥0.6)**: 1对 (5.0%)
- **中等相关 (0.3≤Jaccard<0.6)**: 9对 (45.0%)
- **弱相关 (Jaccard<0.3)**: 10对 (50.0%)

### 申赎信号各指数相关性详细结果

| 指数代码 | ETF1 | ETF2 | 申赎信号重叠数 | Jaccard相似度 | 相关性等级 |
|---------|------|------|---------------|-------------|-----------|
| 300 | 159919 | 510330 | 7 | 0.6364 | 强相关 |
| 300 | 159919 | 510310 | 5 | 0.5000 | 中等相关 |
| 852 | 512100 | 159845 | 7 | 0.4667 | 中等相关 |
| 300 | 510300 | 510310 | 6 | 0.4615 | 中等相关 |
| 300 | 510310 | 510330 | 6 | 0.4615 | 中等相关 |
| 852 | 159629 | 159845 | 6 | 0.4286 | 中等相关 |
| 300 | 159919 | 510300 | 5 | 0.3846 | 中等相关 |
| 852 | 159629 | 512100 | 5 | 0.3333 | 中等相关 |
| 852 | 560010 | 159845 | 4 | 0.3077 | 中等相关 |
| 852 | 560010 | 512100 | 4 | 0.3077 | 中等相关 |
| 300 | 510300 | 510330 | 5 | 0.2941 | 弱相关 |
| 16 | 510100 | 510050 | 2 | 0.2857 | 弱相关 |
| 852 | 560010 | 159629 | 3 | 0.2500 | 弱相关 |
| 905 | 510500 | 512500 | 1 | 0.1667 | 弱相关 |
| 905 | 510500 | 159922 | 1 | 0.1429 | 弱相关 |
| 399006 | 159952 | 159977 | 3 | 0.1111 | 弱相关 |
| 399006 | 159915 | 159952 | 3 | 0.1034 | 弱相关 |
| 399006 | 159915 | 159977 | 2 | 0.0833 | 弱相关 |
| 688 | 588050 | 588080 | 0 | 0.0000 | 弱相关 |
| 905 | 159922 | 512500 | 0 | 0.0000 | 弱相关 |


## 90分位成交额信号分析结果（对比）

### 基础统计
- **可分析相关性ETF对**: 19对
- **平均Jaccard相似度**: 0.7480

### 90分位信号相关性强度分类

- **强相关 (Jaccard≥0.6)**: 18对 (94.7%)
- **中等相关 (0.3≤Jaccard<0.6)**: 1对 (5.3%)
- **弱相关 (Jaccard<0.3)**: 0对 (0.0%)

## 对比分析结果

### 相关性强度对比

| 指标 | 申赎信号 | 90分位信号 | 差异 |
|------|---------|-----------|------|
| 平均Jaccard相似度 | 0.2863 | 0.7480 | 0.4617 |
| 强相关ETF对比例 | 5.0% | 94.7% | 89.7% |
| 中等相关ETF对比例 | 45.0% | 5.3% | -39.7% |
| 弱相关ETF对比例 | 50.0% | 0.0% | -50.0% |

### 各指数相关性对比

| 指数代码 | 申赎信号平均相似度 | 90分位信号平均相似度 | 差异 |
|---------|-------------------|-------------------|------|
| 16 | 0.2857 | 0.6923 | 0.4066 |
| 300 | 0.4564 | 0.7071 | 0.2508 |
| 688 | 0.0000 | 0.0000 | 0.0000 |
| 852 | 0.3490 | 0.7805 | 0.4315 |
| 905 | 0.1032 | 0.9111 | 0.8079 |
| 399006 | 0.0993 | 0.6200 | 0.5207 |


## 分析结论

### 1. 申赎信号相关性特征

**整体评估**: 申赎信号相关性总体较弱

- **平均相关性**: 0.2863 (弱到中等相关)
- **相关性分布**: 50.0%弱相关，45.0%中等相关，5.0%强相关
- **信号独立性**: 大部分ETF对的申赎信号相对独立

### 2. 最相关的申赎信号ETF对


#### 第1名: ETF 159919 vs ETF 510330 (指数300)
- **Jaccard相似度**: 0.6364
- **重叠申赎信号数**: 7个

#### 第2名: ETF 159919 vs ETF 510310 (指数300)
- **Jaccard相似度**: 0.5000
- **重叠申赎信号数**: 5个

#### 第3名: ETF 512100 vs ETF 159845 (指数852)
- **Jaccard相似度**: 0.4667
- **重叠申赎信号数**: 7个

#### 第4名: ETF 510300 vs ETF 510310 (指数300)
- **Jaccard相似度**: 0.4615
- **重叠申赎信号数**: 6个

#### 第5名: ETF 510310 vs ETF 510330 (指数300)
- **Jaccard相似度**: 0.4615
- **重叠申赎信号数**: 6个


### 3. 申赎信号 vs 90分位信号对比结论

#### 相关性强度对比
- **申赎信号**: 平均相关性0.2863，以弱相关为主
- **90分位信号**: 平均相关性0.7480，以强相关为主
- **差异**: 90分位信号相关性比申赎信号高0.4617

#### 信号特征差异
1. **申赎信号**: 基于资金流向，反映投资者行为，相关性较低
2. **90分位信号**: 基于成交量，反映市场活跃度，相关性较高
3. **驱动因素**: 申赎行为更多样化，成交量更同步化

#### 投资策略含义
1. **申赎信号**: 更适合分散化投资，ETF间独立性强
2. **90分位信号**: 需要避免重复配置，ETF间同质化严重
3. **组合构建**: 申赎信号可支持更多ETF同时持有


### 4. 投资建议

#### 基于申赎信号的策略
1. **多元化配置**: 由于相关性较低，可以配置多个跟踪相同指数的ETF
2. **独立监控**: 每个ETF的申赎信号相对独立，需要分别监控
3. **风险分散**: 申赎信号的低相关性有利于风险分散

#### 信号选择建议
1. **高频交易**: 90分位信号更适合，相关性高便于批量操作
2. **长期投资**: 申赎信号更适合，反映基本面变化
3. **组合管理**: 结合两类信号，申赎信号用于选股，90分位信号用于择时

### 5. 风险提示

#### 申赎信号风险
1. **信号稀疏**: 申赎信号相对较少，可能错过交易机会
2. **滞后性**: 申赎数据可能存在披露滞后
3. **噪音干扰**: 个别大额申赎可能产生误导信号

#### 策略建议
1. **信号确认**: 结合多个指标确认申赎信号
2. **阈值调整**: 根据市场环境调整申赎信号阈值
3. **定期评估**: 定期评估申赎信号的有效性

---

## 技术说明

### 相关性计算方法
- **Jaccard相似度**: |A ∩ B| / |A ∪ B|
- **相关性分类**: 强相关(≥0.6)、中等相关(0.3-0.6)、弱相关(<0.3)

### 数据处理说明
- **时间标准化**: 统一转换为日期格式
- **ETF代码标准化**: 去除后缀，统一格式
- **信号去重**: 每日每个ETF只保留一个信号

---
*报告生成时间: 2025-06-12 13:37:49*
