#!/usr/bin/env python3
"""
简化版ETF交易明细表生成工具
"""

import pandas as pd
from datetime import datetime, timedelta

def calculate_trading_days_ahead(date, days=15):
    """计算交易日后的日期（简化版本）"""
    current_date = date
    trading_days = 0
    
    while trading_days < days:
        current_date += timedelta(days=1)
        if current_date.weekday() < 5:  # 跳过周末
            trading_days += 1
    
    return current_date

def generate_90_percentile_details():
    """生成90分位成交额信号交易明细"""
    print("=== 生成90分位成交额信号交易明细 ===")
    
    try:
        # 读取信号数据
        signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
        signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
        signals_df['date'] = signals_df['datetime'].dt.date
        
        # 筛选90分位且通过PE筛选的信号
        filtered_signals = signals_df[
            (signals_df['pe_filter_passed'] == True) &
            (signals_df['threshold'] == 90) &
            (signals_df['holding_period'] == 'next_15day_close')
        ].copy()
        
        # 分析时间范围
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()
        
        period_signals = filtered_signals[
            (filtered_signals['date'] >= start_date) &
            (filtered_signals['date'] <= end_date)
        ].copy()
        
        # 每日每个ETF只保留一个信号
        daily_signals = period_signals.groupby(['etf_code', 'date']).first().reset_index()
        daily_signals = daily_signals.sort_values(['etf_code', 'date'])
        
        print(f"处理信号数: {len(daily_signals)}")
        
        # 生成交易明细
        trading_details = []
        
        for _, signal in daily_signals.iterrows():
            etf_code = signal['etf_code']
            signal_date = signal['date']
            entry_price = signal['entry_price']
            exit_price = signal['exit_price']
            return_rate = signal['return']
            
            # 计算结束日期
            end_date_calc = calculate_trading_days_ahead(signal_date, 15)
            
            if pd.notna(entry_price) and pd.notna(exit_price) and pd.notna(return_rate):
                trading_details.append({
                    'ETF代码': etf_code,
                    '信号日期': signal_date.strftime('%Y-%m-%d'),
                    '交易结束日期': end_date_calc.strftime('%Y-%m-%d'),
                    '开始价格': round(entry_price, 4),
                    '结束价格': round(exit_price, 4),
                    '期间收益率(%)': round(return_rate * 100, 4),
                    '信号时间': signal['datetime'].strftime('%Y-%m-%d %H:%M:%S'),
                    '成交额': signal.get('turnover', 0)
                })
        
        return pd.DataFrame(trading_details)
        
    except Exception as e:
        print(f"生成90分位交易明细时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def generate_redemption_details():
    """生成申赎信号交易明细"""
    print("\n=== 生成申赎信号交易明细 ===")
    
    try:
        # 读取申赎信号数据
        signals_df = pd.read_csv('分析结果_PE筛选_20240430_20250430/PE筛选后信号数据汇总表.csv')
        signals_df['日期'] = pd.to_datetime(signals_df['日期'])
        
        # 分析时间范围
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()
        
        # 筛选申赎信号
        period_signals = signals_df[
            (signals_df['日期'].dt.date >= start_date) &
            (signals_df['日期'].dt.date <= end_date) &
            (signals_df['PE筛选后信号1_买入'] == 1)
        ].copy()
        
        period_signals['ETF代码_数字'] = period_signals['ETF代码'].str.split('.').str[0].astype(int)
        period_signals = period_signals.sort_values(['ETF代码_数字', '日期'])
        
        print(f"处理申赎信号数: {len(period_signals)}")
        
        # 生成交易明细（简化版本，不计算实际收益率）
        trading_details = []
        
        for _, signal in period_signals.iterrows():
            etf_code = signal['ETF代码_数字']
            signal_date = signal['日期'].date()
            
            # 计算结束日期
            end_date_calc = calculate_trading_days_ahead(signal_date, 15)
            
            trading_details.append({
                'ETF代码': etf_code,
                '信号日期': signal_date.strftime('%Y-%m-%d'),
                '交易结束日期': end_date_calc.strftime('%Y-%m-%d'),
                '申赎净额(万股)': round(signal.get('净申购金额', 0) / 10000, 2),
                '申赎净额占比(%)': round(signal.get('净申购金额分位数', 0), 4),
                '市盈率分位数': round(signal.get('市盈率分位数', 0), 4)
            })
        
        return pd.DataFrame(trading_details)
        
    except Exception as e:
        print(f"生成申赎交易明细时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def main():
    """主函数"""
    print("=== ETF实际交易明细表生成工具（简化版） ===")
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 生成90分位成交额信号交易明细
    turnover_details = generate_90_percentile_details()
    
    # 生成申赎信号交易明细
    redemption_details = generate_redemption_details()
    
    # 保存Excel文件
    if not turnover_details.empty or not redemption_details.empty:
        excel_path = 'ETF实际交易明细表_简化版.xlsx'
        with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
            if not turnover_details.empty:
                turnover_details.to_excel(writer, sheet_name='90分位成交额信号明细', index=False)
                print(f"90分位成交额信号明细已保存: {len(turnover_details)} 条记录")
            
            if not redemption_details.empty:
                redemption_details.to_excel(writer, sheet_name='申赎信号明细', index=False)
                print(f"申赎信号明细已保存: {len(redemption_details)} 条记录")
        
        print(f"\nExcel文件已保存: {excel_path}")
        
        # 显示统计信息
        if not turnover_details.empty:
            print(f"\n90分位成交额信号统计:")
            print(f"  总交易次数: {len(turnover_details)}")
            print(f"  涉及ETF数量: {turnover_details['ETF代码'].nunique()}")
            print(f"  平均收益率: {turnover_details['期间收益率(%)'].mean():.4f}%")
            print(f"  胜率: {(turnover_details['期间收益率(%)'] > 0).mean() * 100:.2f}%")
            
            # 显示各ETF的交易次数
            etf_counts = turnover_details['ETF代码'].value_counts().sort_index()
            print(f"\n各ETF交易次数:")
            for etf, count in etf_counts.items():
                print(f"  {etf}: {count}次")
        
        if not redemption_details.empty:
            print(f"\n申赎信号统计:")
            print(f"  总交易次数: {len(redemption_details)}")
            print(f"  涉及ETF数量: {redemption_details['ETF代码'].nunique()}")
            
            # 显示各ETF的交易次数
            etf_counts = redemption_details['ETF代码'].value_counts().sort_index()
            print(f"\n各ETF申赎信号次数:")
            for etf, count in etf_counts.items():
                print(f"  {etf}: {count}次")
    else:
        print("未能生成有效的交易明细数据")

if __name__ == "__main__":
    main()
