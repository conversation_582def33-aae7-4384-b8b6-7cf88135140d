#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF净申购数据分析脚本 - 增加市盈率筛选
计算ETF净申购金额、异动信号和收益率分析，并加入市盈率分位数筛选
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class ETFNetPurchaseAnalyzerWithPE:
    def __init__(self, data_path="ETF净申购数据", start_date="2024-04-30", end_date="2025-04-30"):
        self.data_path = data_path
        self.start_date = pd.to_datetime(start_date)
        self.end_date = pd.to_datetime(end_date)
        self.nav_data = None
        self.shares_data = {}
        self.price_data = None
        self.pe_data = None
        self.etf_mapping = None
        self.results = {}
        self.results_with_pe = {}

    def load_pe_data(self):
        """加载市盈率数据和ETF映射关系"""
        try:
            # 读取ETF跟踪指数映射表
            etf_mapping_path = '市盈率信息/ETF跟踪指数.xlsx'
            self.etf_mapping = pd.read_excel(etf_mapping_path)

            # 读取指数市盈率分位数表
            pe_data_path = '市盈率信息/指数市盈率分位数表.xlsx'
            self.pe_data = pd.read_excel(pe_data_path)

            # 处理日期列
            self.pe_data['日期'] = pd.to_datetime(self.pe_data['日期'])

            # 标准化指数代码格式
            self.etf_mapping['跟踪指数代码'] = self.etf_mapping['跟踪指数代码'].astype(str).str.zfill(6)

            # 标准化PE数据的列名（指数代码）
            pe_columns = {}
            for col in self.pe_data.columns:
                if col != '日期':
                    # 统一格式为6位数字
                    if str(col).isdigit():
                        pe_columns[col] = str(col).zfill(6)
                    else:
                        pe_columns[col] = str(col)

            self.pe_data = self.pe_data.rename(columns=pe_columns)

            print(f"成功加载ETF映射关系: {len(self.etf_mapping)} 条记录")
            print(f"成功加载市盈率数据: {len(self.pe_data)} 条记录")

            return True

        except Exception as e:
            print(f"加载市盈率数据时出错: {e}")
            return False

    def get_pe_percentile(self, etf_code, date):
        """获取指定ETF在指定日期的市盈率分位数"""
        try:
            # 提取ETF代码的数字部分
            etf_numeric = int(etf_code.split('.')[0])

            # 获取ETF对应的指数代码
            etf_row = self.etf_mapping[self.etf_mapping['ETF代码'] == etf_numeric]
            if etf_row.empty:
                return np.nan

            index_code = etf_row.iloc[0]['跟踪指数代码']

            # 确保指数代码在PE数据中存在
            if index_code not in self.pe_data.columns:
                return np.nan

            # 查找最接近的日期
            available_dates = self.pe_data['日期'].dt.date
            target_date = date.date()

            # 找到小于等于目标日期的最大日期
            valid_dates = available_dates[available_dates <= target_date]
            if valid_dates.empty:
                return np.nan

            closest_date = valid_dates.max()

            # 获取该日期的市盈率分位数
            pe_row = self.pe_data[self.pe_data['日期'].dt.date == closest_date]
            if pe_row.empty:
                return np.nan

            pe_percentile = pe_row.iloc[0][index_code]
            return pe_percentile

        except Exception as e:
            print(f"获取ETF {etf_code} 在日期 {date} 的市盈率分位数时出错: {e}")
            return np.nan

    def load_data(self):
        """加载所有数据文件（先加载全量数据用于计算信号）"""
        print("正在加载全量数据...")

        # 1. 加载ETF净值数据（全量）
        nav_file = os.path.join(self.data_path, "基础数据", "ETF净值数据.csv")
        self.nav_data_full = pd.read_csv(nav_file, index_col=0)
        self.nav_data_full.index = pd.to_datetime(self.nav_data_full.index)
        print(f"全量净值数据加载完成，包含{len(self.nav_data_full.columns)}只ETF，{len(self.nav_data_full)}个交易日")

        # 2. 加载ETF份额数据（全量）
        shares_dir = os.path.join(self.data_path, "基础数据")
        shares_files = [f for f in os.listdir(shares_dir) if f.endswith("每日份额.csv")]

        self.shares_data_full = {}
        for file in shares_files:
            etf_code = file.replace("每日份额.csv", "")
            file_path = os.path.join(shares_dir, file)
            df = pd.read_csv(file_path)
            df['date'] = pd.to_datetime(df['date'])
            df = df.set_index('date')[['shares']].sort_index()
            self.shares_data_full[etf_code] = df

        print(f"全量份额数据加载完成，包含{len(self.shares_data_full)}只ETF")

        # 3. 加载行情数据（全量）
        price_file = os.path.join(self.data_path, "行情数据", "行情序列.csv")
        self.price_data_full = pd.read_csv(price_file)
        self.price_data_full['时间'] = pd.to_datetime(self.price_data_full['时间'])

        # 转换代码格式：510050.SH -> 510050.XSHG
        self.price_data_full['标准代码'] = self.price_data_full['代码'].apply(self._convert_price_code)
        print(f"全量行情数据加载完成，包含{len(self.price_data_full)}条记录")
        print(f"回测期间：{self.start_date.strftime('%Y-%m-%d')} 至 {self.end_date.strftime('%Y-%m-%d')}")

    def _convert_price_code(self, code):
        """转换行情数据中的代码格式"""
        if pd.isna(code) or not isinstance(code, str):
            return code
        if code.endswith('.SH'):
            base_code = code.replace('.SH', '')
            return f"{base_code}.XSHG"
        elif code.endswith('.SZ'):
            base_code = code.replace('.SZ', '')
            return f"{base_code}.XSHE"
        return code

    def calculate_share_changes(self):
        """计算每只ETF的每日基金份额变化（使用全量数据）"""
        print("正在计算基金份额变化...")

        # 使用全量数据计算
        for etf_code, shares_df in self.shares_data_full.items():
            # 计算份额变化：T日份额 - T-1日份额
            shares_df['shares_change'] = shares_df['shares'].diff()

        print("基金份额变化计算完成")

    def calculate_net_purchase_amount(self):
        """计算每只ETF的每日净申购金额（使用全量数据）"""
        print("正在计算净申购金额...")

        for etf_code in self.shares_data_full.keys():
            if etf_code not in self.nav_data_full.columns:
                print(f"警告：{etf_code}在净值数据中不存在")
                continue

            shares_df = self.shares_data_full[etf_code]
            nav_series = self.nav_data_full[etf_code]

            # 合并数据
            merged_df = shares_df.join(nav_series.rename('nav'), how='inner')

            # 计算净申购金额：份额变化 * 单位净值
            merged_df['net_purchase_amount'] = merged_df['shares_change'] * merged_df['nav']

            # 更新数据
            self.shares_data_full[etf_code] = merged_df

        print("净申购金额计算完成")

    def calculate_rolling_5day_net_purchase(self):
        """计算连续5个交易日的净申购金额（使用全量数据）"""
        print("正在计算连续5日净申购金额...")

        for etf_code, df in self.shares_data_full.items():
            if 'net_purchase_amount' in df.columns:
                # 计算连续5日净申购金额累计
                df['net_purchase_5d'] = df['net_purchase_amount'].rolling(window=5, min_periods=5).sum()

        print("连续5日净申购金额计算完成")

    def calculate_percentiles(self):
        """计算净申购金额的分位数（使用全量数据，250天窗口）"""
        print("正在计算分位数...")

        # 使用标准的250天窗口（1年交易日）
        window_size = 250
        min_periods = 250  # 需要完整的250天数据

        print(f"使用{window_size}天窗口计算分位数，最少需要{min_periods}天数据")

        for etf_code, df in self.shares_data_full.items():
            if 'net_purchase_amount' not in df.columns:
                continue

            # 计算单日净申购金额的分位数
            # 正确公式：历史值小于等于当前值的比例
            df['net_purchase_percentile'] = df['net_purchase_amount'].rolling(
                window=window_size, min_periods=min_periods
            ).apply(lambda x: (x <= x.iloc[-1]).mean() * 100)

            # 计算5日净申购金额的分位数
            if 'net_purchase_5d' in df.columns:
                df['net_purchase_5d_percentile'] = df['net_purchase_5d'].rolling(
                    window=window_size, min_periods=min_periods
                ).apply(lambda x: (x <= x.iloc[-1]).mean() * 100)

        print("分位数计算完成")

    def generate_signals(self):
        """生成异动信号（使用全量数据）"""
        print("正在生成异动信号...")

        for etf_code, df in self.shares_data_full.items():
            # 信号1：基于单日净申购金额分位数
            if 'net_purchase_percentile' in df.columns:
                df['signal_1_buy'] = (df['net_purchase_percentile'] >= 95).astype(int)  # 调整为95分位
                df['signal_1_sell'] = (df['net_purchase_percentile'] <= 5).astype(int)  # 保持5分位

            # 信号2：基于5日净申购金额分位数
            if 'net_purchase_5d_percentile' in df.columns:
                df['signal_2_buy'] = (df['net_purchase_5d_percentile'] >= 95).astype(int)  # 调整为95分位
                df['signal_2_sell'] = (df['net_purchase_5d_percentile'] <= 5).astype(int)  # 保持5分位

        # 现在截取回测期间的数据用于后续分析
        self._filter_backtest_period()

        print("异动信号生成完成")

    def _filter_backtest_period(self):
        """截取回测期间的数据"""
        print(f"正在截取回测期间数据：{self.start_date.strftime('%Y-%m-%d')} 至 {self.end_date.strftime('%Y-%m-%d')}")

        # 截取净值数据
        self.nav_data = self.nav_data_full[(self.nav_data_full.index >= self.start_date) &
                                           (self.nav_data_full.index <= self.end_date)]

        # 截取份额和信号数据
        self.shares_data = {}
        for etf_code, df in self.shares_data_full.items():
            filtered_df = df[(df.index >= self.start_date) & (df.index <= self.end_date)]
            self.shares_data[etf_code] = filtered_df

        # 截取行情数据
        self.price_data = self.price_data_full[(self.price_data_full['时间'] >= self.start_date) &
                                               (self.price_data_full['时间'] <= self.end_date)]

        print(f"回测期间数据截取完成：净值数据{len(self.nav_data)}天，行情数据{len(self.price_data)}条")

    def generate_signals_with_pe_filter(self):
        """生成带市盈率筛选的异动信号"""
        print("正在生成带市盈率筛选的异动信号...")

        for etf_code, df in self.shares_data.items():
            # 初始化PE筛选后的信号列
            df['signal_1_buy_pe_filtered'] = 0
            df['signal_2_buy_pe_filtered'] = 0
            df['signal_1_sell_pe_filtered'] = df['signal_1_sell']  # 卖出信号不受PE筛选影响
            df['signal_2_sell_pe_filtered'] = df['signal_2_sell']  # 卖出信号不受PE筛选影响
            df['pe_percentile'] = np.nan
            df['pe_filter_passed'] = True

            # 对每个交易日检查PE筛选条件
            for date, row in df.iterrows():
                # 获取市盈率分位数
                pe_percentile = self.get_pe_percentile(etf_code, date)
                df.loc[date, 'pe_percentile'] = pe_percentile

                # 市盈率筛选：如果市盈率分位数 >= 80分位，则买入信号无效
                pe_filter_passed = True
                if not pd.isna(pe_percentile) and pe_percentile >= 80:
                    pe_filter_passed = False

                df.loc[date, 'pe_filter_passed'] = pe_filter_passed

                # 只有通过PE筛选的买入信号才有效
                if pe_filter_passed:
                    df.loc[date, 'signal_1_buy_pe_filtered'] = row['signal_1_buy']
                    df.loc[date, 'signal_2_buy_pe_filtered'] = row['signal_2_buy']

        print("带市盈率筛选的异动信号生成完成")

    def calculate_returns(self):
        """计算信号后的收益率（原始信号和PE筛选后信号）"""
        print("正在计算收益率...")

        # 准备行情数据字典
        price_dict = {}
        for etf_code in self.shares_data.keys():
            price_subset = self.price_data[self.price_data['标准代码'] == etf_code].copy()
            if len(price_subset) > 0:
                price_subset = price_subset.set_index('时间')[['开盘价(元)', '最高价(元)', '最低价(元)', '收盘价(元)']]
                price_dict[etf_code] = price_subset

        # 持有期列表
        holding_periods = [1, 3, 5, 7, 10, 15, 30, 60]

        # 初始化结果字典
        self.results = {
            'signal_1_buy': [],
            'signal_1_sell': [],
            'signal_2_buy': [],
            'signal_2_sell': []
        }

        self.results_with_pe = {
            'signal_1_buy_pe_filtered': [],
            'signal_1_sell_pe_filtered': [],
            'signal_2_buy_pe_filtered': [],
            'signal_2_sell_pe_filtered': []
        }

        for etf_code, df in self.shares_data.items():
            if etf_code not in price_dict:
                continue

            price_df = price_dict[etf_code]

            # 合并信号数据和价格数据
            merged_data = df.join(price_df, how='inner')

            # 计算原始信号的收益率
            for signal_type in ['signal_1_buy', 'signal_1_sell', 'signal_2_buy', 'signal_2_sell']:
                if signal_type not in merged_data.columns:
                    continue

                signal_dates = merged_data[merged_data[signal_type] == 1].index

                for signal_date in signal_dates:
                    returns_row = self._calculate_signal_returns(
                        merged_data, signal_date, holding_periods, etf_code, signal_type
                    )
                    if returns_row is not None:
                        self.results[signal_type].append(returns_row)

            # 计算PE筛选后信号的收益率
            for signal_type in ['signal_1_buy_pe_filtered', 'signal_1_sell_pe_filtered',
                               'signal_2_buy_pe_filtered', 'signal_2_sell_pe_filtered']:
                if signal_type not in merged_data.columns:
                    continue

                signal_dates = merged_data[merged_data[signal_type] == 1].index

                for signal_date in signal_dates:
                    returns_row = self._calculate_signal_returns(
                        merged_data, signal_date, holding_periods, etf_code, signal_type
                    )
                    if returns_row is not None:
                        # 添加PE相关信息
                        returns_row['pe_percentile'] = merged_data.loc[signal_date, 'pe_percentile']
                        returns_row['pe_filter_passed'] = merged_data.loc[signal_date, 'pe_filter_passed']
                        self.results_with_pe[signal_type].append(returns_row)

        print("收益率计算完成")

    def _calculate_signal_returns(self, data, signal_date, holding_periods, etf_code, signal_type):
        """计算单个信号的收益率"""
        try:
            # 找到信号日期之后的第一个交易日
            future_dates = data.index[data.index > signal_date]
            if len(future_dates) == 0:
                return None

            entry_date = future_dates[0]
            entry_price = data.loc[entry_date, '开盘价(元)']

            if pd.isna(entry_price) or entry_price <= 0:
                return None

            returns_row = {
                'etf_code': etf_code,
                'signal_type': signal_type,
                'signal_date': signal_date,
                'entry_date': entry_date,
                'entry_price': entry_price
            }

            # 判断是否为卖出信号（做空策略）
            is_sell_signal = 'sell' in signal_type

            # 计算不同持有期的收益率
            for period in holding_periods:
                exit_dates = future_dates[future_dates >= entry_date]
                if len(exit_dates) >= period:
                    exit_date = exit_dates[period-1]
                    exit_price = data.loc[exit_date, '开盘价(元)']

                    if not pd.isna(exit_price) and exit_price > 0:
                        if is_sell_signal:
                            # 卖出信号：做空策略，价格下跌为正收益
                            returns_row[f'return_{period}d'] = (entry_price - exit_price) / entry_price * 100
                        else:
                            # 买入信号：做多策略，价格上涨为正收益
                            returns_row[f'return_{period}d'] = (exit_price - entry_price) / entry_price * 100

                        # 计算区间最高涨幅和最大跌幅
                        period_data = data.loc[entry_date:exit_date]
                        if len(period_data) > 0:
                            max_high = period_data['最高价(元)'].max()
                            min_low = period_data['最低价(元)'].min()

                            if not pd.isna(max_high) and max_high > 0:
                                if is_sell_signal:
                                    # 做空：最高价对应最大亏损
                                    returns_row[f'max_gain_{period}d'] = (entry_price - min_low) / entry_price * 100  # 最大盈利（价格跌到最低）
                                    returns_row[f'max_loss_{period}d'] = (entry_price - max_high) / entry_price * 100  # 最大亏损（价格涨到最高）
                                else:
                                    # 做多：最高价对应最大盈利
                                    returns_row[f'max_gain_{period}d'] = (max_high - entry_price) / entry_price * 100
                                    returns_row[f'max_loss_{period}d'] = (min_low - entry_price) / entry_price * 100

            return returns_row

        except Exception as e:
            print(f"计算收益率时出错：{e}")
            return None

    def analyze_strategy_performance(self):
        """分析策略表现（原始信号和PE筛选后信号）"""
        print("正在分析策略表现...")

        holding_periods = [1, 3, 5, 7, 10, 15, 30, 60]

        # 分析原始信号表现
        self.performance_summary = {}
        for signal_type, results_list in self.results.items():
            if not results_list:
                continue

            df_results = pd.DataFrame(results_list)
            signal_performance = {
                'total_signals': len(df_results),
                'periods': {}
            }

            for period in holding_periods:
                return_col = f'return_{period}d'
                if return_col in df_results.columns:
                    returns = df_results[return_col].dropna()
                    if len(returns) > 0:
                        # 判断是否为卖出信号（做空策略）
                        is_sell_signal = 'sell' in signal_type

                        performance_metrics = {
                            'count': len(returns),
                            'win_rate': (returns > 0).mean() * 100,
                            'avg_return': returns.mean(),
                            'median_return': returns.median(),
                            'std_return': returns.std(),
                            'max_return': returns.max(),
                            'min_return': returns.min()
                        }

                        # 为做空策略添加专门的统计指标
                        if is_sell_signal:
                            performance_metrics['avg_decline'] = returns.mean()  # 平均跌幅（做空收益）
                            performance_metrics['decline_win_rate'] = (returns > 0).mean() * 100  # 做空胜率
                            performance_metrics['max_decline'] = returns.max()  # 最大跌幅
                            performance_metrics['strategy_type'] = '做空策略'
                        else:
                            performance_metrics['strategy_type'] = '做多策略'

                        signal_performance['periods'][f'{period}d'] = performance_metrics

            self.performance_summary[signal_type] = signal_performance

        # 分析PE筛选后信号表现
        self.performance_summary_with_pe = {}
        for signal_type, results_list in self.results_with_pe.items():
            if not results_list:
                continue

            df_results = pd.DataFrame(results_list)
            signal_performance = {
                'total_signals': len(df_results),
                'periods': {}
            }

            for period in holding_periods:
                return_col = f'return_{period}d'
                if return_col in df_results.columns:
                    returns = df_results[return_col].dropna()
                    if len(returns) > 0:
                        # 判断是否为卖出信号（做空策略）
                        is_sell_signal = 'sell' in signal_type

                        performance_metrics = {
                            'count': len(returns),
                            'win_rate': (returns > 0).mean() * 100,
                            'avg_return': returns.mean(),
                            'median_return': returns.median(),
                            'std_return': returns.std(),
                            'max_return': returns.max(),
                            'min_return': returns.min()
                        }

                        # 为做空策略添加专门的统计指标
                        if is_sell_signal:
                            performance_metrics['avg_decline'] = returns.mean()  # 平均跌幅（做空收益）
                            performance_metrics['decline_win_rate'] = (returns > 0).mean() * 100  # 做空胜率
                            performance_metrics['max_decline'] = returns.max()  # 最大跌幅
                            performance_metrics['strategy_type'] = '做空策略'
                        else:
                            performance_metrics['strategy_type'] = '做多策略'

                        signal_performance['periods'][f'{period}d'] = performance_metrics

            self.performance_summary_with_pe[signal_type] = signal_performance

        print("策略表现分析完成")

        return self.performance_summary, self.performance_summary_with_pe

    def save_results(self, output_dir="分析结果_买入95分位_卖出5分位"):
        """保存分析结果"""
        # 创建带日期的输出目录
        date_suffix = f"{self.start_date.strftime('%Y%m%d')}_{self.end_date.strftime('%Y%m%d')}"
        output_dir = f"{output_dir}_{date_suffix}"

        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        print("正在保存结果...")

        # 保存原始信号的详细收益率数据
        for signal_type, results_list in self.results.items():
            if results_list:
                df = pd.DataFrame(results_list)
                output_file = os.path.join(output_dir, f"{signal_type}_原始信号_详细结果.csv")
                df.to_csv(output_file, index=False, encoding='utf-8-sig')

        # 保存PE筛选后信号的详细收益率数据
        for signal_type, results_list in self.results_with_pe.items():
            if results_list:
                df = pd.DataFrame(results_list)
                output_file = os.path.join(output_dir, f"{signal_type}_详细结果.csv")
                df.to_csv(output_file, index=False, encoding='utf-8-sig')

        # 保存原始策略表现汇总
        summary_data = []
        short_strategy_data = []  # 专门保存做空策略数据

        for signal_type, performance in self.performance_summary.items():
            for period, metrics in performance['periods'].items():
                base_row = {
                    '信号类型': signal_type,
                    '持有期': period,
                    '信号数量': metrics['count'],
                    '胜率(%)': round(metrics['win_rate'], 2),
                    '平均收益率(%)': round(metrics['avg_return'], 4),
                    '中位数收益率(%)': round(metrics['median_return'], 4),
                    '收益率标准差(%)': round(metrics['std_return'], 4),
                    '最大收益率(%)': round(metrics['max_return'], 4),
                    '最小收益率(%)': round(metrics['min_return'], 4),
                    '策略类型': metrics.get('strategy_type', '做多策略')
                }
                summary_data.append(base_row)

                # 如果是做空策略，添加到专门的做空表格
                if 'sell' in signal_type:
                    short_row = {
                        '信号类型': signal_type,
                        '持有期': period,
                        '信号数量': metrics['count'],
                        '做空胜率(%)': round(metrics.get('decline_win_rate', metrics['win_rate']), 2),
                        '平均跌幅(%)': round(metrics.get('avg_decline', metrics['avg_return']), 4),
                        '中位数跌幅(%)': round(metrics['median_return'], 4),
                        '跌幅标准差(%)': round(metrics['std_return'], 4),
                        '最大跌幅(%)': round(metrics.get('max_decline', metrics['max_return']), 4),
                        '最小跌幅(%)': round(metrics['min_return'], 4)
                    }
                    short_strategy_data.append(short_row)

        summary_df = pd.DataFrame(summary_data)
        summary_file = os.path.join(output_dir, "原始策略表现汇总.csv")
        summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')

        # 保存做空策略专门表格
        if short_strategy_data:
            short_df = pd.DataFrame(short_strategy_data)
            short_file = os.path.join(output_dir, "原始做空策略表现汇总.csv")
            short_df.to_csv(short_file, index=False, encoding='utf-8-sig')

        # 保存PE筛选后策略表现汇总
        summary_data_pe = []
        short_strategy_data_pe = []  # 专门保存PE筛选后做空策略数据

        for signal_type, performance in self.performance_summary_with_pe.items():
            for period, metrics in performance['periods'].items():
                base_row = {
                    '信号类型': signal_type,
                    '持有期': period,
                    '信号数量': metrics['count'],
                    '胜率(%)': round(metrics['win_rate'], 2),
                    '平均收益率(%)': round(metrics['avg_return'], 4),
                    '中位数收益率(%)': round(metrics['median_return'], 4),
                    '收益率标准差(%)': round(metrics['std_return'], 4),
                    '最大收益率(%)': round(metrics['max_return'], 4),
                    '最小收益率(%)': round(metrics['min_return'], 4),
                    '策略类型': metrics.get('strategy_type', '做多策略')
                }
                summary_data_pe.append(base_row)

                # 如果是做空策略，添加到专门的做空表格
                if 'sell' in signal_type:
                    short_row = {
                        '信号类型': signal_type,
                        '持有期': period,
                        '信号数量': metrics['count'],
                        '做空胜率(%)': round(metrics.get('decline_win_rate', metrics['win_rate']), 2),
                        '平均跌幅(%)': round(metrics.get('avg_decline', metrics['avg_return']), 4),
                        '中位数跌幅(%)': round(metrics['median_return'], 4),
                        '跌幅标准差(%)': round(metrics['std_return'], 4),
                        '最大跌幅(%)': round(metrics.get('max_decline', metrics['max_return']), 4),
                        '最小跌幅(%)': round(metrics['min_return'], 4)
                    }
                    short_strategy_data_pe.append(short_row)

        summary_df_pe = pd.DataFrame(summary_data_pe)
        summary_file_pe = os.path.join(output_dir, "PE筛选后策略表现汇总.csv")
        summary_df_pe.to_csv(summary_file_pe, index=False, encoding='utf-8-sig')

        # 保存PE筛选后做空策略专门表格
        if short_strategy_data_pe:
            short_df_pe = pd.DataFrame(short_strategy_data_pe)
            short_file_pe = os.path.join(output_dir, "PE筛选后做空策略表现汇总.csv")
            short_df_pe.to_csv(short_file_pe, index=False, encoding='utf-8-sig')

        # 保存所有ETF的详细数据（包含PE信息）
        self.save_detailed_etf_data_with_pe(output_dir)

        print(f"结果已保存到 {output_dir} 目录")

    def save_detailed_etf_data_with_pe(self, output_dir):
        """保存包含PE信息的ETF详细数据"""
        print("正在保存包含PE信息的ETF详细数据...")

        # 为每只ETF保存详细数据
        for etf_code, df in self.shares_data.items():
            if len(df) == 0:
                continue

            # 准备输出数据
            output_df = df.copy()
            output_df.index.name = 'date'

            # 重新排列列的顺序，便于查看
            columns_order = ['shares', 'shares_change', 'nav', 'net_purchase_amount',
                           'net_purchase_5d', 'net_purchase_percentile', 'net_purchase_5d_percentile',
                           'signal_1_buy', 'signal_1_sell', 'signal_2_buy', 'signal_2_sell',
                           'pe_percentile', 'pe_filter_passed',
                           'signal_1_buy_pe_filtered', 'signal_1_sell_pe_filtered',
                           'signal_2_buy_pe_filtered', 'signal_2_sell_pe_filtered']

            # 只保留存在的列
            available_columns = [col for col in columns_order if col in output_df.columns]
            output_df = output_df[available_columns]

            # 保存到文件
            output_file = os.path.join(output_dir, f"{etf_code}_详细数据_含PE筛选.csv")
            output_df.to_csv(output_file, encoding='utf-8-sig')

        # 创建汇总表格，包含所有ETF的关键数据
        self.create_summary_table_with_pe(output_dir)

        print("包含PE信息的ETF详细数据保存完成")

    def create_summary_table_with_pe(self, output_dir):
        """创建包含PE信息的汇总验证表格"""
        print("正在创建包含PE信息的汇总验证表格...")

        all_data = []

        for etf_code, df in self.shares_data.items():
            if len(df) == 0:
                continue

            # 为每个日期创建一行数据
            for date, row in df.iterrows():
                data_row = {
                    'ETF代码': etf_code,
                    '日期': date.strftime('%Y-%m-%d'),
                    '基金份额': row.get('shares', np.nan),
                    '份额变化': row.get('shares_change', np.nan),
                    '单位净值': row.get('nav', np.nan),
                    '净申购金额': row.get('net_purchase_amount', np.nan),
                    '连续5日净申购金额': row.get('net_purchase_5d', np.nan),
                    '净申购金额分位数': row.get('net_purchase_percentile', np.nan),
                    '连续5日净申购金额分位数': row.get('net_purchase_5d_percentile', np.nan),
                    '市盈率分位数': row.get('pe_percentile', np.nan),
                    'PE筛选通过': row.get('pe_filter_passed', True),
                    '原始信号1_买入': row.get('signal_1_buy', 0),
                    '原始信号1_卖出': row.get('signal_1_sell', 0),
                    '原始信号2_买入': row.get('signal_2_buy', 0),
                    '原始信号2_卖出': row.get('signal_2_sell', 0),
                    'PE筛选后信号1_买入': row.get('signal_1_buy_pe_filtered', 0),
                    'PE筛选后信号1_卖出': row.get('signal_1_sell_pe_filtered', 0),
                    'PE筛选后信号2_买入': row.get('signal_2_buy_pe_filtered', 0),
                    'PE筛选后信号2_卖出': row.get('signal_2_sell_pe_filtered', 0)
                }
                all_data.append(data_row)

        # 创建汇总DataFrame
        summary_df = pd.DataFrame(all_data)

        # 按ETF代码和日期排序
        summary_df = summary_df.sort_values(['ETF代码', '日期'])

        # 保存汇总表格
        summary_file = os.path.join(output_dir, "所有ETF数据汇总验证表_含PE筛选.csv")
        summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')

        # 创建PE筛选后信号汇总表
        pe_signal_cols = ['PE筛选后信号1_买入', 'PE筛选后信号1_卖出', 'PE筛选后信号2_买入', 'PE筛选后信号2_卖出']
        pe_signal_summary = summary_df[summary_df[pe_signal_cols].sum(axis=1) > 0].copy()
        pe_signal_file = os.path.join(output_dir, "PE筛选后信号数据汇总表.csv")
        pe_signal_summary.to_csv(pe_signal_file, index=False, encoding='utf-8-sig')

        print(f"汇总验证表格已保存，包含{len(summary_df)}条记录")
        print(f"PE筛选后信号数据汇总表已保存，包含{len(pe_signal_summary)}条信号记录")

    def run_analysis(self):
        """运行完整分析流程"""
        print("开始ETF净申购数据分析（含市盈率筛选）...")

        # 加载市盈率数据
        if not self.load_pe_data():
            print("无法加载市盈率数据，退出分析")
            return None

        # 原有分析流程
        self.load_data()
        self.calculate_share_changes()
        self.calculate_net_purchase_amount()
        self.calculate_rolling_5day_net_purchase()
        self.calculate_percentiles()
        self.generate_signals()

        # 新增：生成带PE筛选的信号
        self.generate_signals_with_pe_filter()

        # 计算收益率
        self.calculate_returns()

        # 分析策略表现
        self.analyze_strategy_performance()

        # 保存结果
        self.save_results()

        print("分析完成！")
        return True

if __name__ == "__main__":
    analyzer = ETFNetPurchaseAnalyzerWithPE()
    result = analyzer.run_analysis()

    if result:
        print("\n=== 策略表现对比 ===")
        print("\n原始策略:")
        for signal_type, perf in analyzer.performance_summary.items():
            print(f"  {signal_type}: 总信号数 {perf['total_signals']}")

        print("\nPE筛选后策略:")
        for signal_type, perf in analyzer.performance_summary_with_pe.items():
            print(f"  {signal_type}: 总信号数 {perf['total_signals']}")
