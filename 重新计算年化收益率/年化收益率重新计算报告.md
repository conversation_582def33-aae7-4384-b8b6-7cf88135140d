# 重新计算年化收益率分析报告

## 计算逻辑更新

### 原始逻辑问题
- 原始计算中，同一天同一ETF的多个信号被重复计算
- 导致年化收益率被高估

### 新的计算逻辑
- **信号合并**: 每日每个ETF每个策略组合最多一个信号
- **避免重复**: 同一天的多个信号只取最早的一个
- **年化计算**: 基于实际的信号交易日数重新计算

## 更新后的汇总统计结果

| 阈值 | 持有周期 | 信号数 | 胜率(%) | 平均收益率(%) | 中位数收益率(%) | 年化收益率(%) |
|------|----------|--------|---------|---------------|----------------|---------------|
| 大于80分位 | next_10day_close | 1065 | 72.11 | 5.8086 | 2.9722 | 509.45 |
| 大于80分位 | next_30day_close | 897 | 61.87 | 8.9357 | 4.1315 | 660.09 |
| 大于80分位 | next_5day_close | 1125 | 66.76 | 3.2443 | 1.2300 | 300.57 |
| 大于80分位 | next_day_close | 1163 | 57.09 | 0.9537 | 0.2991 | 91.34 |
| 大于90分位 | next_10day_close | 845 | 74.79 | 6.5893 | 3.4524 | 458.54 |
| 大于90分位 | next_30day_close | 705 | 64.54 | 10.4436 | 5.0591 | 606.34 |
| 大于90分位 | next_5day_close | 888 | 68.24 | 3.9838 | 1.4253 | 291.33 |
| 大于90分位 | next_day_close | 910 | 58.13 | 1.1806 | 0.3853 | 88.48 |
| 大于95分位 | next_10day_close | 607 | 75.78 | 7.1255 | 3.8366 | 3020.99 |
| 大于95分位 | next_30day_close | 502 | 65.14 | 11.5708 | 5.7334 | 9142.75 |
| 大于95分位 | next_5day_close | 634 | 68.45 | 4.8106 | 1.8643 | 251.17 |
| 大于95分位 | next_day_close | 647 | 60.28 | 1.4731 | 0.4496 | 78.49 |


## 关键变化分析

### 1. 信号数量变化
- 原始信号经过每日合并后，信号数量显著减少
- 避免了同一天多次交易的重复计算

### 2. 年化收益率调整
- 基于实际的信号交易日数重新计算
- 更准确地反映策略的真实年化表现

### 3. 策略表现评估
- 胜率和平均收益率保持不变（基于相同的交易逻辑）
- 年化收益率更加保守和现实

## 计算方法说明

```python
# 信号频率计算
signal_frequency_per_year = (unique_signal_days / total_data_days) * 252

# 年化收益率计算
annual_return = ((1 + avg_return/100) ** signal_frequency_per_year) - 1
annualized_return = annual_return * 100
```

## 结论

重新计算后的年化收益率更加准确地反映了策略的真实表现，避免了重复计算导致的高估问题。这为投资决策提供了更可靠的参考依据。

---
*报告生成时间: 2025-06-09 19:52:20*
