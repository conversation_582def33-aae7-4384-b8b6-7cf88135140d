# 申赎买入信号按指数汇总报告

## 数据概述

**处理时间**: 2025-06-12 20:46:11  
**数据源**: 分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv  
**信号类型**: 申赎买入信号（95分位，PE筛选）  
**交易日来源**: 上证指数行情序列.xlsx

## 输出表格结构

### 主表：申赎买入信号按指数汇总
- **A列**: 日期（从上证指数行情序列.xlsx读取的交易日）
- **B列开始**: 各跟踪指数代码列，数值为当日该指数下发出信号的ETF数量
- **汇总逻辑**: 同一指数下发出信号的ETF个数求和

### 统计表格
1. **指数信号统计**: 各指数的ETF信号汇总统计和跟踪ETF信息
2. **ETF指数映射**: ETF与跟踪指数的对应关系
3. **日期信号统计**: 各交易日的ETF信号分布统计

## 数据统计

- **交易日总数**: 243 天
- **跟踪指数数量**: 10 个
- **总ETF信号数**: 171.0 个
- **有信号的交易日**: 81 天

## 主要指数ETF信号分布

1. **指数 399006**: 44.0个ETF信号, 38天, 跟踪3个ETF
2. **指数 300**: 37.0个ETF信号, 18天, 跟踪4个ETF
3. **指数 852**: 37.0个ETF信号, 18天, 跟踪4个ETF
4. **指数 399330**: 16.0个ETF信号, 16天, 跟踪1个ETF
5. **指数 16**: 9.0个ETF信号, 7天, 跟踪2个ETF
6. **指数 905**: 9.0个ETF信号, 7天, 跟踪3个ETF
7. **指数 746059**: 8.0个ETF信号, 8天, 跟踪1个ETF
8. **指数 688**: 7.0个ETF信号, 7天, 跟踪2个ETF
9. **指数 10**: 2.0个ETF信号, 2天, 跟踪1个ETF
10. **指数 906**: 2.0个ETF信号, 2天, 跟踪1个ETF


## 指数ETF映射关系

- **指数 10**: 510180 (1个ETF)
- **指数 16**: 510050, 510100 (2个ETF)
- **指数 300**: 159919, 510300, 510310, 510330 (4个ETF)
- **指数 688**: 588050, 588080 (2个ETF)
- **指数 852**: 159629, 159845, 512100, 560010 (4个ETF)
- **指数 905**: 159922, 510500, 512500 (3个ETF)
- **指数 906**: 515800 (1个ETF)
- **指数 399006**: 159915, 159952, 159977 (3个ETF)
- **指数 399330**: 159901 (1个ETF)
- **指数 746059**: 560050 (1个ETF)


## 使用说明

1. **数据格式**: Excel文件，包含4个工作表
2. **主要用途**: 指数层面的申赎信号分析、指数选择、ETF配置研究
3. **注意事项**: 
   - 数值表示当日该指数下发出申赎买入信号的ETF数量
   - 空值表示当日该指数下无ETF发出信号
   - 交易日基于上证指数行情数据

## 分析建议

1. **指数活跃度**: 关注ETF信号数量多的指数
2. **ETF参与度**: 分析指数下ETF的参与程度
3. **信号同步性**: 研究同一指数下多个ETF同时发出信号的情况
4. **时间趋势**: 分析指数ETF信号的时间分布特征

---
*报告生成时间: 2025-06-12 20:46:11*
