#!/usr/bin/env python3
"""
生成三类信号的综合对比分析报告
1. 90分位成交额信号
2. 申赎买入信号（95分位）
3. 申赎卖出信号（5分位）
"""

import pandas as pd
from datetime import datetime

def generate_comprehensive_comparison():
    """生成三类信号综合对比分析报告"""
    
    print("=== 生成三类信号综合对比分析报告 ===")
    
    try:
        # 读取三类信号分析结果
        # 1. 90分位成交额信号
        try:
            turnover_df = pd.read_excel('跟踪相同指数ETF信号相关性分析结果.xlsx', sheet_name='ETF对相关性分析')
            turnover_available = True
        except:
            print("未找到90分位成交额信号分析结果")
            turnover_df = pd.DataFrame()
            turnover_available = False
        
        # 2. 申赎买入信号
        try:
            buy_df = pd.read_excel('申赎信号ETF完整分析结果.xlsx', sheet_name='申赎信号相关性分析')
            buy_available = True
        except:
            print("未找到申赎买入信号分析结果")
            buy_df = pd.DataFrame()
            buy_available = False
        
        # 3. 申赎卖出信号
        try:
            sell_df = pd.read_excel('卖出信号ETF完整分析结果.xlsx', sheet_name='卖出信号相关性分析')
            sell_available = True
        except:
            print("未找到申赎卖出信号分析结果")
            sell_df = pd.DataFrame()
            sell_available = False
        
        # 生成综合对比报告
        report_content = f"""# 三类ETF信号相关性综合对比分析报告

## 报告概述

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**对比目的**: 全面比较三类信号在跟踪相同指数ETF间的相关性差异  
**分析方法**: Jaccard相似度系数对比

## 三类信号数据源对比

### 信号1：90分位成交额信号（PE筛选）
- **数据源**: `回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv`
- **信号特征**: 基于5分钟成交额90分位阈值
- **筛选条件**: PE筛选通过且threshold=90
- **信号性质**: 市场活跃度信号

### 信号2：申赎买入信号（95分位，PE筛选）
- **数据源**: `分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv`
- **信号特征**: 基于申赎净额95分位阈值（大额申购）
- **筛选条件**: PE筛选通过
- **信号性质**: 资金流入信号

### 信号3：申赎卖出信号（5分位，PE筛选）
- **数据源**: `分析结果_买入95分位_卖出5分位_20240430_20250430/signal_2_sell_pe_filtered_详细结果.csv`
- **信号特征**: 基于申赎净额5分位阈值（大额赎回）
- **筛选条件**: PE筛选通过
- **信号性质**: 资金流出信号

## 三类信号相关性对比分析

### 基础统计对比

| 信号类型 | 可分析ETF对数 | 平均Jaccard相似度 | 相关性水平 |
|---------|--------------|------------------|-----------|
"""
        
        # 添加各信号的基础统计
        if turnover_available:
            turnover_avg = turnover_df['Jaccard相似度'].mean()
            turnover_level = "强相关" if turnover_avg >= 0.6 else "中等相关" if turnover_avg >= 0.3 else "弱相关"
            report_content += f"| 90分位成交额信号 | {len(turnover_df)} | {turnover_avg:.4f} | {turnover_level} |\n"
        
        if buy_available:
            buy_avg = buy_df['Jaccard相似度'].mean()
            buy_level = "强相关" if buy_avg >= 0.6 else "中等相关" if buy_avg >= 0.3 else "弱相关"
            report_content += f"| 申赎买入信号 | {len(buy_df)} | {buy_avg:.4f} | {buy_level} |\n"
        
        if sell_available:
            sell_avg = sell_df['Jaccard相似度'].mean()
            sell_level = "强相关" if sell_avg >= 0.6 else "中等相关" if sell_avg >= 0.3 else "弱相关"
            report_content += f"| 申赎卖出信号 | {len(sell_df)} | {sell_avg:.4f} | {sell_level} |\n"
        
        # 相关性强度分布对比
        report_content += f"""

### 相关性强度分布对比

| 信号类型 | 强相关(≥0.6) | 中等相关(0.3-0.6) | 弱相关(<0.3) |
|---------|-------------|------------------|-------------|
"""
        
        if turnover_available:
            t_high = len(turnover_df[turnover_df['Jaccard相似度'] >= 0.6])
            t_medium = len(turnover_df[(turnover_df['Jaccard相似度'] >= 0.3) & (turnover_df['Jaccard相似度'] < 0.6)])
            t_low = len(turnover_df[turnover_df['Jaccard相似度'] < 0.3])
            report_content += f"| 90分位成交额信号 | {t_high}对({t_high/len(turnover_df)*100:.1f}%) | {t_medium}对({t_medium/len(turnover_df)*100:.1f}%) | {t_low}对({t_low/len(turnover_df)*100:.1f}%) |\n"
        
        if buy_available:
            b_high = len(buy_df[buy_df['Jaccard相似度'] >= 0.6])
            b_medium = len(buy_df[(buy_df['Jaccard相似度'] >= 0.3) & (buy_df['Jaccard相似度'] < 0.6)])
            b_low = len(buy_df[buy_df['Jaccard相似度'] < 0.3])
            report_content += f"| 申赎买入信号 | {b_high}对({b_high/len(buy_df)*100:.1f}%) | {b_medium}对({b_medium/len(buy_df)*100:.1f}%) | {b_low}对({b_low/len(buy_df)*100:.1f}%) |\n"
        
        if sell_available:
            s_high = len(sell_df[sell_df['Jaccard相似度'] >= 0.6])
            s_medium = len(sell_df[(sell_df['Jaccard相似度'] >= 0.3) & (sell_df['Jaccard相似度'] < 0.6)])
            s_low = len(sell_df[sell_df['Jaccard相似度'] < 0.3])
            report_content += f"| 申赎卖出信号 | {s_high}对({s_high/len(sell_df)*100:.1f}%) | {s_medium}对({s_medium/len(sell_df)*100:.1f}%) | {s_low}对({s_low/len(sell_df)*100:.1f}%) |\n"
        
        # 各指数相关性对比
        report_content += f"""

### 各指数相关性对比分析

"""
        
        # 按指数分组对比
        if turnover_available and buy_available and sell_available:
            turnover_by_index = turnover_df.groupby('跟踪指数代码')['Jaccard相似度'].mean()
            buy_by_index = buy_df.groupby('跟踪指数代码')['Jaccard相似度'].mean()
            sell_by_index = sell_df.groupby('跟踪指数代码')['Jaccard相似度'].mean()
            
            report_content += f"| 指数代码 | 90分位成交额 | 申赎买入 | 申赎卖出 | 最高相关性 | 最低相关性 |\n"
            report_content += f"|---------|-------------|---------|---------|-----------|----------|\n"
            
            all_indices = set(turnover_by_index.index) | set(buy_by_index.index) | set(sell_by_index.index)
            
            for index_code in sorted(all_indices):
                turnover_val = turnover_by_index.get(index_code, 0)
                buy_val = buy_by_index.get(index_code, 0)
                sell_val = sell_by_index.get(index_code, 0)
                
                values = [v for v in [turnover_val, buy_val, sell_val] if v > 0]
                max_val = max(values) if values else 0
                min_val = min(values) if values else 0
                
                report_content += f"| {index_code} | {turnover_val:.4f} | {buy_val:.4f} | {sell_val:.4f} | {max_val:.4f} | {min_val:.4f} |\n"
        
        # 分析结论
        report_content += f"""

## 综合分析结论

### 1. 三类信号相关性特征对比

"""
        
        if turnover_available:
            report_content += f"""
#### 90分位成交额信号特征
- **平均相关性**: {turnover_avg:.4f} (强相关)
- **相关性分布**: {t_high/len(turnover_df)*100:.1f}%强相关，{t_medium/len(turnover_df)*100:.1f}%中等相关，{t_low/len(turnover_df)*100:.1f}%弱相关
- **信号特点**: 反映市场活跃度，ETF间高度同步
- **驱动因素**: 底层指数成交量变化
"""
        
        if buy_available:
            report_content += f"""
#### 申赎买入信号特征
- **平均相关性**: {buy_avg:.4f} (弱到中等相关)
- **相关性分布**: {b_high/len(buy_df)*100:.1f}%强相关，{b_medium/len(buy_df)*100:.1f}%中等相关，{b_low/len(buy_df)*100:.1f}%弱相关
- **信号特点**: 反映资金流入，ETF间相对独立
- **驱动因素**: 投资者申购行为差异
"""
        
        if sell_available:
            report_content += f"""
#### 申赎卖出信号特征
- **平均相关性**: {sell_avg:.4f} (弱到中等相关)
- **相关性分布**: {s_high/len(sell_df)*100:.1f}%强相关，{s_medium/len(sell_df)*100:.1f}%中等相关，{s_low/len(sell_df)*100:.1f}%弱相关
- **信号特点**: 反映资金流出，ETF间相对独立
- **驱动因素**: 投资者赎回行为差异
"""
        
        # 投资策略建议
        report_content += f"""

### 2. 投资策略建议

#### 基于信号相关性的配置策略

"""
        
        if turnover_available and buy_available and sell_available:
            report_content += f"""
1. **90分位成交额信号策略**
   - **配置原则**: 每个指数只选择1个ETF，避免重复配置
   - **适用场景**: 短期择时交易，技术面分析
   - **风险控制**: 高相关性要求严格的风险分散

2. **申赎买入信号策略**
   - **配置原则**: 可适度配置多个同指数ETF，相关性较低
   - **适用场景**: 捕捉资金流入机会，中长期投资
   - **风险控制**: 天然分散，但需关注个别高相关ETF对

3. **申赎卖出信号策略**
   - **配置原则**: 可配置多个同指数ETF，相关性适中
   - **适用场景**: 风险规避，资金流出预警
   - **风险控制**: 相对分散，适合防御性策略

#### 组合策略建议

1. **多信号融合策略**
   - **核心持仓**: 基于申赎信号的多ETF配置
   - **择时交易**: 基于90分位信号的短期操作
   - **风险管理**: 结合卖出信号进行止损

2. **分层投资策略**
   - **第一层**: 90分位信号快速响应层
   - **第二层**: 申赎买入信号价值发现层
   - **第三层**: 申赎卖出信号风险控制层
"""
        
        # 风险提示
        report_content += f"""

### 3. 风险提示与建议

#### 各信号类型风险特征

1. **90分位成交额信号风险**
   - **高相关性风险**: 系统性风险集中
   - **技术性风险**: 可能产生虚假信号
   - **建议**: 严格控制同指数ETF数量

2. **申赎买入信号风险**
   - **滞后性风险**: 申赎数据可能存在披露延迟
   - **噪音风险**: 个别大额申购可能误导
   - **建议**: 结合其他指标确认信号

3. **申赎卖出信号风险**
   - **恐慌性风险**: 可能放大市场恐慌情绪
   - **流动性风险**: 大额赎回可能影响ETF流动性
   - **建议**: 理性分析赎回原因，避免盲目跟随

#### 综合风险管理建议

1. **信号确认机制**: 多信号交叉验证，提高准确性
2. **动态调整策略**: 根据市场环境调整信号权重
3. **定期评估**: 定期评估各信号的有效性和相关性变化

---

## 技术说明

### 相关性计算方法
- **Jaccard相似度**: |A ∩ B| / |A ∪ B|
- **相关性分类**: 强相关(≥0.6)、中等相关(0.3-0.6)、弱相关(<0.3)

### 数据处理统一标准
- **时间标准化**: 统一转换为日期格式
- **ETF代码标准化**: 去除后缀，统一格式
- **信号去重**: 每日每个ETF只保留一个信号

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存报告
        report_path = '三类ETF信号相关性综合对比分析报告.md'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"综合对比分析报告已保存: {report_path}")
        
        # 显示关键结论
        print(f"\n=== 三类信号对比关键结论 ===")
        if turnover_available:
            print(f"1. 90分位成交额信号: 平均相关性{turnover_avg:.4f} (强相关)")
        if buy_available:
            print(f"2. 申赎买入信号: 平均相关性{buy_avg:.4f} (弱到中等相关)")
        if sell_available:
            print(f"3. 申赎卖出信号: 平均相关性{sell_avg:.4f} (弱到中等相关)")
        
        if turnover_available and buy_available and sell_available:
            print(f"4. 相关性排序: 90分位 > 申赎卖出 > 申赎买入")
            print(f"5. 策略建议: 90分位信号需严格控制重复配置，申赎信号可适度分散")
        
    except Exception as e:
        print(f"生成综合对比报告时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    generate_comprehensive_comparison()
