import pandas as pd
from scipy.stats import percentileofscore
from datetime import timedelta

def calculate_pe_percentiles(input_file_path, output_file_path):
    """
    读取指数市盈率数据，计算每只指数每日市盈率在过去三年数据中的分位数，
    并重新生成一张表把分位数数据记录下来。

    参数:
    input_file_path (str): 输入的Excel文件路径，包含市盈率数据。
    output_file_path (str): 输出的Excel文件路径，用于保存计算得到的分位数数据。
    """
    try:
        # 读取Excel文件，第一行为列名，第一列为日期索引
        df = pd.read_excel(input_file_path, index_col=0)
    except FileNotFoundError:
        print(f"错误：输入文件未找到于 {input_file_path}")
        return
    except Exception as e:
        print(f"读取Excel文件时发生错误: {e}")
        return

    # 将索引转换为datetime对象
    try:
        df.index = pd.to_datetime(df.index)
    except Exception as e:
        print(f"转换日期索引时发生错误: {e}. 请确保第一列是有效的日期格式。")
        return

    # 创建一个空的DataFrame来存储分位数结果
    percentile_df = pd.DataFrame(index=df.index, columns=df.columns)

    # 定义三年时间窗口
    three_years = timedelta(days=3*365.25) # 包含闰年

    # 遍历每一列（即每只指数）
    for column in df.columns:
        print(f"正在处理指数: {column}")
        # 遍历每一行（即每个日期）
        for date in df.index:
            current_pe = df.loc[date, column]

            # 如果当前PE值为空，则跳过
            if pd.isna(current_pe):
                percentile_df.loc[date, column] = None
                continue

            # 定义过去三年的时间窗口
            start_date_window = date - three_years
            end_date_window = date

            # 筛选出该指数在过去三年窗口内的数据
            historical_data = df.loc[(df.index >= start_date_window) & (df.index <= end_date_window), column].dropna()

            if not historical_data.empty:
                # 计算当前PE值在历史数据中的分位数
                # percentileofscore的kind='rank'行为：如果值在数组中，则返回其百分位数排名。
                # 如果值不在数组中，则返回其应插入位置的百分位数排名。
                percentile = percentileofscore(historical_data, current_pe, kind='rank')
                percentile_df.loc[date, column] = percentile
            else:
                percentile_df.loc[date, column] = None # 如果没有足够的历史数据

    # 将结果保存到新的Excel文件
    try:
        percentile_df.to_excel(output_file_path)
        print(f"分位数数据已成功保存到: {output_file_path}")
    except Exception as e:
        print(f"保存结果到Excel文件时发生错误: {e}")

if __name__ == "__main__":
    # 定义输入和输出文件路径
    # 请确保 '指数市盈率信息.xls' 文件与脚本在同一目录下，或者提供完整路径
    input_excel_file = "指数市盈率信息.xls"
    output_excel_file = "指数市盈率分位数表.xlsx"

    # 调用函数进行计算和保存
    calculate_pe_percentiles(input_excel_file, output_excel_file)

    # 提示：如果输入文件不在脚本的同一目录，请修改 input_excel_file 的路径
    # 例如: input_excel_file = "/path/to/your/指数市盈率信息.xls"
    # 同样，也可以为 output_excel_file 指定完整路径
    # 例如: output_excel_file = "/path/to/your/output/指数市盈率分位数表.xlsx"