#!/usr/bin/env python3
"""
生成最终的ETF交易报告
整合所有信号日期和交易结果
"""

import pandas as pd
from datetime import datetime

def create_final_report():
    """创建最终的交易报告"""
    
    print("=== 生成最终ETF交易报告 ===")
    
    # 读取90分位成交额信号结果
    try:
        turnover_excel = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        print(f"90分位成交额信号结果: {len(turnover_excel)} 个ETF")
    except:
        turnover_excel = pd.DataFrame()
        print("未找到90分位成交额信号结果文件")
    
    # 读取申赎信号结果
    try:
        redemption_excel = pd.read_excel('超额收益_申赎信号/ETF申赎信号择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        print(f"申赎信号结果: {len(redemption_excel)} 个ETF")
    except:
        redemption_excel = pd.DataFrame()
        print("未找到申赎信号结果文件")
    
    # 读取简化版交易明细
    try:
        simple_details = pd.read_excel('ETF实际交易明细表_简化版.xlsx', sheet_name=None)
        print(f"简化版交易明细已读取")
    except:
        simple_details = {}
        print("未找到简化版交易明细文件")
    
    # 创建最终报告
    final_report = []
    
    # 处理90分位成交额信号
    if not turnover_excel.empty:
        for _, row in turnover_excel.iterrows():
            etf_code = row['ETF代码']
            
            # 从简化版明细中获取信号次数
            signal_count = 0
            if '90分位成交额信号明细' in simple_details:
                signal_count = len(simple_details['90分位成交额信号明细'][
                    simple_details['90分位成交额信号明细']['ETF代码'] == etf_code
                ])
            
            final_report.append({
                'ETF代码': etf_code,
                '信号类型': '90分位成交额信号',
                '跟踪指数': row['跟踪指数'],
                '总信号次数': signal_count,
                '实际交易次数': row['交易次数'],
                '信号转化率(%)': round(row['交易次数'] / signal_count * 100, 2) if signal_count > 0 else 0,
                '成功交易次数': row['成功交易'],
                '胜率(%)': row['胜率(%)'],
                '择时累计收益率(%)': row['择时累计收益率(%)'],
                '指数基准收益率(%)': row['指数基准收益率(%)'],
                '超额收益率(%)': row['超额收益率(%)']
            })
    
    # 处理申赎信号
    if not redemption_excel.empty:
        for _, row in redemption_excel.iterrows():
            etf_code = row['ETF代码']
            
            # 从简化版明细中获取信号次数
            signal_count = 0
            if '申赎信号明细' in simple_details:
                signal_count = len(simple_details['申赎信号明细'][
                    simple_details['申赎信号明细']['ETF代码'] == etf_code
                ])
            
            final_report.append({
                'ETF代码': etf_code,
                '信号类型': '申赎信号',
                '跟踪指数': row['跟踪指数'],
                '总信号次数': signal_count,
                '实际交易次数': row['交易次数'],
                '信号转化率(%)': round(row['交易次数'] / signal_count * 100, 2) if signal_count > 0 else 0,
                '成功交易次数': row['成功交易'],
                '胜率(%)': row['胜率(%)'],
                '择时累计收益率(%)': row['择时累计收益率(%)'],
                '指数基准收益率(%)': row['指数基准收益率(%)'],
                '超额收益率(%)': row['超额收益率(%)']
            })
    
    return pd.DataFrame(final_report)

def create_etf_comparison():
    """创建ETF对比表"""
    
    # 读取两种信号的结果
    try:
        turnover_excel = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        redemption_excel = pd.read_excel('超额收益_申赎信号/ETF申赎信号择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        
        # 合并两种信号的结果
        comparison_data = []
        
        # 获取所有ETF代码
        all_etfs = set()
        if not turnover_excel.empty:
            all_etfs.update(turnover_excel['ETF代码'].tolist())
        if not redemption_excel.empty:
            all_etfs.update(redemption_excel['ETF代码'].tolist())
        
        for etf_code in sorted(all_etfs):
            # 90分位信号数据
            turnover_row = turnover_excel[turnover_excel['ETF代码'] == etf_code]
            if not turnover_row.empty:
                turnover_trades = turnover_row.iloc[0]['交易次数']
                turnover_win_rate = turnover_row.iloc[0]['胜率(%)']
                turnover_excess = turnover_row.iloc[0]['超额收益率(%)']
                turnover_index = turnover_row.iloc[0]['跟踪指数']
            else:
                turnover_trades = 0
                turnover_win_rate = 0
                turnover_excess = 0
                turnover_index = ''
            
            # 申赎信号数据
            redemption_row = redemption_excel[redemption_excel['ETF代码'] == etf_code]
            if not redemption_row.empty:
                redemption_trades = redemption_row.iloc[0]['交易次数']
                redemption_win_rate = redemption_row.iloc[0]['胜率(%)']
                redemption_excess = redemption_row.iloc[0]['超额收益率(%)']
                redemption_index = redemption_row.iloc[0]['跟踪指数']
            else:
                redemption_trades = 0
                redemption_win_rate = 0
                redemption_excess = 0
                redemption_index = ''
            
            # 确定跟踪指数
            index_code = turnover_index if turnover_index else redemption_index
            
            comparison_data.append({
                'ETF代码': etf_code,
                '跟踪指数': index_code,
                '90分位信号_交易次数': turnover_trades,
                '90分位信号_胜率(%)': turnover_win_rate,
                '90分位信号_超额收益(%)': turnover_excess,
                '申赎信号_交易次数': redemption_trades,
                '申赎信号_胜率(%)': redemption_win_rate,
                '申赎信号_超额收益(%)': redemption_excess,
                '总交易次数': turnover_trades + redemption_trades,
                '最佳策略': '90分位信号' if turnover_excess > redemption_excess else '申赎信号' if redemption_excess > turnover_excess else '相当'
            })
        
        return pd.DataFrame(comparison_data)
        
    except Exception as e:
        print(f"创建ETF对比表时出错: {e}")
        return pd.DataFrame()

def main():
    """主函数"""
    print("=== ETF最终交易报告生成工具 ===")
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建最终报告
    final_report = create_final_report()
    
    # 创建ETF对比表
    comparison_table = create_etf_comparison()
    
    # 保存到Excel文件
    excel_path = 'ETF最终交易报告.xlsx'
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        
        # 最终报告
        if not final_report.empty:
            final_report.to_excel(writer, sheet_name='最终交易报告', index=False)
        
        # ETF对比表
        if not comparison_table.empty:
            comparison_table.to_excel(writer, sheet_name='ETF策略对比', index=False)
        
        # 统计摘要
        stats_data = []
        
        if not final_report.empty:
            # 90分位信号统计
            turnover_data = final_report[final_report['信号类型'] == '90分位成交额信号']
            if not turnover_data.empty:
                stats_data.extend([
                    ['90分位成交额信号', ''],
                    ['参与ETF数量', len(turnover_data)],
                    ['总信号次数', turnover_data['总信号次数'].sum()],
                    ['总交易次数', turnover_data['实际交易次数'].sum()],
                    ['平均信号转化率(%)', turnover_data['信号转化率(%)'].mean()],
                    ['平均胜率(%)', turnover_data['胜率(%)'].mean()],
                    ['平均超额收益率(%)', turnover_data['超额收益率(%)'].mean()],
                    ['', '']
                ])
            
            # 申赎信号统计
            redemption_data = final_report[final_report['信号类型'] == '申赎信号']
            if not redemption_data.empty:
                stats_data.extend([
                    ['申赎信号', ''],
                    ['参与ETF数量', len(redemption_data)],
                    ['总信号次数', redemption_data['总信号次数'].sum()],
                    ['总交易次数', redemption_data['实际交易次数'].sum()],
                    ['平均信号转化率(%)', redemption_data['信号转化率(%)'].mean()],
                    ['平均胜率(%)', redemption_data['胜率(%)'].mean()],
                    ['平均超额收益率(%)', redemption_data['超额收益率(%)'].mean()],
                ])
        
        if stats_data:
            stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
            stats_df.to_excel(writer, sheet_name='统计摘要', index=False)
    
    print(f"\nExcel文件已保存: {excel_path}")
    
    # 显示关键统计
    if not final_report.empty:
        print(f"\n=== 关键统计信息 ===")
        
        turnover_data = final_report[final_report['信号类型'] == '90分位成交额信号']
        redemption_data = final_report[final_report['信号类型'] == '申赎信号']
        
        if not turnover_data.empty:
            print(f"90分位成交额信号:")
            print(f"  参与ETF: {len(turnover_data)}个")
            print(f"  总信号: {turnover_data['总信号次数'].sum()}次")
            print(f"  总交易: {turnover_data['实际交易次数'].sum()}次")
            print(f"  平均胜率: {turnover_data['胜率(%)'].mean():.2f}%")
            print(f"  平均超额收益: {turnover_data['超额收益率(%)'].mean():.4f}%")
        
        if not redemption_data.empty:
            print(f"\n申赎信号:")
            print(f"  参与ETF: {len(redemption_data)}个")
            print(f"  总信号: {redemption_data['总信号次数'].sum()}次")
            print(f"  总交易: {redemption_data['实际交易次数'].sum()}次")
            print(f"  平均胜率: {redemption_data['胜率(%)'].mean():.2f}%")
            print(f"  平均超额收益: {redemption_data['超额收益率(%)'].mean():.4f}%")

if __name__ == "__main__":
    main()
