#!/usr/bin/env python3
"""
生成ETF实际交易明细表
包含每次交易的开始日期、结束日期和期间收益
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def calculate_trading_days_ahead(date, days=15):
    """计算交易日后的日期（跳过周末）"""
    current_date = date
    trading_days = 0
    
    while trading_days < days:
        current_date += timedelta(days=1)
        # 跳过周末
        if current_date.weekday() < 5:  # 0-4 是周一到周五
            trading_days += 1
    
    return current_date

def generate_90_percentile_trading_details():
    """生成90分位成交额信号的交易明细表"""
    print("=== 生成90分位成交额信号交易明细表 ===")
    
    try:
        # 读取原始信号数据
        signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
        signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
        signals_df['date'] = signals_df['datetime'].dt.date
        
        # 筛选90分位且通过PE筛选的信号
        filtered_signals = signals_df[
            (signals_df['pe_filter_passed'] == True) &
            (signals_df['threshold'] == 90) &
            (signals_df['holding_period'] == 'next_15day_close')
        ].copy()
        
        # 分析时间范围
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()
        
        period_signals = filtered_signals[
            (filtered_signals['date'] >= start_date) &
            (filtered_signals['date'] <= end_date)
        ].copy()
        
        # 每日每个ETF只保留一个信号（取最早的）
        daily_signals = period_signals.groupby(['etf_code', 'date']).first().reset_index()
        daily_signals = daily_signals.sort_values(['etf_code', 'date'])
        
        # 读取指数行情数据
        index_data = pd.read_excel('指数行情序列.xlsx')
        index_data['时间'] = pd.to_datetime(index_data['时间'])
        index_data['日期'] = index_data['时间'].dt.date
        # 处理代码格式
        if '代码' in index_data.columns:
            index_data['代码'] = index_data['代码'].astype(str).str.replace('.SH', '').str.replace('.SZ', '').str.zfill(6)
        
        # 读取ETF映射关系
        etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
        etf_mapping['跟踪指数代码'] = etf_mapping['跟踪指数代码'].astype(str).str.zfill(6)
        
        # 生成交易明细
        trading_details = []
        
        for etf_code, group in daily_signals.groupby('etf_code'):
            # 获取ETF对应的指数代码
            etf_info = etf_mapping[etf_mapping['ETF代码'] == etf_code]
            if etf_info.empty:
                continue
            
            index_code = etf_info.iloc[0]['跟踪指数代码']
            # ETF映射文件只有代码，没有名称，使用代码作为名称
            etf_name = f'ETF_{etf_code}'
            index_name = f'指数_{index_code}'
            
            # 获取指数价格数据
            index_prices = index_data[index_data['代码'] == index_code].copy()
            if index_prices.empty and index_code == '746059':
                index_prices = index_data[index_data['代码'] == '746059.MSI'].copy()
            
            if index_prices.empty:
                continue
            
            index_prices = index_prices.sort_values('日期')
            
            # 处理每个信号
            signal_dates = sorted(group['date'].tolist())
            
            for i, signal_date in enumerate(signal_dates):
                # 计算结束日期（15个交易日后）
                end_date_calc = calculate_trading_days_ahead(signal_date, 15)
                
                # 获取开始和结束价格
                start_price_data = index_prices[index_prices['日期'] == signal_date]
                end_price_data = index_prices[index_prices['日期'] == end_date_calc]
                
                # 如果没有找到确切日期，找最近的交易日
                if start_price_data.empty:
                    start_price_data = index_prices[index_prices['日期'] >= signal_date]
                    if not start_price_data.empty:
                        start_price_data = start_price_data.iloc[0:1]
                
                if end_price_data.empty:
                    end_price_data = index_prices[index_prices['日期'] <= end_date_calc]
                    if not end_price_data.empty:
                        end_price_data = end_price_data.iloc[-1:]
                
                if not start_price_data.empty and not end_price_data.empty:
                    start_price = start_price_data.iloc[0]['开盘价(元)']
                    end_price = end_price_data.iloc[0]['收盘价(元)']
                    actual_start_date = start_price_data.iloc[0]['日期']
                    actual_end_date = end_price_data.iloc[0]['日期']
                    
                    if pd.notna(start_price) and pd.notna(end_price) and start_price > 0:
                        return_rate = (end_price - start_price) / start_price * 100
                        
                        trading_details.append({
                            'ETF代码': etf_code,
                            'ETF名称': etf_name,
                            '跟踪指数': index_code,
                            '指数名称': index_name,
                            '交易序号': i + 1,
                            '信号日期': signal_date,
                            '实际开始日期': actual_start_date,
                            '实际结束日期': actual_end_date,
                            '持有天数': (actual_end_date - actual_start_date).days,
                            '开始价格': round(start_price, 4),
                            '结束价格': round(end_price, 4),
                            '期间收益率(%)': round(return_rate, 4)
                        })
        
        # 转换为DataFrame
        trading_df = pd.DataFrame(trading_details)
        trading_df = trading_df.sort_values(['ETF代码', '交易序号'])
        
        print(f"生成90分位交易明细: {len(trading_df)} 条记录")
        return trading_df
        
    except Exception as e:
        print(f"生成90分位交易明细时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def generate_redemption_trading_details():
    """生成申赎信号的交易明细表"""
    print("\n=== 生成申赎信号交易明细表 ===")
    
    try:
        # 读取申赎信号数据
        signals_df = pd.read_csv('分析结果_PE筛选_20240430_20250430/PE筛选后信号数据汇总表.csv')
        signals_df['日期'] = pd.to_datetime(signals_df['日期'])
        
        # 分析时间范围
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()
        
        # 筛选signal_1_buy_pe_filtered信号
        period_signals = signals_df[
            (signals_df['日期'].dt.date >= start_date) &
            (signals_df['日期'].dt.date <= end_date) &
            (signals_df['PE筛选后信号1_买入'] == 1)
        ].copy()
        
        period_signals['ETF代码_数字'] = period_signals['ETF代码'].str.split('.').str[0].astype(int)
        period_signals['日期_date'] = period_signals['日期'].dt.date
        period_signals = period_signals.sort_values(['ETF代码_数字', '日期'])
        
        # 读取指数行情数据
        index_data = pd.read_excel('指数行情序列.xlsx')
        index_data['时间'] = pd.to_datetime(index_data['时间'])
        index_data['日期'] = index_data['时间'].dt.date
        # 处理代码格式
        if '代码' in index_data.columns:
            index_data['代码'] = index_data['代码'].astype(str).str.replace('.SH', '').str.replace('.SZ', '').str.zfill(6)
        
        # 读取ETF映射关系
        etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
        etf_mapping['跟踪指数代码'] = etf_mapping['跟踪指数代码'].astype(str).str.zfill(6)
        
        # 生成交易明细
        trading_details = []
        
        for etf_code, group in period_signals.groupby('ETF代码_数字'):
            # 获取ETF对应的指数代码
            etf_info = etf_mapping[etf_mapping['ETF代码'] == etf_code]
            if etf_info.empty:
                continue
            
            index_code = etf_info.iloc[0]['跟踪指数代码']
            # ETF映射文件只有代码，没有名称，使用代码作为名称
            etf_name = f'ETF_{etf_code}'
            index_name = f'指数_{index_code}'
            
            # 获取指数价格数据
            index_prices = index_data[index_data['代码'] == index_code].copy()
            if index_prices.empty and index_code == '746059':
                index_prices = index_data[index_data['代码'] == '746059.MSI'].copy()
            
            if index_prices.empty:
                continue
            
            index_prices = index_prices.sort_values('日期')
            
            # 处理每个信号
            signal_dates = sorted(group['日期_date'].tolist())
            
            for i, signal_date in enumerate(signal_dates):
                # 计算结束日期（15个交易日后）
                end_date_calc = calculate_trading_days_ahead(signal_date, 15)
                
                # 获取开始和结束价格
                start_price_data = index_prices[index_prices['日期'] == signal_date]
                end_price_data = index_prices[index_prices['日期'] == end_date_calc]
                
                # 如果没有找到确切日期，找最近的交易日
                if start_price_data.empty:
                    start_price_data = index_prices[index_prices['日期'] >= signal_date]
                    if not start_price_data.empty:
                        start_price_data = start_price_data.iloc[0:1]
                
                if end_price_data.empty:
                    end_price_data = index_prices[index_prices['日期'] <= end_date_calc]
                    if not end_price_data.empty:
                        end_price_data = end_price_data.iloc[-1:]
                
                if not start_price_data.empty and not end_price_data.empty:
                    start_price = start_price_data.iloc[0]['开盘价(元)']
                    end_price = end_price_data.iloc[0]['收盘价(元)']
                    actual_start_date = start_price_data.iloc[0]['日期']
                    actual_end_date = end_price_data.iloc[0]['日期']
                    
                    if pd.notna(start_price) and pd.notna(end_price) and start_price > 0:
                        return_rate = (end_price - start_price) / start_price * 100
                        
                        trading_details.append({
                            'ETF代码': etf_code,
                            'ETF名称': etf_name,
                            '跟踪指数': index_code,
                            '指数名称': index_name,
                            '交易序号': i + 1,
                            '信号日期': signal_date,
                            '实际开始日期': actual_start_date,
                            '实际结束日期': actual_end_date,
                            '持有天数': (actual_end_date - actual_start_date).days,
                            '开始价格': round(start_price, 4),
                            '结束价格': round(end_price, 4),
                            '期间收益率(%)': round(return_rate, 4)
                        })
        
        # 转换为DataFrame
        trading_df = pd.DataFrame(trading_details)
        trading_df = trading_df.sort_values(['ETF代码', '交易序号'])
        
        print(f"生成申赎信号交易明细: {len(trading_df)} 条记录")
        return trading_df
        
    except Exception as e:
        print(f"生成申赎信号交易明细时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def main():
    """主函数"""
    print("=== ETF实际交易明细表生成工具 ===")
    
    # 生成90分位成交额信号交易明细
    turnover_details = generate_90_percentile_trading_details()
    
    # 生成申赎信号交易明细
    redemption_details = generate_redemption_trading_details()
    
    # 保存Excel文件
    if not turnover_details.empty or not redemption_details.empty:
        with pd.ExcelWriter('ETF实际交易明细表.xlsx', engine='xlsxwriter') as writer:
            if not turnover_details.empty:
                turnover_details.to_excel(writer, sheet_name='90分位成交额信号交易明细', index=False)
                print(f"90分位成交额信号交易明细已保存: {len(turnover_details)} 条记录")
            
            if not redemption_details.empty:
                redemption_details.to_excel(writer, sheet_name='申赎信号交易明细', index=False)
                print(f"申赎信号交易明细已保存: {len(redemption_details)} 条记录")
        
        print(f"\nExcel文件已保存: ETF实际交易明细表.xlsx")
        
        # 显示统计信息
        if not turnover_details.empty:
            print(f"\n90分位成交额信号统计:")
            print(f"  总交易次数: {len(turnover_details)}")
            print(f"  涉及ETF数量: {turnover_details['ETF代码'].nunique()}")
            print(f"  平均收益率: {turnover_details['期间收益率(%)'].mean():.4f}%")
            print(f"  胜率: {(turnover_details['期间收益率(%)'] > 0).mean() * 100:.2f}%")
        
        if not redemption_details.empty:
            print(f"\n申赎信号统计:")
            print(f"  总交易次数: {len(redemption_details)}")
            print(f"  涉及ETF数量: {redemption_details['ETF代码'].nunique()}")
            print(f"  平均收益率: {redemption_details['期间收益率(%)'].mean():.4f}%")
            print(f"  胜率: {(redemption_details['期间收益率(%)'] > 0).mean() * 100:.2f}%")
    else:
        print("未能生成有效的交易明细数据")

if __name__ == "__main__":
    main()
