#!/usr/bin/env python3
"""
将申赎买入信号数据按跟踪指数汇总整理
输出格式：
- A列：日期（从上证指数行情序列.xlsx读取）
- B列开始：各跟踪指数代码列，数值为当日该指数下发出信号的ETF数量
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def standardize_etf_code(code):
    """标准化ETF代码，去除后缀"""
    if pd.isna(code):
        return None
    
    code_str = str(code)
    # 去除常见的后缀
    suffixes = ['.SH', '.SZ', '.XSHE', '.XSHG']
    for suffix in suffixes:
        if code_str.endswith(suffix):
            code_str = code_str[:-len(suffix)]
    
    # 确保是6位数字格式
    try:
        return int(code_str)
    except:
        return None

def load_trading_days_from_index():
    """从上证指数行情序列.xlsx读取交易日"""
    print(f"=== 从上证指数行情序列.xlsx读取交易日 ===")
    
    try:
        # 读取上证指数行情数据
        index_df = pd.read_excel('上证指数行情序列.xlsx')
        
        print(f"上证指数数据行数: {len(index_df)}")
        print(f"列名: {index_df.columns.tolist()}")
        
        # 获取日期列
        if '日期' in index_df.columns:
            dates = pd.to_datetime(index_df['日期'])
        elif 'date' in index_df.columns:
            dates = pd.to_datetime(index_df['date'])
        else:
            print("未找到日期列")
            return []
        
        # 筛选2024-4-30至2025-4-30的交易日
        start_date = pd.to_datetime('2024-04-30')
        end_date = pd.to_datetime('2025-04-30')
        
        filtered_dates = dates[(dates >= start_date) & (dates <= end_date)]
        trading_days = [date.date() for date in filtered_dates]
        
        print(f"筛选后交易日数量: {len(trading_days)}")
        print(f"起始交易日: {trading_days[0]}")
        print(f"结束交易日: {trading_days[-1]}")
        
        return trading_days
        
    except Exception as e:
        print(f"读取上证指数行情数据时出错: {e}")
        # 如果读取失败，生成默认交易日
        print("使用默认方法生成交易日")
        return generate_default_trading_days()

def generate_default_trading_days():
    """生成默认交易日列表（排除周末）"""
    print(f"=== 生成默认交易日列表 ===")
    
    start = pd.to_datetime('2024-04-30')
    end = pd.to_datetime('2025-04-30')
    
    # 生成日期范围
    date_range = pd.date_range(start=start, end=end, freq='D')
    
    # 排除周末（周六=5，周日=6）
    trading_days = [date for date in date_range if date.weekday() < 5]
    
    print(f"默认交易日数量: {len(trading_days)}")
    
    return [date.date() for date in trading_days]

def load_etf_index_mapping():
    """加载ETF与跟踪指数的映射关系"""
    print(f"\n=== 加载ETF跟踪指数映射关系 ===")
    
    try:
        etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
        
        print(f"ETF映射数据行数: {len(etf_mapping)}")
        print(f"列名: {etf_mapping.columns.tolist()}")
        
        # 创建ETF到指数的映射字典
        etf_to_index = {}
        for _, row in etf_mapping.iterrows():
            etf_code = row['ETF代码']
            index_code = row['跟踪指数代码']
            etf_to_index[etf_code] = index_code
        
        print(f"ETF到指数映射关系: {len(etf_to_index)} 个")
        
        # 显示指数分组
        index_groups = etf_mapping.groupby('跟踪指数代码')['ETF代码'].apply(list).to_dict()
        print(f"指数分组情况:")
        for index_code, etf_list in index_groups.items():
            print(f"  指数 {index_code}: {etf_list}")
        
        return etf_to_index, sorted(index_groups.keys())
        
    except Exception as e:
        print(f"加载ETF映射关系时出错: {e}")
        return {}, []

def load_and_process_redemption_signals():
    """加载并处理PE筛选申赎买入信号数据"""
    print(f"\n=== 加载PE筛选申赎买入信号数据 ===")

    try:
        # 读取PE筛选申赎买入信号数据
        signals_df = pd.read_csv('分析结果_PE筛选_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv')
        
        print(f"原始数据行数: {len(signals_df)}")
        print(f"列名: {signals_df.columns.tolist()}")
        
        # 转换时间格式
        if 'signal_date' in signals_df.columns:
            signals_df['signal_date'] = pd.to_datetime(signals_df['signal_date'])
            signals_df['date'] = signals_df['signal_date'].dt.date
        else:
            print("未找到signal_date列")
            return pd.DataFrame()
        
        # 标准化ETF代码
        if 'etf_code' in signals_df.columns:
            signals_df['etf_code_std'] = signals_df['etf_code'].apply(standardize_etf_code)
        else:
            print("未找到etf_code列")
            return pd.DataFrame()
        
        signals_df = signals_df.dropna(subset=['etf_code_std'])
        
        print(f"标准化后信号数: {len(signals_df)}")
        
        # 统计各ETF的信号数量
        etf_counts = signals_df['etf_code_std'].value_counts().sort_index()
        print(f"涉及ETF数量: {len(etf_counts)}")
        print(f"各ETF信号数量:")
        for etf_code, count in etf_counts.items():
            print(f"  ETF {etf_code}: {count}个")
        
        return signals_df
        
    except Exception as e:
        print(f"加载申赎买入信号数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def create_index_signal_matrix(trading_days, index_list, etf_to_index, signals_df):
    """创建按指数汇总的信号矩阵（计算ETF数量）"""
    print(f"\n=== 创建按指数汇总的信号矩阵 ===")
    
    # 为信号数据添加指数代码
    signals_df['index_code'] = signals_df['etf_code_std'].map(etf_to_index)
    mapped_signals = signals_df.dropna(subset=['index_code'])
    
    print(f"成功映射到指数的信号数: {len(mapped_signals)}")
    
    # 每日每个ETF只保留一个信号记录（去重）
    daily_etf_signals = mapped_signals.groupby(['date', 'etf_code_std', 'index_code']).first().reset_index()
    
    # 按日期和指数汇总ETF数量（即发出信号的ETF个数）
    daily_index_signals = daily_etf_signals.groupby(['date', 'index_code']).size().reset_index()
    daily_index_signals.columns = ['date', 'index_code', 'etf_count']
    
    print(f"每日指数信号统计行数: {len(daily_index_signals)}")
    
    # 创建基础矩阵
    result_data = []
    
    for date in trading_days:
        row_data = {'日期': date}
        
        # 获取该日期的所有指数信号
        date_signals = daily_index_signals[daily_index_signals['date'] == date]
        
        # 为每个指数填充ETF数量
        for index_code in index_list:
            index_signals = date_signals[date_signals['index_code'] == index_code]
            
            if not index_signals.empty:
                # 如果有信号，记录发出信号的ETF数量
                etf_count = index_signals['etf_count'].sum()
                row_data[str(index_code)] = etf_count
            else:
                # 如果没有信号，保持为空（NaN）
                row_data[str(index_code)] = np.nan
        
        result_data.append(row_data)
    
    # 转换为DataFrame
    result_df = pd.DataFrame(result_data)
    
    # 将日期列转换为字符串格式
    result_df['日期'] = result_df['日期'].apply(lambda x: x.strftime('%Y-%m-%d'))
    
    print(f"结果矩阵形状: {result_df.shape}")
    print(f"列名: {result_df.columns.tolist()}")
    
    # 统计信号分布
    total_etf_signals = 0
    signal_days = 0
    
    for col in result_df.columns[1:]:  # 跳过日期列
        col_signals = result_df[col].dropna()
        if not col_signals.empty:
            total_etf_signals += col_signals.sum()
            signal_days += len(col_signals)
    
    print(f"总ETF信号数量: {total_etf_signals}")
    print(f"有信号的指数-日期组合: {signal_days}")
    
    return result_df

def save_results(result_df, etf_to_index):
    """保存结果"""
    print(f"\n=== 保存结果 ===")
    
    # 保存为Excel文件
    excel_path = 'PE筛选申赎买入信号按指数汇总表_2024-2025.xlsx'
    
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        result_df.to_excel(writer, sheet_name='PE筛选申赎买入信号按指数汇总', index=False)
        
        # 获取工作表和工作簿对象
        workbook = writer.book
        worksheet = writer.sheets['PE筛选申赎买入信号按指数汇总']
        
        # 设置列宽
        worksheet.set_column('A:A', 12)  # 日期列
        for i in range(1, len(result_df.columns)):
            worksheet.set_column(i, i, 10)  # 指数列
        
        # 设置数字格式（整数）
        number_format = workbook.add_format({'num_format': '0'})
        for i in range(1, len(result_df.columns)):
            worksheet.set_column(i, i, 10, number_format)
        
        # 添加指数统计信息sheet
        stats_data = []
        
        # 计算各指数的统计信息
        for col in result_df.columns[1:]:
            index_code = col
            index_signals = result_df[col].dropna()
            
            if not index_signals.empty:
                total_etf_signals = index_signals.sum()
                signal_days = len(index_signals)
                avg_etfs_per_day = index_signals.mean()
                max_etfs_per_day = index_signals.max()
                min_etfs_per_day = index_signals.min()
                
                # 获取该指数下的ETF列表
                etf_list = [etf for etf, idx in etf_to_index.items() if idx == int(index_code)]
            else:
                total_etf_signals = 0
                signal_days = 0
                avg_etfs_per_day = 0
                max_etfs_per_day = 0
                min_etfs_per_day = 0
                etf_list = []
            
            stats_data.append({
                '指数代码': index_code,
                '总ETF信号数': total_etf_signals,
                '有信号天数': signal_days,
                '平均每日ETF数': round(avg_etfs_per_day, 2),
                '最大单日ETF数': max_etfs_per_day,
                '最小单日ETF数': min_etfs_per_day,
                '跟踪ETF数量': len(etf_list),
                '跟踪ETF列表': ', '.join(map(str, etf_list))
            })
        
        stats_df = pd.DataFrame(stats_data)
        stats_df = stats_df.sort_values('总ETF信号数', ascending=False)
        stats_df.to_excel(writer, sheet_name='指数信号统计', index=False)
        
        # 添加ETF到指数映射表
        mapping_data = []
        for etf_code, index_code in etf_to_index.items():
            mapping_data.append({
                'ETF代码': etf_code,
                '跟踪指数代码': index_code
            })
        
        mapping_df = pd.DataFrame(mapping_data)
        mapping_df = mapping_df.sort_values('跟踪指数代码')
        mapping_df.to_excel(writer, sheet_name='ETF指数映射', index=False)
        
        # 添加日期统计sheet
        date_stats = []
        for _, row in result_df.iterrows():
            date = row['日期']
            daily_total_etfs = 0
            active_indices = 0
            
            for col in result_df.columns[1:]:
                if not pd.isna(row[col]):
                    daily_total_etfs += row[col]
                    active_indices += 1
            
            if daily_total_etfs > 0:
                date_stats.append({
                    '日期': date,
                    '总ETF信号数': daily_total_etfs,
                    '活跃指数数': active_indices,
                    '平均每指数ETF数': round(daily_total_etfs / active_indices, 2) if active_indices > 0 else 0
                })
        
        if date_stats:
            date_stats_df = pd.DataFrame(date_stats)
            date_stats_df = date_stats_df.sort_values('总ETF信号数', ascending=False)
            date_stats_df.to_excel(writer, sheet_name='日期信号统计', index=False)
    
    print(f"Excel文件已保存: {excel_path}")
    
    # 生成简要报告
    report_content = f"""# PE筛选申赎买入信号按指数汇总报告

## 数据概述

**处理时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**数据源**: 分析结果_PE筛选_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv
**信号类型**: PE筛选申赎买入信号
**交易日来源**: 上证指数行情序列.xlsx

## 输出表格结构

### 主表：PE筛选申赎买入信号按指数汇总
- **A列**: 日期（从上证指数行情序列.xlsx读取的交易日）
- **B列开始**: 各跟踪指数代码列，数值为当日该指数下发出信号的ETF数量
- **汇总逻辑**: 同一指数下发出信号的ETF个数求和

### 统计表格
1. **指数信号统计**: 各指数的ETF信号汇总统计和跟踪ETF信息
2. **ETF指数映射**: ETF与跟踪指数的对应关系
3. **日期信号统计**: 各交易日的ETF信号分布统计

## 数据统计

- **交易日总数**: {len(result_df)} 天
- **跟踪指数数量**: {len(result_df.columns) - 1} 个
- **总ETF信号数**: {sum([result_df[col].dropna().sum() for col in result_df.columns[1:]])} 个
- **有信号的交易日**: {len([row for _, row in result_df.iterrows() if any(not pd.isna(row[col]) for col in result_df.columns[1:])])} 天

## 主要指数ETF信号分布

"""
    
    # 添加前10个指数的统计
    stats_df = pd.DataFrame(stats_data).sort_values('总ETF信号数', ascending=False)
    
    for i, (_, row) in enumerate(stats_df.head(10).iterrows(), 1):
        report_content += f"{i}. **指数 {row['指数代码']}**: {row['总ETF信号数']}个ETF信号, {row['有信号天数']}天, 跟踪{row['跟踪ETF数量']}个ETF\n"
    
    report_content += f"""

## 指数ETF映射关系

"""
    
    # 按指数分组显示ETF
    index_groups = {}
    for etf_code, index_code in etf_to_index.items():
        if index_code not in index_groups:
            index_groups[index_code] = []
        index_groups[index_code].append(etf_code)
    
    for index_code in sorted(index_groups.keys()):
        etf_list = sorted(index_groups[index_code])
        report_content += f"- **指数 {index_code}**: {', '.join(map(str, etf_list))} ({len(etf_list)}个ETF)\n"
    
    report_content += f"""

## 使用说明

1. **数据格式**: Excel文件，包含4个工作表
2. **主要用途**: 指数层面的申赎信号分析、指数选择、ETF配置研究
3. **注意事项**: 
   - 数值表示当日该指数下发出申赎买入信号的ETF数量
   - 空值表示当日该指数下无ETF发出信号
   - 交易日基于上证指数行情数据

## 分析建议

1. **指数活跃度**: 关注ETF信号数量多的指数
2. **ETF参与度**: 分析指数下ETF的参与程度
3. **信号同步性**: 研究同一指数下多个ETF同时发出信号的情况
4. **时间趋势**: 分析指数ETF信号的时间分布特征

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存报告
    report_path = 'PE筛选申赎买入信号按指数汇总报告.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"报告已保存: {report_path}")
    
    return excel_path, report_path

def main():
    """主函数"""
    print("=== 申赎买入信号按指数汇总数据整理 ===")
    print(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 从上证指数行情序列.xlsx读取交易日
    trading_days = load_trading_days_from_index()
    
    if not trading_days:
        print("无法获取交易日数据，程序终止")
        return
    
    # 加载ETF与指数映射关系
    etf_to_index, index_list = load_etf_index_mapping()
    
    if not etf_to_index:
        print("无法获取ETF指数映射关系，程序终止")
        return
    
    # 加载申赎买入信号数据
    signals_df = load_and_process_redemption_signals()
    
    if signals_df.empty:
        print("申赎买入信号数据为空，无法继续处理")
        return
    
    # 创建按指数汇总的信号矩阵
    result_df = create_index_signal_matrix(trading_days, index_list, etf_to_index, signals_df)
    
    # 保存结果
    excel_path, report_path = save_results(result_df, etf_to_index)
    
    # 显示完成信息
    print(f"\n=== 处理完成 ===")
    print(f"输出文件: {excel_path}")
    print(f"报告文件: {report_path}")
    print(f"数据维度: {result_df.shape[0]} 个交易日 × {result_df.shape[1]-1} 个指数")
    
    # 显示数据预览
    print(f"\n=== 数据预览 ===")
    print("前5行数据:")
    print(result_df.head().to_string(index=False))
    
    print(f"\n后5行数据:")
    print(result_df.tail().to_string(index=False))
    
    # 显示指数ETF信号统计
    print(f"\n=== 指数ETF信号统计摘要 ===")
    for col in result_df.columns[1:]:
        col_signals = result_df[col].dropna()
        if not col_signals.empty:
            total_etf_signals = col_signals.sum()
            signal_days = len(col_signals)
            print(f"指数 {col}: {total_etf_signals}个ETF信号, {signal_days}天有信号")

if __name__ == "__main__":
    main()
