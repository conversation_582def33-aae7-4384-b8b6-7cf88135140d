# -*- coding: utf-8 -*-
"""
ETF成交额分位数回测系统 (5分钟级别)

该脚本实现了基于ETF 5分钟级数据的成交额分位数回测策略：
1. 将1分钟数据聚合为5分钟数据（开盘价、最高价、最低价、收盘价、成交额总和）
2. 计算当前5分钟周期成交额在前九十个交易日全部5分钟级成交额数据中的分位数
3. 根据不同分位数阈值(80%, 90%, 95%)生成买入信号
4. 计算不同持有周期的收益情况
5. 信号生成时避开每日开盘后两个5分钟周期 (9:30-9:35, 9:35-9:40) 和收盘前两个5分钟周期 (14:50-14:55, 14:55-15:00)
"""

import os
import sys
import pandas as pd
import numpy as np
import time
import importlib
from concurrent.futures import ProcessPoolExecutor
from collections import defaultdict

# 检查必要的模块是否已安装
def check_required_modules():
    """检查必要的Python模块是否已安装"""
    required_modules = {
        'pandas': 'pd',
        'numpy': 'np',
        'xlsxwriter': None,  # 用于Excel输出
        'openpyxl': None,    # pandas读取Excel可能需要
    }
    
    missing_modules = []
    
    for module_name, alias in required_modules.items():
        try:
            if alias:
                # 如果已经导入并有别名，则检查别名
                if alias not in globals():
                    importlib.import_module(module_name)
                    print(f"模块 {module_name} 已安装但未正确导入，现已导入")
            else:
                # 尝试导入模块
                importlib.import_module(module_name)
        except ImportError:
            missing_modules.append(module_name)
    
    if missing_modules:
        print("错误: 缺少以下必要的Python模块:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n请使用以下命令安装缺失的模块:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    return True

# 设置pandas显示选项
pd.set_option('display.max_rows', 100)
pd.set_option('display.max_columns', 20)
pd.set_option('display.width', 1000)

# 数据文件夹路径
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), '分钟级数据')

# 持有周期定义
HOLDING_PERIODS = {
    '1min': 1,
    '2min': 2,
    '3min': 3,
    '5min': 5,
    '10min': 10,
    'next_day_open': 'next_day_open',
    'next_day_close': 'next_day_close',
    'next_2day_open': 'next_2day_open',
    'next_2day_close': 'next_2day_close',
    'next_3day_close': 'next_3day_close',
    'next_5day_close': 'next_5day_close',
    'next_10day_close': 'next_10day_close',
    'next_15day_close': 'next_15day_close',
    'next_30day_close': 'next_30day_close'
}

# 分位数阈值
PERCENTILE_THRESHOLDS = [80, 90, 95]

def format_percentage(value, decimals=2, default_na_str="NaN"):
    """Helper function to format percentage values, handling NaNs."""
    if pd.isna(value):
        return default_na_str
    return f"{value:.{decimals}f}%"

def aggregate_to_5min(df):
    """
    将1分钟数据聚合为5分钟数据
    
    Args:
        df: 包含1分钟数据的DataFrame
        
    Returns:
        聚合后的5分钟数据DataFrame
    """
    if df.empty:
        return pd.DataFrame()

    df = df.set_index('datetime')
    # 定义聚合规则
    agg_rules = {
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum',
        'turnover': 'sum',
        'turnover_rate': 'sum', # 或者根据需要调整成交额占比的聚合方式
        'date': 'first', # 保留日期信息
        'time': 'first'  # 保留每个5分钟周期的起始时间
    }
    
    # 按5分钟重采样并聚合
    df_5min = df.resample('5min').agg(agg_rules)
    
    # 移除没有交易的5分钟K线 (例如午休时间)
    df_5min = df_5min.dropna(subset=['open'])
    
    # 重置索引，并将datetime作为列
    df_5min = df_5min.reset_index()
    
    # 更新time列为5分钟周期的起始时间
    df_5min['time'] = df_5min['datetime'].dt.time
    df_5min['date'] = df_5min['datetime'].dt.date # 确保date列也正确
    
    return df_5min

def load_etf_data(file_path):
    """
    加载ETF分钟级数据并聚合到5分钟级别
    
    Args:
        file_path: ETF数据文件路径
        
    Returns:
        处理后的ETF 5分钟数据DataFrame
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 确保列名正确
        if len(df.columns) >= 8:
            # 设置列名
            df.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume', 'turnover', 'turnover_rate']
            
            # 确保datetime列为datetime类型
            df['datetime'] = pd.to_datetime(df['datetime'])
            
            # 提取日期和时间信息
            df['date'] = df['datetime'].dt.date
            df['time'] = df['datetime'].dt.time
            
            # 按日期和时间排序
            df = df.sort_values(['date', 'time'])
            
            # 聚合到5分钟级别
            df_5min = aggregate_to_5min(df)
            return df_5min
        else:
            print(f"警告: {file_path} 列数不足")
            return None
    except Exception as e:
        print(f"加载 {file_path} 时出错: {e}")
        return None

def calculate_percentile(current_value, historical_values):
    """
    计算当前值在历史值中的分位数
    
    Args:
        current_value: 当前值
        historical_values: 历史值列表
        
    Returns:
        分位数(0-100)
    """
    if np.isnan(current_value) or len(historical_values) == 0:
        return np.nan
    
    # 移除历史值中的NaN
    historical_values = historical_values[~np.isnan(historical_values)]
    
    if len(historical_values) == 0:
        return np.nan
    
    # 计算分位数
    percentile = 100 * (np.sum(historical_values < current_value) / len(historical_values))
    return percentile

def get_previous_trading_days(df, current_date, n_days=3):
    """
    获取当前日期前n个交易日
    
    Args:
        df: ETF数据DataFrame
        current_date: 当前日期
        n_days: 前n个交易日
        
    Returns:
        前n个交易日列表
    """
    all_dates = sorted(df['date'].unique())
    current_idx = all_dates.index(current_date) if current_date in all_dates else -1
    
    if current_idx < n_days:
        return all_dates[:current_idx]
    else:
        return all_dates[current_idx-n_days:current_idx]

def calculate_returns(df, entry_indices, holding_period):
    """
    计算给定买入点和持有周期的收益率
    
    Args:
        df: ETF数据DataFrame
        entry_indices: 买入点索引列表
        holding_period: 持有周期
        
    Returns:
        收益率列表
    """
    returns = []
    dates = df['date'].unique()
    
    for idx in entry_indices:
        if idx + 1 >= len(df):  # 确保有下一分钟数据用于买入
            continue
            
        entry_price = df.iloc[idx + 1]['open']  # 下一分钟开盘价买入
        if np.isnan(entry_price):
            continue
            
        # 当日分钟级持有
        if isinstance(holding_period, int):
            if idx + holding_period + 1 < len(df) and df.iloc[idx]['date'] == df.iloc[idx + holding_period]['date']:
                exit_price = df.iloc[idx + holding_period]['close']
                if not np.isnan(exit_price):
                    returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})
        # 跨日持有
        else:
            entry_date = df.iloc[idx]['date']
            entry_date_idx = np.where(dates == entry_date)[0][0]
            
            if holding_period == 'next_day_open' and entry_date_idx + 1 < len(dates):
                next_day = dates[entry_date_idx + 1]
                next_day_data = df[df['date'] == next_day]
                if not next_day_data.empty:
                    exit_price = next_day_data.iloc[0]['open']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})
                        
            elif holding_period == 'next_day_close' and entry_date_idx + 1 < len(dates):
                next_day = dates[entry_date_idx + 1]
                next_day_data = df[df['date'] == next_day]
                if not next_day_data.empty:
                    exit_price = next_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})
                        
            elif holding_period == 'next_2day_open' and entry_date_idx + 2 < len(dates):
                next_2day = dates[entry_date_idx + 2]
                next_2day_data = df[df['date'] == next_2day]
                if not next_2day_data.empty:
                    exit_price = next_2day_data.iloc[0]['open']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})
                        
            elif holding_period == 'next_2day_close' and entry_date_idx + 2 < len(dates):
                next_2day = dates[entry_date_idx + 2]
                next_2day_data = df[df['date'] == next_2day]
                if not next_2day_data.empty:
                    exit_price = next_2day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})
            
            # 新增持有期逻辑
            elif holding_period == 'next_3day_close' and entry_date_idx + 3 < len(dates):
                future_day = dates[entry_date_idx + 3]
                future_day_data = df[df['date'] == future_day]
                if not future_day_data.empty:
                    exit_price = future_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})
            elif holding_period == 'next_5day_close' and entry_date_idx + 5 < len(dates):
                future_day = dates[entry_date_idx + 5]
                future_day_data = df[df['date'] == future_day]
                if not future_day_data.empty:
                    exit_price = future_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})
            elif holding_period == 'next_10day_close' and entry_date_idx + 10 < len(dates):
                future_day = dates[entry_date_idx + 10]
                future_day_data = df[df['date'] == future_day]
                if not future_day_data.empty:
                    exit_price = future_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})
            elif holding_period == 'next_15day_close' and entry_date_idx + 15 < len(dates):
                future_day = dates[entry_date_idx + 15]
                future_day_data = df[df['date'] == future_day]
                if not future_day_data.empty:
                    exit_price = future_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})
            elif holding_period == 'next_30day_close' and entry_date_idx + 30 < len(dates):
                future_day = dates[entry_date_idx + 30]
                future_day_data = df[df['date'] == future_day]
                if not future_day_data.empty:
                    exit_price = future_day_data.iloc[-1]['close']
                    if not np.isnan(exit_price):
                        returns.append({'entry_price': entry_price, 'exit_price': exit_price, 'return_value': (exit_price / entry_price) - 1})
    
    return returns

def backtest_single_etf(file_path):
    """
    对单个ETF进行回测
    
    Args:
        file_path: ETF数据文件路径
        
    Returns:
        回测结果字典
    """
    # 提取ETF代码
    etf_code = os.path.basename(file_path).split('分钟级数据')[0]
    print(f"正在回测 {etf_code}...")
    
    # 加载数据
    df = load_etf_data(file_path)
    if df is None or df.empty:
        print(f"无法加载 {etf_code} 的数据")
        return {}
    
    # 回测结果
    results = {}
    detailed_signals_list = [] # 用于存储详细信号
    
    # 按日期分组处理
    all_dates = sorted(df['date'].unique())
    
    # 从第91个交易日开始回测(确保有90天历史数据)
    for date_idx, current_date in enumerate(all_dates):
        if date_idx < 90:  # 跳过前90个交易日
            continue
            
        # 获取前90个交易日
        prev_days = all_dates[date_idx-90:date_idx]
        
        # 当日数据
        current_day_data = df[df['date'] == current_date]
        
        # 前90天数据
        prev_days_data = df[df['date'].isin(prev_days)]
        
        # 对当日每个5分钟K线进行回测
        for i, row in current_day_data.iterrows(): # current_day_data 现在是5分钟级别
            current_turnover = row['turnover']
            
            # 跳过NaN值
            if np.isnan(current_turnover):
                continue
                
            # 获取当前5分钟K线的起始时间
            current_kline_time = row['time'] 
            
            # 剔除开盘后两个5分钟周期 (9:30-9:35, 9:35-9:40) 和 收盘前两个5分钟周期 (14:50-14:55, 14:55-15:00) 的数据
            # 对应的5分钟K线起始时间为 9:30, 9:35 和 14:50, 14:55
            time_9_30 = pd.Timestamp('09:30:00').time()
            time_9_35 = pd.Timestamp('09:35:00').time()
            time_14_50 = pd.Timestamp('14:50:00').time()
            time_14_55 = pd.Timestamp('14:55:00').time()

            if (current_kline_time == time_9_30 or current_kline_time == time_9_35) or \
               (current_kline_time == time_14_50 or current_kline_time == time_14_55):
                continue
                
            # 计算分位数
            percentile = calculate_percentile(current_turnover, prev_days_data['turnover'].values)
            
            # 跳过NaN分位数
            if np.isnan(percentile):
                continue
                
            # 根据不同分位数阈值生成买入信号
            for threshold in PERCENTILE_THRESHOLDS:
                if percentile > threshold:
                    threshold_key = f"大于{threshold}分位"
                    
                    # 初始化结果字典
                    if threshold_key not in results:
                        results[threshold_key] = {period: [] for period in HOLDING_PERIODS.keys()}
                    
                    # 对不同持有周期计算收益
                    for period_name, period_value in HOLDING_PERIODS.items():
                        trade_details_list = calculate_returns(df, [i], period_value)
                        if trade_details_list:
                            # calculate_returns for a single index [i] returns a list with 0 or 1 elements
                            for trade_detail in trade_details_list:
                                signal_record = {
                                    'etf_code': etf_code,
                                    'datetime': df.loc[i, 'datetime'],
                                    'turnover': current_turnover,
                                    'percentile': percentile,
                                    'threshold': int(threshold_key.split('大于')[1].split('分位')[0]),
                                    'holding_period': period_name,
                                    'entry_price': trade_detail['entry_price'],
                                    'exit_price': trade_detail['exit_price'],
                                    'return': trade_detail['return_value']
                                }
                                detailed_signals_list.append(signal_record)

                                # For summary statistics (final_results)
                                if threshold_key not in results:
                                    results[threshold_key] = {p_name: [] for p_name in HOLDING_PERIODS.keys()}
                                results[threshold_key][period_name].append(trade_detail['return_value'])
    
    # 计算每个分位数阈值和持有周期的统计结果
    final_results = {}
    for threshold_key, period_results in results.items():
        final_results[threshold_key] = {}
        for period_name, returns in period_results.items():
            if returns:
                win_rate = np.sum(np.array(returns) > 0) / len(returns) * 100
                avg_return = np.mean(returns) * 100
                # Calculate Annualized Return
                annualized_return_pct = np.nan
                if returns: # Equivalent to count > 0
                    # Ensure df is available in this scope if not already
                    # Assuming df is the DataFrame for the current ETF, loaded at the start of backtest_single_etf
                    current_threshold_numeric = int(threshold_key.split('大于')[1].split('分位')[0])
                    
                    # Filter detailed_signals_list for the current strategy (threshold and period)
                    # This requires detailed_signals_list to be populated *before* this summary calculation block
                    # or to be passed/accessible here.
                    # Assuming detailed_signals_list is populated correctly before this loop.
                    strategy_signals_for_period = [
                        s for s in detailed_signals_list 
                        if s['threshold'] == current_threshold_numeric and s['holding_period'] == period_name
                    ]

                    if strategy_signals_for_period:
                        signal_datetimes = [s['datetime'] for s in strategy_signals_for_period]
                        unique_signal_days = 0
                        if signal_datetimes: # Ensure list is not empty
                            # Convert to datetime objects then extract date part for nunique
                            unique_signal_days = pd.to_datetime(pd.Series(signal_datetimes)).dt.date.nunique()
                        
                        # total_days_in_etf_data should be the number of unique trading days in the ETF's dataset
                        total_days_in_etf_data = df['date'].nunique() # df is from load_etf_data

                        if unique_signal_days > 0 and total_days_in_etf_data > 0:
                            avg_return_decimal = avg_return / 100 # avg_return is already in %
                            
                            # Estimate triggers per year
                            # Assuming 252 trading days in a year
                            triggers_per_year_estimate = (unique_signal_days / total_days_in_etf_data) * 252
                            
                            # Compound the average return per trade
                            # (1 + avg_return_per_trade) ^ (trades_per_year) - 1
                            if (1 + avg_return_decimal) > 0: # Avoid issues with log of non-positive if used, or large negative returns
                                annualized_return_value = ((1 + avg_return_decimal) ** triggers_per_year_estimate) - 1
                                annualized_return_pct = annualized_return_value * 100
                            else:
                                # Handle cases where (1 + avg_return_decimal) is not positive, e.g., return is -100% or less
                                # This might indicate a very large loss, annualized could be -100%
                                annualized_return_pct = -100.0 # Or some other appropriate value like np.nan
                
                final_results[threshold_key][period_name] = {
                    'count': len(returns),
                    'win_rate': win_rate,
                    'avg_return': avg_return,
                    'annualized_return': annualized_return_pct
                }
            else:
                final_results[threshold_key][period_name] = {
                    'count': 0,
                    'win_rate': np.nan,
                    'avg_return': np.nan,
                    'annualized_return': np.nan
                }
    
    return {etf_code: {'summary_stats': final_results, 'detailed_signals': detailed_signals_list}}

def run_backtest():
    """
    运行所有ETF的回测
    """
    start_time = time.time()
    
    # 获取所有ETF数据文件
    etf_files = [os.path.join(DATA_DIR, f) for f in os.listdir(DATA_DIR) if f.endswith('.xlsx')]
    
    # 使用多进程加速回测
    all_results = {}
    with ProcessPoolExecutor() as executor:
        results = list(executor.map(backtest_single_etf, etf_files))
        
        # 合并结果
        for result in results:
            all_results.update(result)
    
    # 准备Excel写入器和数据收集
    output_excel_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '回测结果_5min.xlsx')
    output_md_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '回测分析报告_5min.md')
    excel_writer = pd.ExcelWriter(output_excel_path, engine='xlsxwriter')
    
    all_detailed_signals_list = []
    summary_stats_list = []
    etf_summary_stats_list = []

    for etf_code, results_data in all_results.items():
        etf_summary = results_data['summary_stats']
        etf_detailed_signals = results_data['detailed_signals']
        all_detailed_signals_list.extend(etf_detailed_signals)

        for threshold, period_results in etf_summary.items():
            for period, stats in period_results.items():
                if stats['count'] > 0:
                    summary_stats_list.append({
                        'etf_code': etf_code,
                        'threshold': threshold,
                        'period': period,
                        'count': stats['count'],
                        'win_rate': stats['win_rate'],
                        'avg_return': stats['avg_return'],
                        'annualized_return': stats.get('annualized_return', np.nan) # Add annualized_return
                    })
                    etf_summary_stats_list.append({
                        'etf_code': etf_code,
                        'threshold': threshold,
                        'period': period,
                        'count': stats['count'],
                        'win_rate': stats['win_rate'],
                        'avg_return': stats['avg_return'],
                        'annualized_return': stats.get('annualized_return', np.nan) # Add annualized_return
                    })

    # 1. 所有信号明细 (保存为CSV)
    output_csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '所有信号明细_5min.csv')
    if all_detailed_signals_list:
        detailed_signals_df = pd.DataFrame(all_detailed_signals_list)
        detailed_signals_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig') # 使用utf-8-sig以确保中文正确显示
        print(f"\n所有信号明细已保存至: {output_csv_path}")
    else:
        # 创建一个空的CSV文件，如果需要的话，或者只是打印一条消息
        with open(output_csv_path, 'w', encoding='utf-8-sig') as f:
            f.write('') # 写入空内容或表头（如果需要）
        print(f"\n没有详细信号生成，已创建空的CSV文件: {output_csv_path}")

    # 2. 各ETF详细统计
    if etf_summary_stats_list:
        etf_summary_df = pd.DataFrame(etf_summary_stats_list)
        etf_summary_df.to_excel(excel_writer, sheet_name='各ETF详细统计', index=False)
    else:
        pd.DataFrame().to_excel(excel_writer, sheet_name='各ETF详细统计', index=False) # 空表

    # 3. 汇总统计结果
    overall_summary_list = []
    if summary_stats_list: # 使用收集到的 summary_stats_list 计算总体统计
        summary_df_for_overall_calc = pd.DataFrame(summary_stats_list)
        for (threshold, period), group in summary_df_for_overall_calc.groupby(['threshold', 'period']):
            if not group.empty:
                total_count = group['count'].sum()
                # 加权平均胜率和收益率 (按信号数量加权)
                weighted_win_rate = np.average(group['win_rate'], weights=group['count']) if total_count > 0 else np.nan
                weighted_avg_return = np.average(group['avg_return'], weights=group['count']) if total_count > 0 else np.nan
                
                # Weighted average for annualized_return
                # Ensure 'annualized_return' column exists and handle NaNs
                valid_annualized_returns = group.dropna(subset=['annualized_return'])
                weighted_annualized_return = np.nan
                if not valid_annualized_returns.empty and valid_annualized_returns['count'].sum() > 0:
                    weighted_annualized_return = np.average(valid_annualized_returns['annualized_return'], 
                                                            weights=valid_annualized_returns['count'])
                
                overall_summary_list.append({
                    'threshold': threshold,
                    'period': period,
                    'total_signals': total_count,
                    'avg_win_rate': weighted_win_rate,
                    'avg_return': weighted_avg_return,
                    'annualized_return': weighted_annualized_return # Add annualized_return
                })
    
    if overall_summary_list:
        overall_summary_df = pd.DataFrame(overall_summary_list)
        overall_summary_df = overall_summary_df.sort_values(by=['threshold', 'period'])
        overall_summary_df.to_excel(excel_writer, sheet_name='汇总统计结果', index=False)
    else:
        pd.DataFrame().to_excel(excel_writer, sheet_name='汇总统计结果', index=False) # 空表

    excel_writer.close()
    print(f"\n回测结果已保存至: {output_excel_path}")

    # 生成Markdown分析报告
    with open(output_md_path, 'w', encoding='utf-8') as f:
        f.write("# ETF成交额分位数回测分析报告 (5分钟级别)\n\n")
        f.write(f"**回测执行时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}  \n")
        f.write(f"**数据文件夹**: {DATA_DIR}  \n")
        f.write(f"**参与回测的ETF文件数量**: {len(etf_files)}  \n\n")

        f.write("## 汇总统计结果\n\n")
        if overall_summary_list:
            overall_report_df = pd.DataFrame(overall_summary_list)
            overall_report_df = overall_report_df.sort_values(by=['threshold', 'period'])
            
            # 创建Markdown表格头部
            f.write("| 阈值 | 持有周期 | 总信号数 | 平均胜率 | 平均收益率 | 年化收益率 |\n")
            f.write("| ---- | -------- | -------- | -------- | ---------- | ------------ |\n")
            
            for _, row in overall_report_df.iterrows():
                avg_win_rate_str = format_percentage(row['avg_win_rate'])
                avg_return_str = format_percentage(row['avg_return'], 4)
                annualized_return_str = format_percentage(row.get('annualized_return', np.nan))
                f.write(f"| {row['threshold']} | {row['period']} | {row['total_signals']} | {avg_win_rate_str} | {avg_return_str} | {annualized_return_str} |\n")
            f.write("\n")
        else:
            f.write("*无汇总统计结果可显示。*\n\n")

        f.write("## 各ETF详细统计 (部分展示)\n\n")
        if etf_summary_stats_list:
            etf_report_df = pd.DataFrame(etf_summary_stats_list)
            # 按ETF代码和阈值排序，方便查看
            etf_report_df = etf_report_df.sort_values(by=['etf_code', 'threshold', 'period'])
            # 仅展示部分结果，避免文件过大
            unique_etfs = etf_report_df['etf_code'].unique()
            
            for etf in unique_etfs[:min(len(unique_etfs), 5)]: # 最多展示5个ETF的详细信息
                f.write(f"### ETF: {etf}\n\n")
                etf_data_to_report = etf_report_df[etf_report_df['etf_code'] == etf]
                
                # 创建Markdown表格头部
                f.write("| 阈值 | 持有周期 | 信号数量 | 胜率 | 平均收益率 | 年化收益率 |\n")
                f.write("| ---- | -------- | -------- | ---- | ---------- | ------------ |\n")
                
                for _, row in etf_data_to_report.iterrows():
                    win_rate_str = format_percentage(row['win_rate'])
                    avg_return_str = format_percentage(row['avg_return'], 4)
                    annualized_return_str = format_percentage(row.get('annualized_return', np.nan))
                    f.write(f"| {row['threshold']} | {row['period']} | {row['count']} | {win_rate_str} | {avg_return_str} | {annualized_return_str} |\n")
                f.write("\n")
                
            if len(unique_etfs) > 5:
                f.write("*更多ETF的详细统计请查看Excel文件中的 '各ETF详细统计' 工作簿*\n\n")
        else:
            f.write("*无各ETF详细统计结果可显示。*\n\n")

        f.write("## 信号明细说明\n\n")
        f.write(f"所有详细的交易信号数据已保存在CSV文件 '{os.path.basename(output_csv_path)}' 中。该文件位于: {output_csv_path}\n")
        f.write(f"相关的Excel汇总数据等详细信息请参照 {os.path.basename(output_csv_path)} 和 {os.path.basename(output_excel_path)} 文件。\n\n")
        f.write("包含字段: \n\n")
        f.write("- etf_code\n- datetime\n- turnover\n- percentile\n- threshold\n- holding_period\n- entry_price\n- exit_price\n- return\n\n")
        f.write("*报告生成完毕。*\n")
    
    print(f"分析报告已保存至: {output_md_path}")

    # 移除旧的打印输出
    # 打印总体统计结果
    # print("\n===== 回测总体统计结果 =====")
    # for threshold in sorted(summary_results.keys()):
    #     print(f"\n--- {threshold} ---")
    #     for period in HOLDING_PERIODS.keys():
    #         if period in summary_results[threshold]:
    #             period_stats = summary_results[threshold][period]
    #             avg_win_rate = np.mean([stat['win_rate'] for stat in period_stats])
    #             avg_return = np.mean([stat['avg_return'] for stat in period_stats])
    #             total_count = sum([stat['count'] for stat in period_stats])
                
    #             print(f"持有周期: {period}")
    #             print(f"  信号数量: {total_count}")
    #             print(f"  平均胜率: {avg_win_rate:.2f}%")
    #             print(f"  平均收益率: {avg_return:.4f}%")
    
    # # 打印每个ETF的详细结果
    # print("\n===== 各ETF详细回测结果 =====")
    # for etf_code in sorted(all_results.keys()):
    #     print(f"\n== {etf_code} ==")
    #     etf_summary = all_results[etf_code]['summary_stats'] # Corrected to access summary_stats
    #     for threshold in sorted(etf_summary.keys()):
    #         print(f"\n--- {threshold} ---")
    #         for period in HOLDING_PERIODS.keys():
    #             if period in etf_summary[threshold]: # Check if period exists for this threshold
    #                 stats = etf_summary[threshold][period]
    #                 if stats['count'] > 0:
    #                     print(f"持有周期: {period}")
    #                     print(f"  信号数量: {stats['count']}")
    #                     print(f"  胜率: {stats['win_rate']:.2f}%")
    #                     print(f"  平均收益率: {stats['avg_return']:.4f}%")
    
    end_time = time.time()
    print(f"\n回测完成，耗时: {end_time - start_time:.2f}秒")
    
    end_time = time.time()
    print(f"\n回测完成，耗时: {end_time - start_time:.2f}秒")

if __name__ == "__main__":
    # 运行前检查必要模块是否已安装
    if check_required_modules():
        run_backtest()
    else:
        print("由于缺少必要模块，回测无法执行。请安装所需模块后重试。")