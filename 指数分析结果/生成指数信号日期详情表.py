# -*- coding: utf-8 -*-
"""
生成指数信号日期详情表
提供各指数有信号的具体日期详情，包括每日信号数量和收益率统计
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """加载数据"""
    print("正在加载数据...")
    
    # 读取信号数据
    signals_df = pd.read_csv('../回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
    signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
    signals_df['date'] = signals_df['datetime'].dt.date
    
    # 读取ETF映射数据
    etf_mapping = pd.read_excel('../市盈率信息/ETF跟踪指数.xlsx')
    etf_mapping['跟踪指数代码'] = etf_mapping['跟踪指数代码'].astype(str).str.zfill(6)
    
    return signals_df, etf_mapping

def create_index_mapping():
    """创建指数代码到指数名称的映射"""
    index_names = {
        '000010': '上证180指数',
        '000016': '上证50指数', 
        '000300': '沪深300指数',
        '000688': '科创50指数',
        '000852': '中证1000指数',
        '000905': '中证500指数',
        '000906': '中证800指数',
        '399006': '创业板指数',
        '399330': '深证100指数',
        '746059': '中证红利指数'
    }
    return index_names

def generate_date_details(signals_df, etf_mapping):
    """生成日期详情数据"""
    
    # 筛选90分位15日信号
    filtered_signals = signals_df[
        (signals_df['pe_filter_passed'] == True) &
        (signals_df['threshold'] == 90) &
        (signals_df['holding_period'] == 'next_15day_close')
    ].copy()
    
    # 合并指数信息
    signals_with_index = filtered_signals.merge(
        etf_mapping, 
        left_on='etf_code', 
        right_on='ETF代码', 
        how='left'
    )
    
    print(f"筛选后信号数: {len(signals_with_index)}")
    
    # 获取指数名称映射
    index_names = create_index_mapping()
    
    # 生成日期详情数据
    date_details = []
    
    for index_code, group in signals_with_index.groupby('跟踪指数代码'):
        if pd.isna(index_code):
            continue
            
        index_name = index_names.get(index_code, f'指数{index_code}')
        
        # 按日期统计
        for date, daily_group in group.groupby('date'):
            # 统计当日信号
            daily_signals = len(daily_group)
            
            # 统计涉及的ETF
            etfs_involved = daily_group['etf_code'].unique()
            etf_count = len(etfs_involved)
            
            # 收益率统计
            returns = daily_group['return'].dropna()
            if len(returns) > 0:
                avg_return = returns.mean() * 100
                median_return = returns.median() * 100
                win_rate = (returns > 0).sum() / len(returns) * 100
                max_return = returns.max() * 100
                min_return = returns.min() * 100
            else:
                avg_return = median_return = win_rate = max_return = min_return = 0
            
            # 获取星期几
            weekday = pd.to_datetime(date).strftime('%A')
            weekday_cn = {
                'Monday': '周一', 'Tuesday': '周二', 'Wednesday': '周三',
                'Thursday': '周四', 'Friday': '周五', 'Saturday': '周六', 'Sunday': '周日'
            }.get(weekday, weekday)
            
            date_details.append({
                '指数代码': index_code,
                '指数名称': index_name,
                '日期': date,
                '星期': weekday_cn,
                '当日信号数': daily_signals,
                '涉及ETF数': etf_count,
                '涉及ETF列表': ', '.join(map(str, sorted(etfs_involved))),
                '平均收益率(%)': round(avg_return, 4),
                '中位数收益率(%)': round(median_return, 4),
                '最大收益率(%)': round(max_return, 4),
                '最小收益率(%)': round(min_return, 4),
                '当日胜率(%)': round(win_rate, 2)
            })
    
    date_details_df = pd.DataFrame(date_details)
    date_details_df = date_details_df.sort_values(['指数名称', '日期'])
    
    return date_details_df

def generate_summary_stats(date_details_df):
    """生成汇总统计"""
    
    summary_stats = []
    
    for index_name, group in date_details_df.groupby('指数名称'):
        total_days = len(group)
        total_signals = group['当日信号数'].sum()
        avg_signals_per_day = group['当日信号数'].mean()
        
        # 收益率统计
        avg_return = group['平均收益率(%)'].mean()
        avg_win_rate = group['当日胜率(%)'].mean()
        
        # 信号强度分布
        signal_distribution = {
            '1-5个信号': len(group[(group['当日信号数'] >= 1) & (group['当日信号数'] <= 5)]),
            '6-10个信号': len(group[(group['当日信号数'] >= 6) & (group['当日信号数'] <= 10)]),
            '11-20个信号': len(group[(group['当日信号数'] >= 11) & (group['当日信号数'] <= 20)]),
            '21-50个信号': len(group[(group['当日信号数'] >= 21) & (group['当日信号数'] <= 50)]),
            '50个以上信号': len(group[group['当日信号数'] > 50])
        }
        
        # 星期分布
        weekday_distribution = group['星期'].value_counts().to_dict()
        
        summary_stats.append({
            '指数名称': index_name,
            '有信号天数': total_days,
            '总信号数': total_signals,
            '平均每日信号数': round(avg_signals_per_day, 2),
            '平均收益率(%)': round(avg_return, 4),
            '平均胜率(%)': round(avg_win_rate, 2),
            '信号强度分布': signal_distribution,
            '星期分布': weekday_distribution
        })
    
    return summary_stats

def save_to_excel(date_details_df, summary_stats):
    """保存到Excel文件"""
    
    excel_path = '指数信号日期详情表_90分位15日.xlsx'
    
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        # 日期详情表
        date_details_df.to_excel(writer, sheet_name='指数信号日期详情', index=False)
        
        # 汇总统计
        summary_data = []
        for stat in summary_stats:
            summary_data.append({
                '指数名称': stat['指数名称'],
                '有信号天数': stat['有信号天数'],
                '总信号数': stat['总信号数'],
                '平均每日信号数': stat['平均每日信号数'],
                '平均收益率(%)': stat['平均收益率(%)'],
                '平均胜率(%)': stat['平均胜率(%)']
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='日期统计汇总', index=False)
        
        # 信号强度分布
        distribution_data = []
        for stat in summary_stats:
            for range_name, count in stat['信号强度分布'].items():
                distribution_data.append({
                    '指数名称': stat['指数名称'],
                    '信号强度范围': range_name,
                    '天数': count,
                    '占比(%)': round(count / stat['有信号天数'] * 100, 2) if stat['有信号天数'] > 0 else 0
                })
        
        distribution_df = pd.DataFrame(distribution_data)
        distribution_df.to_excel(writer, sheet_name='信号强度分布', index=False)
        
        # 星期分布
        weekday_data = []
        for stat in summary_stats:
            for weekday, count in stat['星期分布'].items():
                weekday_data.append({
                    '指数名称': stat['指数名称'],
                    '星期': weekday,
                    '天数': count,
                    '占比(%)': round(count / stat['有信号天数'] * 100, 2) if stat['有信号天数'] > 0 else 0
                })
        
        weekday_df = pd.DataFrame(weekday_data)
        weekday_df.to_excel(writer, sheet_name='星期分布', index=False)
    
    print(f"Excel文件已保存: {excel_path}")
    return excel_path

def generate_markdown_report(date_details_df, summary_stats, excel_path):
    """生成Markdown报告"""
    
    total_days = len(date_details_df['日期'].unique())
    total_signals = date_details_df['当日信号数'].sum()
    date_range = f"{date_details_df['日期'].min()} 到 {date_details_df['日期'].max()}"
    
    md_content = f"""# 指数信号日期详情分析报告

## 📊 概述

**分析条件**: pe_filter_passed=TRUE且threshold=90且holding_period=next_15day_close  
**日期范围**: {date_range}  
**有信号天数**: {total_days}天  
**总信号数**: {total_signals:,}个

## 📈 各指数信号日期统计

| 指数名称 | 有信号天数 | 总信号数 | 平均每日信号数 | 平均收益率(%) | 平均胜率(%) |
|----------|------------|----------|----------------|---------------|-------------|
"""
    
    # 按总信号数排序
    summary_df = pd.DataFrame(summary_stats).sort_values('总信号数', ascending=False)
    
    for _, row in summary_df.iterrows():
        md_content += f"| {row['指数名称']} | {row['有信号天数']} | {row['总信号数']:,} | {row['平均每日信号数']} | {row['平均收益率(%)']} | {row['平均胜率(%)']} |\n"
    
    md_content += f"""

## 🗓️ 信号活跃度分析

### 最活跃的交易日
基于日期详情数据，以下是信号最多的交易日：

"""
    
    # 找出信号最多的前10个交易日
    top_days = date_details_df.nlargest(10, '当日信号数')
    for i, (_, row) in enumerate(top_days.iterrows(), 1):
        md_content += f"{i}. **{row['日期']} ({row['星期']})**: {row['当日信号数']}个信号 - {row['指数名称']}\n"
    
    md_content += f"""

### 星期分布特征
不同星期的信号分布可能反映市场的周期性特征。

### 收益率表现最佳的交易日
"""
    
    # 找出收益率最高的前5个交易日
    top_return_days = date_details_df.nlargest(5, '平均收益率(%)')
    for i, (_, row) in enumerate(top_return_days.iterrows(), 1):
        md_content += f"{i}. **{row['日期']}**: {row['平均收益率(%)']}% - {row['指数名称']} ({row['当日信号数']}个信号)\n"
    
    md_content += f"""

## 📋 数据文件说明

### Excel文件结构
**文件名**: `{excel_path}`

1. **指数信号日期详情** - 每个有信号日期的详细数据
2. **日期统计汇总** - 各指数的日期统计汇总
3. **信号强度分布** - 不同信号强度范围的天数分布
4. **星期分布** - 各指数在不同星期的信号分布

### 使用建议
- 查看具体日期的信号情况，可参考"指数信号日期详情"工作表
- 了解整体分布特征，可参考"日期统计汇总"工作表
- 分析信号强度模式，可参考"信号强度分布"工作表

## ⚠️ 注意事项

1. **数据时效性**: 基于历史数据分析，实际投资需要实时数据
2. **信号质量**: 已通过PE筛选和90分位阈值筛选，质量较高
3. **收益率**: 基于15日持有期的历史回测收益率

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存Markdown文件
    md_path = '指数信号日期详情分析报告.md'
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(md_content)
    
    print(f"Markdown报告已保存: {md_path}")

def main():
    """主函数"""
    print("=== 生成指数信号日期详情表 ===")
    
    # 加载数据
    signals_df, etf_mapping = load_data()
    
    # 生成日期详情
    date_details_df = generate_date_details(signals_df, etf_mapping)
    
    # 生成汇总统计
    summary_stats = generate_summary_stats(date_details_df)
    
    # 保存到Excel
    excel_path = save_to_excel(date_details_df, summary_stats)
    
    # 生成Markdown报告
    generate_markdown_report(date_details_df, summary_stats, excel_path)
    
    # 显示关键结果
    print(f"\n=== 生成结果概览 ===")
    print(f"日期详情记录数: {len(date_details_df)}")
    print(f"涉及指数数量: {len(summary_stats)}")
    print(f"日期范围: {date_details_df['日期'].min()} 到 {date_details_df['日期'].max()}")
    print(f"总信号数: {date_details_df['当日信号数'].sum():,}")
    
    print(f"\n信号最多的前5个指数:")
    summary_df = pd.DataFrame(summary_stats).sort_values('总信号数', ascending=False)
    for i, (_, row) in enumerate(summary_df.head(5).iterrows(), 1):
        print(f"  {i}. {row['指数名称']}: {row['总信号数']:,}个信号 ({row['有信号天数']}天)")
    
    print("\n日期详情表生成完成！")

if __name__ == "__main__":
    main()
