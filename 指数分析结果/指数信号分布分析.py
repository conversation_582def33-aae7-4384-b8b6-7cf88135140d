# -*- coding: utf-8 -*-
"""
指数信号分布分析
将ETF信号数据映射到其跟踪指数上，统计各指数的信号分布情况
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_and_process_data():
    """加载和预处理数据"""
    print("正在加载数据...")
    
    # 读取信号数据
    signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
    signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
    signals_df['date'] = signals_df['datetime'].dt.date
    
    # 读取ETF映射数据
    etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
    etf_mapping['跟踪指数代码'] = etf_mapping['跟踪指数代码'].astype(str).str.zfill(6)
    
    print(f"信号数据: {len(signals_df)} 条")
    print(f"ETF映射数据: {len(etf_mapping)} 条")
    
    return signals_df, etf_mapping

def create_index_mapping():
    """创建指数代码到指数名称的映射"""
    index_names = {
        '000010': '上证180指数',
        '000016': '上证50指数', 
        '000300': '沪深300指数',
        '000688': '科创50指数',
        '000852': '中证1000指数',
        '000905': '中证500指数',
        '000906': '中证800指数',
        '399006': '创业板指数',
        '399330': '深证100指数',
        '746059': '中证红利指数'
    }
    return index_names

def analyze_index_signals(signals_df, etf_mapping):
    """分析各指数的信号分布"""
    
    # 合并ETF信号数据和指数映射
    signals_with_index = signals_df.merge(
        etf_mapping, 
        left_on='etf_code', 
        right_on='ETF代码', 
        how='left'
    )
    
    # 筛选有效信号（通过PE筛选）
    valid_signals = signals_with_index[
        signals_with_index['pe_filter_passed'] == True
    ].copy()
    
    print(f"有效信号数: {len(valid_signals)}")
    
    # 获取指数名称映射
    index_names = create_index_mapping()
    
    # 按指数分组分析
    index_analysis = []
    
    for index_code, group in valid_signals.groupby('跟踪指数代码'):
        if pd.isna(index_code):
            continue
            
        index_name = index_names.get(index_code, f'指数{index_code}')
        
        # 统计该指数的ETF
        etfs_in_index = group['etf_code'].unique()
        
        # 统计有信号的交易日
        signal_dates = group['date'].unique()
        total_signal_days = len(signal_dates)
        
        # 统计总信号数
        total_signals = len(group)
        
        # 计算平均每个交易日的信号数
        avg_signals_per_day = total_signals / total_signal_days if total_signal_days > 0 else 0
        
        # 按日期统计每日信号数
        daily_signals = group.groupby('date').size()
        
        # 统计信号分布
        signal_distribution = {
            '1-5个信号': len(daily_signals[(daily_signals >= 1) & (daily_signals <= 5)]),
            '6-10个信号': len(daily_signals[(daily_signals >= 6) & (daily_signals <= 10)]),
            '11-20个信号': len(daily_signals[(daily_signals >= 11) & (daily_signals <= 20)]),
            '21-50个信号': len(daily_signals[(daily_signals >= 21) & (daily_signals <= 50)]),
            '50个以上信号': len(daily_signals[daily_signals > 50])
        }
        
        # 计算各阈值的信号分布
        threshold_stats = {}
        for threshold in [80, 90, 95]:
            threshold_signals = group[group['threshold'] == threshold]
            threshold_days = threshold_signals['date'].nunique()
            threshold_count = len(threshold_signals)
            threshold_stats[f'{threshold}分位'] = {
                '交易日数': threshold_days,
                '信号数': threshold_count,
                '日均信号': threshold_count / threshold_days if threshold_days > 0 else 0
            }
        
        index_analysis.append({
            '指数代码': index_code,
            '指数名称': index_name,
            '跟踪ETF数量': len(etfs_in_index),
            '跟踪ETF列表': ', '.join(map(str, sorted(etfs_in_index))),
            '有信号交易日数': total_signal_days,
            '总信号数': total_signals,
            '平均每日信号数': round(avg_signals_per_day, 2),
            '最大单日信号数': daily_signals.max(),
            '最小单日信号数': daily_signals.min(),
            '信号分布': signal_distribution,
            '阈值统计': threshold_stats,
            '每日信号数据': daily_signals
        })
    
    return index_analysis

def generate_analysis_report(index_analysis):
    """生成分析报告"""
    
    # 创建汇总表
    summary_data = []
    for analysis in index_analysis:
        summary_data.append({
            '指数代码': analysis['指数代码'],
            '指数名称': analysis['指数名称'],
            '跟踪ETF数量': analysis['跟踪ETF数量'],
            '跟踪ETF列表': analysis['跟踪ETF列表'],
            '有信号交易日数': analysis['有信号交易日数'],
            '总信号数': analysis['总信号数'],
            '平均每日信号数': analysis['平均每日信号数'],
            '最大单日信号数': analysis['最大单日信号数'],
            '最小单日信号数': analysis['最小单日信号数']
        })
    
    summary_df = pd.DataFrame(summary_data)
    
    # 按总信号数排序
    summary_df = summary_df.sort_values('总信号数', ascending=False)
    
    # 保存Excel文件
    excel_path = '指数信号分布分析结果.xlsx'
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        # 汇总统计
        summary_df.to_excel(writer, sheet_name='指数信号汇总', index=False)
        
        # 详细的信号分布
        distribution_data = []
        for analysis in index_analysis:
            for range_name, count in analysis['信号分布'].items():
                distribution_data.append({
                    '指数代码': analysis['指数代码'],
                    '指数名称': analysis['指数名称'],
                    '信号范围': range_name,
                    '交易日数': count,
                    '占比(%)': round(count / analysis['有信号交易日数'] * 100, 2) if analysis['有信号交易日数'] > 0 else 0
                })
        
        distribution_df = pd.DataFrame(distribution_data)
        distribution_df.to_excel(writer, sheet_name='信号分布详情', index=False)
        
        # 阈值统计
        threshold_data = []
        for analysis in index_analysis:
            for threshold, stats in analysis['阈值统计'].items():
                threshold_data.append({
                    '指数代码': analysis['指数代码'],
                    '指数名称': analysis['指数名称'],
                    '阈值': threshold,
                    '交易日数': stats['交易日数'],
                    '信号数': stats['信号数'],
                    '日均信号': round(stats['日均信号'], 2)
                })
        
        threshold_df = pd.DataFrame(threshold_data)
        threshold_df.to_excel(writer, sheet_name='阈值统计', index=False)
    
    print(f"Excel报告已保存: {excel_path}")
    
    return summary_df

def generate_markdown_report(summary_df, index_analysis):
    """生成Markdown报告"""
    
    md_content = f"""# 指数信号分布分析报告

## 分析概述

基于ETF 5分钟成交额信号数据，将各ETF信号映射到其跟踪指数上，分析各指数的信号分布特征。

**数据时间范围**: 2024年9月9日 - 2025年4月30日  
**分析指数数量**: {len(summary_df)}个  
**总信号数**: {summary_df['总信号数'].sum():,}个

## 指数信号汇总统计

| 指数代码 | 指数名称 | 跟踪ETF数 | 有信号交易日数 | 总信号数 | 平均每日信号数 | 最大单日信号数 |
|----------|----------|-----------|----------------|----------|----------------|----------------|
"""
    
    for _, row in summary_df.iterrows():
        md_content += f"| {row['指数代码']} | {row['指数名称']} | {row['跟踪ETF数量']} | {row['有信号交易日数']} | {row['总信号数']:,} | {row['平均每日信号数']} | {row['最大单日信号数']} |\n"
    
    md_content += f"""

## 详细分析

### 1. 信号活跃度排名

**Top 5 最活跃指数**:
"""
    
    top_5 = summary_df.head(5)
    for i, (_, row) in enumerate(top_5.iterrows(), 1):
        md_content += f"{i}. **{row['指数名称']}** ({row['指数代码']}): {row['总信号数']:,}个信号\n"
    
    md_content += f"""

### 2. ETF覆盖情况

"""
    
    for _, row in summary_df.iterrows():
        md_content += f"- **{row['指数名称']}**: {row['跟踪ETF数量']}只ETF ({row['跟踪ETF列表']})\n"
    
    md_content += f"""

### 3. 信号密度分析

| 指数名称 | 信号密度 | 特征描述 |
|----------|----------|----------|
"""
    
    for _, row in summary_df.iterrows():
        if row['平均每日信号数'] >= 20:
            density_desc = "高密度"
        elif row['平均每日信号数'] >= 10:
            density_desc = "中密度"
        else:
            density_desc = "低密度"
        
        md_content += f"| {row['指数名称']} | {row['平均每日信号数']}个/日 | {density_desc} |\n"
    
    md_content += f"""

## 关键发现

### 1. 信号分布特征
- **最活跃指数**: {summary_df.iloc[0]['指数名称']} ({summary_df.iloc[0]['总信号数']:,}个信号)
- **信号密度最高**: {summary_df.loc[summary_df['平均每日信号数'].idxmax(), '指数名称']} ({summary_df['平均每日信号数'].max()}个/日)
- **覆盖ETF最多**: {summary_df.loc[summary_df['跟踪ETF数量'].idxmax(), '指数名称']} ({summary_df['跟踪ETF数量'].max()}只ETF)

### 2. 指数类型分析
- **宽基指数**: 沪深300、中证500、中证1000等信号较为活跃
- **行业指数**: 创业板指数信号密度较高
- **主题指数**: 红利指数等信号相对较少

### 3. 投资启示
- 信号密度高的指数可能存在更多的择时机会
- 多ETF跟踪的指数具有更好的流动性选择
- 不同指数的信号特征反映了市场结构差异

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存Markdown文件
    md_path = '指数信号分布分析报告.md'
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(md_content)
    
    print(f"Markdown报告已保存: {md_path}")

def main():
    """主函数"""
    print("=== 指数信号分布分析 ===")
    
    # 加载数据
    signals_df, etf_mapping = load_and_process_data()
    
    # 分析指数信号
    index_analysis = analyze_index_signals(signals_df, etf_mapping)
    
    # 生成报告
    summary_df = generate_analysis_report(index_analysis)
    generate_markdown_report(summary_df, index_analysis)
    
    # 显示关键结果
    print(f"\n=== 分析结果概览 ===")
    print(f"分析指数数量: {len(summary_df)}")
    print(f"总信号数: {summary_df['总信号数'].sum():,}")
    print(f"平均每个指数信号数: {summary_df['总信号数'].mean():.0f}")
    
    print(f"\n信号最多的前5个指数:")
    top_5 = summary_df.head(5)
    for i, (_, row) in enumerate(top_5.iterrows(), 1):
        print(f"  {i}. {row['指数名称']}: {row['总信号数']:,}个信号 (日均{row['平均每日信号数']}个)")
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
