# 指数信号日期详情分析报告

## 📊 概述

**分析条件**: pe_filter_passed=TRUE且threshold=90且holding_period=next_15day_close  
**日期范围**: 2024-09-09 到 2025-04-09  
**有信号天数**: 135天  
**总信号数**: 6,305个

## 📈 各指数信号日期统计

| 指数名称 | 有信号天数 | 总信号数 | 平均每日信号数 | 平均收益率(%) | 平均胜率(%) |
|----------|------------|----------|----------------|---------------|-------------|
| 创业板指数 | 118 | 2,440 | 20.68 | 2.756 | 48.32 |
| 中证1000指数 | 39 | 1,152 | 29.54 | 13.8606 | 98.97 |
| 沪深300指数 | 23 | 720 | 31.3 | 12.4615 | 97.68 |
| 深证100指数 | 101 | 680 | 6.73 | 2.38 | 52.17 |
| 中证红利指数 | 105 | 473 | 4.5 | 0.3677 | 45.43 |
| 中证500指数 | 15 | 433 | 28.87 | 20.4424 | 98.67 |
| 上证50指数 | 23 | 247 | 10.74 | 10.5167 | 90.22 |
| 中证800指数 | 14 | 87 | 6.21 | 14.8971 | 98.57 |
| 上证180指数 | 14 | 73 | 5.21 | 18.5814 | 100.0 |


## 🗓️ 信号活跃度分析

### 最活跃的交易日
基于日期详情数据，以下是信号最多的交易日：

1. **2024-09-30 (周一)**: 148个信号 - 中证1000指数
2. **2025-04-08 (周二)**: 138个信号 - 中证1000指数
3. **2024-10-09 (周三)**: 135个信号 - 创业板指数
4. **2024-09-30 (周一)**: 135个信号 - 沪深300指数
5. **2024-10-08 (周二)**: 134个信号 - 创业板指数
6. **2025-04-08 (周二)**: 123个信号 - 沪深300指数
7. **2024-09-30 (周一)**: 122个信号 - 创业板指数
8. **2024-10-18 (周五)**: 115个信号 - 创业板指数
9. **2024-09-27 (周五)**: 107个信号 - 创业板指数
10. **2024-10-11 (周五)**: 103个信号 - 创业板指数


### 星期分布特征
不同星期的信号分布可能反映市场的周期性特征。

### 收益率表现最佳的交易日
1. **2024-09-09**: 48.8421% - 创业板指数 (11个信号)
2. **2024-09-10**: 44.0232% - 创业板指数 (25个信号)
3. **2024-09-23**: 43.6584% - 创业板指数 (18个信号)
4. **2024-09-20**: 43.1138% - 创业板指数 (29个信号)
5. **2024-09-24**: 40.3349% - 创业板指数 (61个信号)


## 📋 数据文件说明

### Excel文件结构
**文件名**: `指数信号日期详情表_90分位15日.xlsx`

1. **指数信号日期详情** - 每个有信号日期的详细数据
2. **日期统计汇总** - 各指数的日期统计汇总
3. **信号强度分布** - 不同信号强度范围的天数分布
4. **星期分布** - 各指数在不同星期的信号分布

### 使用建议
- 查看具体日期的信号情况，可参考"指数信号日期详情"工作表
- 了解整体分布特征，可参考"日期统计汇总"工作表
- 分析信号强度模式，可参考"信号强度分布"工作表

## ⚠️ 注意事项

1. **数据时效性**: 基于历史数据分析，实际投资需要实时数据
2. **信号质量**: 已通过PE筛选和90分位阈值筛选，质量较高
3. **收益率**: 基于15日持有期的历史回测收益率

---
*报告生成时间: 2025-06-10 10:14:11*
