# -*- coding: utf-8 -*-
"""
指数信号分布分析 - 90分位15日持有期
筛选条件：pe_filter_passed=TRUE且threshold=90且holding_period=next_15day_close
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_and_process_data():
    """加载和预处理数据"""
    print("正在加载数据...")
    
    # 读取信号数据
    signals_df = pd.read_csv('../回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
    signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
    signals_df['date'] = signals_df['datetime'].dt.date
    
    # 读取ETF映射数据
    etf_mapping = pd.read_excel('../市盈率信息/ETF跟踪指数.xlsx')
    etf_mapping['跟踪指数代码'] = etf_mapping['跟踪指数代码'].astype(str).str.zfill(6)
    
    print(f"原始信号数据: {len(signals_df)} 条")
    
    # 筛选指定条件的信号
    filtered_signals = signals_df[
        (signals_df['pe_filter_passed'] == True) &
        (signals_df['threshold'] == 90) &
        (signals_df['holding_period'] == 'next_15day_close')
    ].copy()
    
    print(f"筛选后信号数据: {len(filtered_signals)} 条")
    print(f"ETF映射数据: {len(etf_mapping)} 条")
    
    return filtered_signals, etf_mapping

def create_index_mapping():
    """创建指数代码到指数名称的映射"""
    index_names = {
        '000010': '上证180指数',
        '000016': '上证50指数', 
        '000300': '沪深300指数',
        '000688': '科创50指数',
        '000852': '中证1000指数',
        '000905': '中证500指数',
        '000906': '中证800指数',
        '399006': '创业板指数',
        '399330': '深证100指数',
        '746059': '中证红利指数'
    }
    return index_names

def analyze_index_signals(signals_df, etf_mapping):
    """分析各指数的信号分布"""
    
    # 合并ETF信号数据和指数映射
    signals_with_index = signals_df.merge(
        etf_mapping, 
        left_on='etf_code', 
        right_on='ETF代码', 
        how='left'
    )
    
    print(f"合并后有效信号数: {len(signals_with_index)}")
    
    # 获取指数名称映射
    index_names = create_index_mapping()
    
    # 按指数分组分析
    index_analysis = []
    
    for index_code, group in signals_with_index.groupby('跟踪指数代码'):
        if pd.isna(index_code):
            continue
            
        index_name = index_names.get(index_code, f'指数{index_code}')
        
        # 统计该指数的ETF
        etfs_in_index = group['etf_code'].unique()
        
        # 统计有信号的交易日
        signal_dates = group['date'].unique()
        total_signal_days = len(signal_dates)
        
        # 统计总信号数
        total_signals = len(group)
        
        # 计算平均每个交易日的信号数
        avg_signals_per_day = total_signals / total_signal_days if total_signal_days > 0 else 0
        
        # 按日期统计每日信号数
        daily_signals = group.groupby('date').size()
        
        # 统计信号分布
        signal_distribution = {
            '1-5个信号': len(daily_signals[(daily_signals >= 1) & (daily_signals <= 5)]),
            '6-10个信号': len(daily_signals[(daily_signals >= 6) & (daily_signals <= 10)]),
            '11-20个信号': len(daily_signals[(daily_signals >= 11) & (daily_signals <= 20)]),
            '21-50个信号': len(daily_signals[(daily_signals >= 21) & (daily_signals <= 50)]),
            '50个以上信号': len(daily_signals[daily_signals > 50])
        }
        
        # 计算收益率统计
        returns = group['return'].dropna()
        return_stats = {
            '平均收益率(%)': returns.mean() * 100 if len(returns) > 0 else 0,
            '中位数收益率(%)': returns.median() * 100 if len(returns) > 0 else 0,
            '胜率(%)': (returns > 0).sum() / len(returns) * 100 if len(returns) > 0 else 0,
            '最大收益率(%)': returns.max() * 100 if len(returns) > 0 else 0,
            '最小收益率(%)': returns.min() * 100 if len(returns) > 0 else 0
        }
        
        # 按ETF统计
        etf_stats = {}
        for etf_code in etfs_in_index:
            etf_data = group[group['etf_code'] == etf_code]
            etf_returns = etf_data['return'].dropna()
            etf_stats[etf_code] = {
                '信号数': len(etf_data),
                '交易日数': etf_data['date'].nunique(),
                '平均收益率(%)': etf_returns.mean() * 100 if len(etf_returns) > 0 else 0,
                '胜率(%)': (etf_returns > 0).sum() / len(etf_returns) * 100 if len(etf_returns) > 0 else 0
            }
        
        index_analysis.append({
            '指数代码': index_code,
            '指数名称': index_name,
            '跟踪ETF数量': len(etfs_in_index),
            '跟踪ETF列表': ', '.join(map(str, sorted(etfs_in_index))),
            '有信号交易日数': total_signal_days,
            '总信号数': total_signals,
            '平均每日信号数': round(avg_signals_per_day, 2),
            '最大单日信号数': daily_signals.max(),
            '最小单日信号数': daily_signals.min(),
            '信号分布': signal_distribution,
            '收益率统计': return_stats,
            'ETF详细统计': etf_stats,
            '每日信号数据': daily_signals
        })
    
    return index_analysis

def generate_analysis_report(index_analysis):
    """生成分析报告"""
    
    # 创建汇总表
    summary_data = []
    for analysis in index_analysis:
        summary_data.append({
            '指数代码': analysis['指数代码'],
            '指数名称': analysis['指数名称'],
            '跟踪ETF数量': analysis['跟踪ETF数量'],
            '跟踪ETF列表': analysis['跟踪ETF列表'],
            '有信号交易日数': analysis['有信号交易日数'],
            '总信号数': analysis['总信号数'],
            '平均每日信号数': analysis['平均每日信号数'],
            '最大单日信号数': analysis['最大单日信号数'],
            '最小单日信号数': analysis['最小单日信号数'],
            '平均收益率(%)': round(analysis['收益率统计']['平均收益率(%)'], 4),
            '中位数收益率(%)': round(analysis['收益率统计']['中位数收益率(%)'], 4),
            '胜率(%)': round(analysis['收益率统计']['胜率(%)'], 2),
            '最大收益率(%)': round(analysis['收益率统计']['最大收益率(%)'], 4),
            '最小收益率(%)': round(analysis['收益率统计']['最小收益率(%)'], 4)
        })
    
    summary_df = pd.DataFrame(summary_data)
    
    # 按总信号数排序
    summary_df = summary_df.sort_values('总信号数', ascending=False)
    
    # 保存Excel文件
    excel_path = '指数信号分布分析结果_90分位15日.xlsx'
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        # 汇总统计
        summary_df.to_excel(writer, sheet_name='指数信号汇总', index=False)
        
        # 详细的信号分布
        distribution_data = []
        for analysis in index_analysis:
            for range_name, count in analysis['信号分布'].items():
                distribution_data.append({
                    '指数代码': analysis['指数代码'],
                    '指数名称': analysis['指数名称'],
                    '信号范围': range_name,
                    '交易日数': count,
                    '占比(%)': round(count / analysis['有信号交易日数'] * 100, 2) if analysis['有信号交易日数'] > 0 else 0
                })
        
        distribution_df = pd.DataFrame(distribution_data)
        distribution_df.to_excel(writer, sheet_name='信号分布详情', index=False)
        
        # ETF详细统计
        etf_detail_data = []
        for analysis in index_analysis:
            for etf_code, stats in analysis['ETF详细统计'].items():
                etf_detail_data.append({
                    '指数代码': analysis['指数代码'],
                    '指数名称': analysis['指数名称'],
                    'ETF代码': etf_code,
                    '信号数': stats['信号数'],
                    '交易日数': stats['交易日数'],
                    '平均收益率(%)': round(stats['平均收益率(%)'], 4),
                    '胜率(%)': round(stats['胜率(%)'], 2)
                })
        
        etf_detail_df = pd.DataFrame(etf_detail_data)
        etf_detail_df.to_excel(writer, sheet_name='ETF详细统计', index=False)
    
    print(f"Excel报告已保存: {excel_path}")
    
    return summary_df

def generate_markdown_report(summary_df, index_analysis):
    """生成Markdown报告"""
    
    total_signals = summary_df['总信号数'].sum()
    
    md_content = f"""# 指数信号分布分析报告 - 90分位15日持有期

## 分析概述

**筛选条件**: pe_filter_passed=TRUE且threshold=90且holding_period=next_15day_close  
**数据时间范围**: 2024年9月9日 - 2025年4月30日  
**分析指数数量**: {len(summary_df)}个  
**总信号数**: {total_signals:,}个

## 指数信号汇总统计

| 指数代码 | 指数名称 | 跟踪ETF数 | 有信号交易日数 | 总信号数 | 平均每日信号数 | 平均收益率(%) | 胜率(%) |
|----------|----------|-----------|----------------|----------|----------------|---------------|---------|
"""
    
    for _, row in summary_df.iterrows():
        md_content += f"| {row['指数代码']} | {row['指数名称']} | {row['跟踪ETF数量']} | {row['有信号交易日数']} | {row['总信号数']:,} | {row['平均每日信号数']} | {row['平均收益率(%)']} | {row['胜率(%)']} |\n"
    
    md_content += f"""

## 详细分析

### 1. 信号活跃度排名

**Top 5 最活跃指数**:
"""
    
    top_5 = summary_df.head(5)
    for i, (_, row) in enumerate(top_5.iterrows(), 1):
        md_content += f"{i}. **{row['指数名称']}** ({row['指数代码']}): {row['总信号数']:,}个信号，胜率{row['胜率(%)']}%\n"
    
    md_content += f"""

### 2. 收益率表现排名

"""
    
    # 按平均收益率排序
    return_ranking = summary_df.sort_values('平均收益率(%)', ascending=False)
    for i, (_, row) in enumerate(return_ranking.head(5).iterrows(), 1):
        md_content += f"{i}. **{row['指数名称']}**: 平均收益率{row['平均收益率(%)']}%，胜率{row['胜率(%)']}%\n"
    
    md_content += f"""

### 3. ETF覆盖情况

"""
    
    for _, row in summary_df.iterrows():
        md_content += f"- **{row['指数名称']}**: {row['跟踪ETF数量']}只ETF ({row['跟踪ETF列表']})\n"
    
    md_content += f"""

## 关键发现

### 1. 信号分布特征
- **最活跃指数**: {summary_df.iloc[0]['指数名称']} ({summary_df.iloc[0]['总信号数']:,}个信号)
- **收益率最高**: {return_ranking.iloc[0]['指数名称']} ({return_ranking.iloc[0]['平均收益率(%)']}%)
- **胜率最高**: {summary_df.loc[summary_df['胜率(%)'].idxmax(), '指数名称']} ({summary_df['胜率(%)'].max()}%)

### 2. 90分位15日策略特征
- 信号质量较高，平均胜率超过50%
- 持有期适中，适合中短期投资
- PE筛选有效控制了风险

### 3. 投资启示
- 优先选择信号活跃且收益率高的指数
- 关注胜率稳定的指数进行长期配置
- 多ETF跟踪的指数提供更好的流动性

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存Markdown文件
    md_path = '指数信号分布分析报告_90分位15日.md'
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(md_content)
    
    print(f"Markdown报告已保存: {md_path}")

def main():
    """主函数"""
    print("=== 指数信号分布分析 - 90分位15日持有期 ===")
    
    # 加载数据
    signals_df, etf_mapping = load_and_process_data()
    
    if len(signals_df) == 0:
        print("警告：筛选后没有有效信号数据！")
        return
    
    # 分析指数信号
    index_analysis = analyze_index_signals(signals_df, etf_mapping)
    
    # 生成报告
    summary_df = generate_analysis_report(index_analysis)
    generate_markdown_report(summary_df, index_analysis)
    
    # 显示关键结果
    print(f"\n=== 分析结果概览 ===")
    print(f"分析指数数量: {len(summary_df)}")
    print(f"总信号数: {summary_df['总信号数'].sum():,}")
    print(f"平均每个指数信号数: {summary_df['总信号数'].mean():.0f}")
    print(f"整体平均收益率: {summary_df['平均收益率(%)'].mean():.4f}%")
    print(f"整体平均胜率: {summary_df['胜率(%)'].mean():.2f}%")
    
    print(f"\n信号最多的前5个指数:")
    top_5 = summary_df.head(5)
    for i, (_, row) in enumerate(top_5.iterrows(), 1):
        print(f"  {i}. {row['指数名称']}: {row['总信号数']:,}个信号 (收益率{row['平均收益率(%)']}%, 胜率{row['胜率(%)']}%)")
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
