#!/usr/bin/env python3
import pandas as pd

# 检查指数分析结果
excel_path = '指数信号分布分析结果_90分位15日.xlsx'

try:
    df = pd.read_excel(excel_path, sheet_name='指数信号汇总')
    print(f"指数分析结果包含 {len(df)} 个指数")
    print("\n指数列表:")
    for _, row in df.iterrows():
        print(f"  {row['指数代码']}: {row['指数名称']} (跟踪ETF: {row['跟踪ETF列表']})")
    
    print(f"\n000688是否存在: {'000688' in df['指数代码'].values}")
    
    # 如果存在000688，显示详细信息
    if '000688' in df['指数代码'].values:
        row_000688 = df[df['指数代码'] == '000688'].iloc[0]
        print(f"\n000688详细信息:")
        for col in df.columns:
            print(f"  {col}: {row_000688[col]}")
    
    # 检查ETF详细统计表
    try:
        etf_df = pd.read_excel(excel_path, sheet_name='ETF详细统计')
        print(f"\nETF详细统计包含 {len(etf_df)} 条记录")
        
        # 检查588050和588080
        for etf in ['588050', '588080']:
            etf_data = etf_df[etf_df['ETF代码'] == etf]
            if not etf_data.empty:
                print(f"\n{etf}在ETF详细统计中:")
                for _, row in etf_data.iterrows():
                    print(f"  指数: {row['指数代码']} - {row['指数名称']}")
                    print(f"  信号数: {row['信号数']}")
                    print(f"  交易日数: {row['交易日数']}")
            else:
                print(f"\n{etf}不在ETF详细统计中")
    except Exception as e:
        print(f"读取ETF详细统计失败: {e}")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
