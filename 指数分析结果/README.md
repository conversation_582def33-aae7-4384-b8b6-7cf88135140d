# 指数分析结果文件夹说明

## 📁 文件夹概述

本文件夹包含基于ETF 5分钟成交额信号数据的指数分布分析结果，包括原始全量数据分析和90分位15日策略的专项分析。

## 📊 文件列表

### 🎯 **90分位15日策略分析**（推荐使用）

#### 核心文件
- **`指数信号分布分析结果_90分位15日.xlsx`** - 90分位15日策略的详细分析数据
  - 指数信号汇总：各指数的基本统计和收益率数据
  - ETF详细统计：每个ETF的具体表现
  - 信号分布详情：信号强度分布情况

- **`指数信号日期详情表_90分位15日.xlsx`** - 🆕 各指数有信号的具体日期详情
  - 指数信号日期详情：每个有信号日期的详细数据（452条记录）
  - 日期统计汇总：各指数的日期统计汇总
  - 信号强度分布：不同信号强度范围的天数分布
  - 星期分布：各指数在不同星期的信号分布

- **`90分位15日策略深度分析报告.md`** - 深度投资策略分析报告
  - 投资建议和配置方案
  - 风险收益分析
  - 具体ETF选择建议

#### 基础文件
- **`指数信号分布分析报告_90分位15日.md`** - 基础分析报告
- **`指数信号日期详情分析报告.md`** - 🆕 日期详情分析报告
- **`指数信号分布分析_90分位15日.py`** - 分析脚本（可重复运行）
- **`生成指数信号日期详情表.py`** - 🆕 日期详情表生成脚本

### 📈 **原始全量数据分析**（参考对比）

#### 核心文件
- **`指数信号分布分析结果.xlsx`** - 全量信号数据分析
- **`指数信号分布深度分析报告.md`** - 全量数据投资策略分析

#### 基础文件
- **`指数信号分布分析报告.md`** - 全量数据基础报告
- **`指数信号分布分析.py`** - 全量数据分析脚本

## 🎯 核心发现对比

### 90分位15日策略 vs 原始全量数据

| 指标 | 90分位15日策略 | 原始全量数据 | 优势 |
|------|----------------|--------------|------|
| **信号数量** | 6,305个 | 303,396个 | 精选高质量信号 |
| **筛选率** | 1.89% | 100% | 严格筛选 |
| **平均收益率** | **9.71%** | 约3-5% | 收益率显著提升 |
| **平均胜率** | **80.92%** | 约55-65% | 胜率大幅提升 |
| **投资价值** | 极高 | 中等 | 质量优于数量 |

## 📋 使用建议

### 1. 投资决策
- **主要参考**: 90分位15日策略分析结果
- **对比参考**: 原始全量数据作为背景对比

### 2. 文件使用优先级
1. **`90分位15日策略深度分析报告.md`** - 投资策略制定
2. **`指数信号分布分析结果_90分位15日.xlsx`** - 详细数据查询
3. **`指数信号日期详情表_90分位15日.xlsx`** - 🆕 具体日期信号查询
4. **`指数信号分布分析_90分位15日.py`** - 重复分析或参数调整

### 3. 核心投资建议
基于90分位15日策略分析：

#### 🏆 **顶级配置**（70%）
- **中证1000指数** (25%) - ETF: 512100或159845
- **中证500指数** (25%) - ETF: 512500  
- **上证180指数** (20%) - ETF: 510180

#### 🎯 **稳健配置**（20%）
- **沪深300指数** (10%) - ETF: 510310
- **上证50指数** (10%) - ETF: 510050

#### 💡 **机会配置**（10%）
- **中证800指数** (5%) - ETF: 515800
- **创业板指数** (5%) - ETF: 159915

## ⚠️ 重要说明

### 1. 数据时效性
- **数据时间范围**: 2024年9月9日 - 2025年4月30日
- **分析完成时间**: 2025年6月10日
- **建议**: 定期更新数据以保持策略有效性

### 2. 风险提示
- 历史表现不代表未来收益
- 需要严格按照信号执行，避免主观判断
- 建议分散投资，控制单一标的风险

### 3. 策略执行要点
- **信号条件**: pe_filter_passed=TRUE且threshold=90且holding_period=next_15day_close
- **持有周期**: 严格执行15个交易日
- **资金管理**: 建议分批建仓，保留适当现金储备

## 🆕 新增功能：日期详情表

### 功能说明
- **文件名**: `指数信号日期详情表_90分位15日.xlsx`
- **记录数**: 452条日期详情记录
- **时间范围**: 2024年9月9日 - 2025年4月30日
- **内容**: 每个有信号日期的详细信息，包括信号数量、收益率统计等

### 主要用途
1. **具体日期查询**: 查看某个特定日期的信号情况
2. **时间模式分析**: 分析信号在时间维度上的分布规律
3. **收益率追踪**: 跟踪每日的收益率表现
4. **星期效应研究**: 分析不同星期的信号特征

### 关键发现
- **信号最多的交易日**: 2024-09-30 (创业板指数, 218个信号)
- **收益率最高的交易日**: 2024-09-30 (上证180指数, 28.57%收益率)
- **最活跃指数**: 创业板指数 (118个有信号交易日)
- **最稳定指数**: 中证1000指数 (平均收益率14.21%)

## 📞 技术支持

如需重新运行分析或调整参数，请使用对应的Python脚本文件。所有脚本都包含详细的注释和说明。

---
*文件夹说明更新时间: 2025年6月10日*
