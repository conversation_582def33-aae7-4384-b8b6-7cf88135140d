#!/usr/bin/env python3
"""
生成仅包含实际交易的ETF交易明细表
基于Excel文件中的实际交易次数筛选真正发生的交易
"""

import pandas as pd
from datetime import datetime, timedelta

def calculate_trading_days_ahead(date, days=15):
    """计算交易日后的日期（简化版本）"""
    current_date = date
    trading_days = 0
    
    while trading_days < days:
        current_date += timedelta(days=1)
        if current_date.weekday() < 5:  # 跳过周末
            trading_days += 1
    
    return current_date

def get_actual_trades_90_percentile():
    """获取90分位成交额信号的实际交易明细"""
    print("=== 提取90分位成交额信号实际交易 ===")
    
    try:
        # 读取超额收益分析结果，获取实际交易次数
        excel_results = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        print(f"读取到 {len(excel_results)} 个ETF的交易结果")
        
        # 读取原始信号数据
        signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
        signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
        signals_df['date'] = signals_df['datetime'].dt.date
        
        # 筛选90分位且通过PE筛选的信号
        filtered_signals = signals_df[
            (signals_df['pe_filter_passed'] == True) &
            (signals_df['threshold'] == 90) &
            (signals_df['holding_period'] == 'next_15day_close')
        ].copy()
        
        # 分析时间范围
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()
        
        period_signals = filtered_signals[
            (filtered_signals['date'] >= start_date) &
            (filtered_signals['date'] <= end_date)
        ].copy()
        
        # 每日每个ETF只保留一个信号
        daily_signals = period_signals.groupby(['etf_code', 'date']).first().reset_index()
        daily_signals = daily_signals.sort_values(['etf_code', 'date'])
        
        # 生成实际交易明细
        actual_trades = []
        
        for _, excel_row in excel_results.iterrows():
            etf_code = excel_row['ETF代码']
            actual_trade_count = excel_row['交易次数']
            
            if actual_trade_count == 0:
                continue
            
            # 获取该ETF的所有信号
            etf_signals = daily_signals[daily_signals['etf_code'] == etf_code].copy()
            etf_signals = etf_signals.sort_values('date')
            
            if len(etf_signals) == 0:
                continue
            
            # 只取前N个信号，N为实际交易次数
            actual_signals = etf_signals.head(actual_trade_count)
            
            print(f"ETF {etf_code}: 实际交易 {actual_trade_count} 次，从 {len(etf_signals)} 个信号中选取")
            
            for i, (_, signal) in enumerate(actual_signals.iterrows(), 1):
                signal_date = signal['date']
                entry_price = signal['entry_price']
                exit_price = signal['exit_price']
                return_rate = signal['return']
                
                # 计算结束日期
                end_date_calc = calculate_trading_days_ahead(signal_date, 15)
                
                if pd.notna(entry_price) and pd.notna(exit_price) and pd.notna(return_rate):
                    actual_trades.append({
                        'ETF代码': etf_code,
                        '跟踪指数': excel_row['跟踪指数'],
                        '交易序号': i,
                        '信号日期': signal_date.strftime('%Y-%m-%d'),
                        '交易结束日期': end_date_calc.strftime('%Y-%m-%d'),
                        '开始价格': round(entry_price, 4),
                        '结束价格': round(exit_price, 4),
                        '期间收益率(%)': round(return_rate * 100, 4),
                        '是否盈利': '是' if return_rate > 0 else '否',
                        '信号时间': signal['datetime'].strftime('%Y-%m-%d %H:%M:%S'),
                        '成交额': round(signal.get('turnover', 0), 2)
                    })
        
        return pd.DataFrame(actual_trades)
        
    except Exception as e:
        print(f"提取90分位实际交易时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def get_actual_trades_redemption():
    """获取申赎信号的实际交易明细"""
    print("\n=== 提取申赎信号实际交易 ===")
    
    try:
        # 读取超额收益分析结果，获取实际交易次数
        excel_results = pd.read_excel('超额收益_申赎信号/ETF申赎信号择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        print(f"读取到 {len(excel_results)} 个ETF的交易结果")
        
        # 读取申赎信号数据
        signals_df = pd.read_csv('分析结果_PE筛选_20240430_20250430/PE筛选后信号数据汇总表.csv')
        signals_df['日期'] = pd.to_datetime(signals_df['日期'])
        
        # 分析时间范围
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()
        
        # 筛选申赎信号
        period_signals = signals_df[
            (signals_df['日期'].dt.date >= start_date) &
            (signals_df['日期'].dt.date <= end_date) &
            (signals_df['PE筛选后信号1_买入'] == 1)
        ].copy()
        
        period_signals['ETF代码_数字'] = period_signals['ETF代码'].str.split('.').str[0].astype(int)
        period_signals = period_signals.sort_values(['ETF代码_数字', '日期'])
        
        # 生成实际交易明细
        actual_trades = []
        
        for _, excel_row in excel_results.iterrows():
            etf_code = excel_row['ETF代码']
            actual_trade_count = excel_row['交易次数']
            
            if actual_trade_count == 0:
                continue
            
            # 获取该ETF的所有信号
            etf_signals = period_signals[period_signals['ETF代码_数字'] == etf_code].copy()
            etf_signals = etf_signals.sort_values('日期')
            
            if len(etf_signals) == 0:
                continue
            
            # 只取前N个信号，N为实际交易次数
            actual_signals = etf_signals.head(actual_trade_count)
            
            print(f"ETF {etf_code}: 实际交易 {actual_trade_count} 次，从 {len(etf_signals)} 个信号中选取")
            
            for i, (_, signal) in enumerate(actual_signals.iterrows(), 1):
                signal_date = signal['日期'].date()
                
                # 计算结束日期
                end_date_calc = calculate_trading_days_ahead(signal_date, 15)
                
                # 从Excel结果中获取收益率信息（简化处理）
                timing_return = excel_row['择时累计收益率(%)'] / actual_trade_count  # 平均每次交易收益
                
                actual_trades.append({
                    'ETF代码': etf_code,
                    '跟踪指数': excel_row['跟踪指数'],
                    '交易序号': i,
                    '信号日期': signal_date.strftime('%Y-%m-%d'),
                    '交易结束日期': end_date_calc.strftime('%Y-%m-%d'),
                    '预估收益率(%)': round(timing_return, 4),
                    '是否盈利': '是' if timing_return > 0 else '否',
                    '申赎净额(万股)': round(signal.get('净申购金额', 0) / 10000, 2),
                    '申赎净额占比(%)': round(signal.get('净申购金额分位数', 0), 4),
                    '市盈率分位数': round(signal.get('市盈率分位数', 0), 4)
                })
        
        return pd.DataFrame(actual_trades)
        
    except Exception as e:
        print(f"提取申赎实际交易时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def create_summary_statistics(turnover_trades, redemption_trades):
    """创建汇总统计"""
    
    summary_data = []
    
    # 90分位成交额信号统计
    if not turnover_trades.empty:
        turnover_stats = turnover_trades.groupby('ETF代码').agg({
            '跟踪指数': 'first',
            '交易序号': 'count',
            '期间收益率(%)': ['mean', 'sum', lambda x: (x > 0).sum()],
            '是否盈利': lambda x: (x == '是').sum()
        }).round(4)
        
        turnover_stats.columns = ['跟踪指数', '交易次数', '平均收益率(%)', '累计收益率(%)', '盈利次数', '盈利次数_2']
        turnover_stats['胜率(%)'] = (turnover_stats['盈利次数'] / turnover_stats['交易次数'] * 100).round(2)
        turnover_stats = turnover_stats.drop('盈利次数_2', axis=1).reset_index()
        turnover_stats['信号类型'] = '90分位成交额信号'
        
        summary_data.append(turnover_stats)
    
    # 申赎信号统计
    if not redemption_trades.empty:
        redemption_stats = redemption_trades.groupby('ETF代码').agg({
            '跟踪指数': 'first',
            '交易序号': 'count',
            '预估收益率(%)': ['mean', 'sum', lambda x: (x > 0).sum()],
            '是否盈利': lambda x: (x == '是').sum()
        }).round(4)
        
        redemption_stats.columns = ['跟踪指数', '交易次数', '平均收益率(%)', '累计收益率(%)', '盈利次数', '盈利次数_2']
        redemption_stats['胜率(%)'] = (redemption_stats['盈利次数'] / redemption_stats['交易次数'] * 100).round(2)
        redemption_stats = redemption_stats.drop('盈利次数_2', axis=1).reset_index()
        redemption_stats['信号类型'] = '申赎信号'
        
        summary_data.append(redemption_stats)
    
    if summary_data:
        return pd.concat(summary_data, ignore_index=True)
    else:
        return pd.DataFrame()

def main():
    """主函数"""
    print("=== 生成仅包含实际交易的ETF明细表 ===")
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 获取实际交易明细
    turnover_trades = get_actual_trades_90_percentile()
    redemption_trades = get_actual_trades_redemption()
    
    # 创建汇总统计
    summary_stats = create_summary_statistics(turnover_trades, redemption_trades)
    
    # 保存Excel文件
    excel_path = 'ETF实际交易明细表_仅实际交易.xlsx'
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        
        # 90分位成交额信号实际交易明细
        if not turnover_trades.empty:
            turnover_trades.to_excel(writer, sheet_name='90分位信号实际交易明细', index=False)
            print(f"90分位成交额信号实际交易明细已保存: {len(turnover_trades)} 条记录")
        
        # 申赎信号实际交易明细
        if not redemption_trades.empty:
            redemption_trades.to_excel(writer, sheet_name='申赎信号实际交易明细', index=False)
            print(f"申赎信号实际交易明细已保存: {len(redemption_trades)} 条记录")
        
        # 汇总统计
        if not summary_stats.empty:
            summary_stats.to_excel(writer, sheet_name='实际交易汇总统计', index=False)
            print(f"汇总统计已保存: {len(summary_stats)} 条记录")
    
    print(f"\nExcel文件已保存: {excel_path}")
    
    # 显示统计信息
    if not turnover_trades.empty:
        print(f"\n90分位成交额信号实际交易统计:")
        print(f"  实际交易总次数: {len(turnover_trades)}")
        print(f"  涉及ETF数量: {turnover_trades['ETF代码'].nunique()}")
        print(f"  平均收益率: {turnover_trades['期间收益率(%)'].mean():.4f}%")
        print(f"  胜率: {(turnover_trades['是否盈利'] == '是').mean() * 100:.2f}%")
        
        # 显示各ETF实际交易次数
        etf_counts = turnover_trades['ETF代码'].value_counts().sort_index()
        print(f"  各ETF实际交易次数:")
        for etf, count in etf_counts.items():
            print(f"    {etf}: {count}次")
    
    if not redemption_trades.empty:
        print(f"\n申赎信号实际交易统计:")
        print(f"  实际交易总次数: {len(redemption_trades)}")
        print(f"  涉及ETF数量: {redemption_trades['ETF代码'].nunique()}")
        print(f"  平均收益率: {redemption_trades['预估收益率(%)'].mean():.4f}%")
        print(f"  胜率: {(redemption_trades['是否盈利'] == '是').mean() * 100:.2f}%")
        
        # 显示各ETF实际交易次数
        etf_counts = redemption_trades['ETF代码'].value_counts().sort_index()
        print(f"  各ETF实际交易次数:")
        for etf, count in etf_counts.items():
            print(f"    {etf}: {count}次")

if __name__ == "__main__":
    main()
