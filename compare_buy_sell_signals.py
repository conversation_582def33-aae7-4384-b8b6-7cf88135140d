#!/usr/bin/env python3
"""
对比申赎买入信号与卖出信号的指数分布差异
"""

import pandas as pd
from datetime import datetime

def generate_buy_sell_comparison():
    """生成买入信号与卖出信号对比分析报告"""
    
    print("=== 生成申赎买入信号与卖出信号对比分析报告 ===")
    
    try:
        # 读取买入信号分析结果
        buy_df = pd.read_excel('指数信号分布分析结果_申赎买入95分位.xlsx', sheet_name='指数汇总统计')
        
        # 读取卖出信号分析结果
        sell_df = pd.read_excel('指数信号分布分析结果_申赎卖出5分位.xlsx', sheet_name='指数汇总统计')
        
        # 生成对比报告
        report_content = f"""# 申赎买入信号与卖出信号指数分布对比分析报告

## 报告概述

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**对比目的**: 全面比较申赎买入信号与卖出信号在各指数上的分布差异  
**数据源**: 
- 买入信号: signal_1_buy_pe_filtered_详细结果.csv (95分位买入)
- 卖出信号: signal_2_sell_pe_filtered_详细结果.csv (5分位卖出)

## 整体对比统计

### 基础统计对比

| 指标 | 申赎买入信号 | 申赎卖出信号 | 差异 | 差异说明 |
|------|-------------|-------------|------|---------|
| 覆盖指数数量 | {len(buy_df)} | {len(sell_df)} | {len(sell_df) - len(buy_df)} | {'卖出信号覆盖更全面' if len(sell_df) > len(buy_df) else '买入信号覆盖更全面' if len(buy_df) > len(sell_df) else '覆盖范围相同'} |
| 总信号数量 | {buy_df['总信号数量'].sum()} | {sell_df['总信号数量'].sum()} | {sell_df['总信号数量'].sum() - buy_df['总信号数量'].sum()} | {'卖出信号更频繁' if sell_df['总信号数量'].sum() > buy_df['总信号数量'].sum() else '买入信号更频繁'} |
| 平均信号密度 | {buy_df['平均每日信号数'].mean():.2f} | {sell_df['平均每日信号数'].mean():.2f} | {sell_df['平均每日信号数'].mean() - buy_df['平均每日信号数'].mean():.2f} | {'卖出信号密度更高' if sell_df['平均每日信号数'].mean() > buy_df['平均每日信号数'].mean() else '买入信号密度更高'} |
| 最高信号密度 | {buy_df['平均每日信号数'].max():.2f} | {sell_df['平均每日信号数'].max():.2f} | {sell_df['平均每日信号数'].max() - buy_df['平均每日信号数'].max():.2f} | {'卖出信号峰值更高' if sell_df['平均每日信号数'].max() > buy_df['平均每日信号数'].max() else '买入信号峰值更高'} |

### 信号密度分类对比

"""
        
        # 买入信号密度分类
        buy_high = len(buy_df[buy_df['信号密度等级'] == '高密度'])
        buy_medium = len(buy_df[buy_df['信号密度等级'] == '中密度'])
        buy_low = len(buy_df[buy_df['信号密度等级'] == '低密度'])
        
        # 卖出信号密度分类
        sell_high = len(sell_df[sell_df['信号密度等级'] == '高密度'])
        sell_medium = len(sell_df[sell_df['信号密度等级'] == '中密度'])
        sell_low = len(sell_df[sell_df['信号密度等级'] == '低密度'])
        
        report_content += f"""
| 密度等级 | 申赎买入信号 | 申赎卖出信号 | 差异分析 |
|---------|-------------|-------------|---------|
| 高密度(≥2个/天) | {buy_high}个({buy_high/len(buy_df)*100:.1f}%) | {sell_high}个({sell_high/len(sell_df)*100:.1f}%) | {'卖出信号高密度指数更多' if sell_high > buy_high else '买入信号高密度指数更多' if buy_high > sell_high else '高密度指数数量相同'} |
| 中密度(1-2个/天) | {buy_medium}个({buy_medium/len(buy_df)*100:.1f}%) | {sell_medium}个({sell_medium/len(sell_df)*100:.1f}%) | {'卖出信号中密度指数更多' if sell_medium > buy_medium else '买入信号中密度指数更多' if buy_medium > sell_medium else '中密度指数数量相同'} |
| 低密度(<1个/天) | {buy_low}个({buy_low/len(buy_df)*100:.1f}%) | {sell_low}个({sell_low/len(sell_df)*100:.1f}%) | {'卖出信号低密度指数更多' if sell_low > buy_low else '买入信号低密度指数更多' if buy_low > sell_low else '低密度指数数量相同'} |

## 各指数详细对比分析

### 指数信号对比表

| 指数代码 | 买入信号数量 | 卖出信号数量 | 买入密度 | 卖出密度 | 买入最大单日 | 卖出最大单日 | 信号特征分析 |
|---------|-------------|-------------|---------|---------|-------------|-------------|-------------|
"""
        
        # 合并两个数据框进行对比
        buy_df_indexed = buy_df.set_index('指数代码')
        sell_df_indexed = sell_df.set_index('指数代码')
        
        # 获取所有指数
        all_indices = set(buy_df['指数代码'].tolist() + sell_df['指数代码'].tolist())
        
        for index_code in sorted(all_indices):
            buy_signals = buy_df_indexed.loc[index_code, '总信号数量'] if index_code in buy_df_indexed.index else 0
            sell_signals = sell_df_indexed.loc[index_code, '总信号数量'] if index_code in sell_df_indexed.index else 0
            buy_density = buy_df_indexed.loc[index_code, '平均每日信号数'] if index_code in buy_df_indexed.index else 0
            sell_density = sell_df_indexed.loc[index_code, '平均每日信号数'] if index_code in sell_df_indexed.index else 0
            buy_max = buy_df_indexed.loc[index_code, '最大单日信号数'] if index_code in buy_df_indexed.index else 0
            sell_max = sell_df_indexed.loc[index_code, '最大单日信号数'] if index_code in sell_df_indexed.index else 0
            
            # 分析信号特征
            if buy_signals > 0 and sell_signals > 0:
                if sell_signals > buy_signals * 1.5:
                    feature = "卖出主导"
                elif buy_signals > sell_signals * 1.5:
                    feature = "买入主导"
                else:
                    feature = "均衡"
            elif buy_signals > 0:
                feature = "仅买入"
            elif sell_signals > 0:
                feature = "仅卖出"
            else:
                feature = "无信号"
            
            report_content += f"| {index_code} | {buy_signals} | {sell_signals} | {buy_density:.2f} | {sell_density:.2f} | {buy_max} | {sell_max} | {feature} |\n"
        
        # 分析各指数的信号特征
        report_content += f"""

### 各指数信号特征分析

"""
        
        for index_code in sorted(all_indices):
            buy_signals = buy_df_indexed.loc[index_code, '总信号数量'] if index_code in buy_df_indexed.index else 0
            sell_signals = sell_df_indexed.loc[index_code, '总信号数量'] if index_code in sell_df_indexed.index else 0
            buy_density = buy_df_indexed.loc[index_code, '平均每日信号数'] if index_code in buy_df_indexed.index else 0
            sell_density = sell_df_indexed.loc[index_code, '平均每日信号数'] if index_code in sell_df_indexed.index else 0
            
            # 获取指数名称（简化版）
            index_names = {
                300: "沪深300", 852: "中证1000", 399006: "创业板指", 
                16: "上证50", 905: "中证500", 688: "科创板",
                399330: "深证100", 746059: "科创50", 10: "上证180", 906: "中证800"
            }
            index_name = index_names.get(index_code, f"指数{index_code}")
            
            report_content += f"""
#### {index_name} (指数{index_code})

**信号对比**:
- 买入信号: {buy_signals}个 (密度{buy_density:.2f}个/天)
- 卖出信号: {sell_signals}个 (密度{sell_density:.2f}个/天)
- 信号比例: 买入:卖出 = {buy_signals}:{sell_signals}

**特征分析**:
"""
            
            if buy_signals > 0 and sell_signals > 0:
                ratio = sell_signals / buy_signals
                if ratio > 2:
                    report_content += f"- **卖出主导型**: 卖出信号是买入信号的{ratio:.1f}倍，风险预警功能突出\n"
                elif ratio > 1.5:
                    report_content += f"- **卖出偏向型**: 卖出信号较多，适合防御性策略\n"
                elif ratio < 0.5:
                    report_content += f"- **买入主导型**: 买入信号较多，适合积极投资策略\n"
                elif ratio < 0.67:
                    report_content += f"- **买入偏向型**: 买入信号较多，适合成长性投资\n"
                else:
                    report_content += f"- **均衡型**: 买入卖出信号相对均衡，适合平衡策略\n"
            elif buy_signals > 0:
                report_content += f"- **纯买入型**: 仅有买入信号，适合价值发现策略\n"
            elif sell_signals > 0:
                report_content += f"- **纯卖出型**: 仅有卖出信号，适合风险管理策略\n"
            else:
                report_content += f"- **无信号型**: 该指数在分析期间无明显信号\n"
        
        # 投资策略建议
        report_content += f"""

## 投资策略建议

### 基于信号特征的策略分类

#### 1. 卖出主导型指数策略
"""
        
        # 找出卖出主导的指数
        dominant_sell_indices = []
        for index_code in sorted(all_indices):
            buy_signals = buy_df_indexed.loc[index_code, '总信号数量'] if index_code in buy_df_indexed.index else 0
            sell_signals = sell_df_indexed.loc[index_code, '总信号数量'] if index_code in sell_df_indexed.index else 0
            if sell_signals > 0 and buy_signals > 0 and sell_signals / buy_signals > 1.5:
                dominant_sell_indices.append(index_code)
        
        if dominant_sell_indices:
            report_content += f"- **目标指数**: {', '.join(map(str, dominant_sell_indices))}\n"
            report_content += f"- **策略特点**: 重点关注风险管理，及时止损\n"
            report_content += f"- **适用场景**: 市场下跌期，防御性投资\n"
        else:
            report_content += f"- **目标指数**: 无明显卖出主导指数\n"
        
        report_content += f"""

#### 2. 买入主导型指数策略
"""
        
        # 找出买入主导的指数
        dominant_buy_indices = []
        for index_code in sorted(all_indices):
            buy_signals = buy_df_indexed.loc[index_code, '总信号数量'] if index_code in buy_df_indexed.index else 0
            sell_signals = sell_df_indexed.loc[index_code, '总信号数量'] if index_code in sell_df_indexed.index else 0
            if buy_signals > 0 and sell_signals > 0 and buy_signals / sell_signals > 1.5:
                dominant_buy_indices.append(index_code)
        
        if dominant_buy_indices:
            report_content += f"- **目标指数**: {', '.join(map(str, dominant_buy_indices))}\n"
            report_content += f"- **策略特点**: 重点关注价值发现，积极建仓\n"
            report_content += f"- **适用场景**: 市场上涨期，成长性投资\n"
        else:
            report_content += f"- **目标指数**: 无明显买入主导指数\n"
        
        report_content += f"""

#### 3. 均衡型指数策略
"""
        
        # 找出均衡的指数
        balanced_indices = []
        for index_code in sorted(all_indices):
            buy_signals = buy_df_indexed.loc[index_code, '总信号数量'] if index_code in buy_df_indexed.index else 0
            sell_signals = sell_df_indexed.loc[index_code, '总信号数量'] if index_code in sell_df_indexed.index else 0
            if buy_signals > 0 and sell_signals > 0:
                ratio = sell_signals / buy_signals
                if 0.67 <= ratio <= 1.5:
                    balanced_indices.append(index_code)
        
        if balanced_indices:
            report_content += f"- **目标指数**: {', '.join(map(str, balanced_indices))}\n"
            report_content += f"- **策略特点**: 买入卖出信号均衡，适合全天候策略\n"
            report_content += f"- **适用场景**: 震荡市场，平衡投资\n"
        else:
            report_content += f"- **目标指数**: 无明显均衡指数\n"
        
        report_content += f"""

### 组合投资建议

#### 1. 攻守兼备组合
- **核心持仓**: 选择买入信号较多的指数作为主要投资标的
- **风险对冲**: 配置卖出信号较多的指数进行风险管理
- **比例建议**: 核心持仓70%，风险对冲30%

#### 2. 信号密度匹配策略
- **高密度指数**: 适合短期交易，快速响应
- **中密度指数**: 适合中期投资，稳健收益
- **低密度指数**: 适合长期配置，价值投资

#### 3. 动态调整策略
- **牛市期**: 重点关注买入信号，增加成长性指数配置
- **熊市期**: 重点关注卖出信号，增加防御性指数配置
- **震荡期**: 平衡买入卖出信号，选择均衡型指数

## 分析结论

### 1. 信号特征差异
- **买入信号**: 总量{buy_df['总信号数量'].sum()}个，平均密度{buy_df['平均每日信号数'].mean():.2f}个/天
- **卖出信号**: 总量{sell_df['总信号数量'].sum()}个，平均密度{sell_df['平均每日信号数'].mean():.2f}个/天
- **主要差异**: {'卖出信号更频繁，风险预警功能更强' if sell_df['总信号数量'].sum() > buy_df['总信号数量'].sum() else '买入信号更频繁，投资机会更多'}

### 2. 指数覆盖特征
- **共同覆盖**: {len(set(buy_df['指数代码']) & set(sell_df['指数代码']))}个指数
- **买入独有**: {len(set(buy_df['指数代码']) - set(sell_df['指数代码']))}个指数
- **卖出独有**: {len(set(sell_df['指数代码']) - set(buy_df['指数代码']))}个指数

### 3. 投资策略价值
1. **互补性强**: 买入卖出信号形成完整的投资决策体系
2. **风险可控**: 卖出信号提供及时的风险预警
3. **机会捕捉**: 买入信号帮助识别投资机会
4. **策略灵活**: 可根据市场环境调整信号权重

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存报告
        report_path = '申赎买入卖出信号指数分布对比分析报告.md'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"对比分析报告已保存: {report_path}")
        
        # 显示关键结论
        print(f"\n=== 买入卖出信号对比关键结论 ===")
        print(f"1. 买入信号: {buy_df['总信号数量'].sum()}个, 平均密度{buy_df['平均每日信号数'].mean():.2f}个/天")
        print(f"2. 卖出信号: {sell_df['总信号数量'].sum()}个, 平均密度{sell_df['平均每日信号数'].mean():.2f}个/天")
        print(f"3. 信号比例: 卖出:买入 = {sell_df['总信号数量'].sum()}:{buy_df['总信号数量'].sum()}")
        print(f"4. 覆盖指数: 买入{len(buy_df)}个, 卖出{len(sell_df)}个")
        print(f"5. 策略建议: {'卖出信号更适合风险管理' if sell_df['总信号数量'].sum() > buy_df['总信号数量'].sum() else '买入信号更适合价值发现'}")
        
    except Exception as e:
        print(f"生成对比报告时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    generate_buy_sell_comparison()
