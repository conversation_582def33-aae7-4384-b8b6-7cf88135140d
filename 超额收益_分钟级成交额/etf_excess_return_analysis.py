# -*- coding: utf-8 -*-
"""
ETF择时策略超额收益分析
计算ETF通过买入信号择时后相对跟踪指数的超额收益

策略逻辑：
1. ETF发出买入信号后，以下一交易日开盘价买入，持有15个交易日后以收盘价卖出
2. 若ETF日内发出多个买入信号，则当日整体记为1个买信号
3. 在持有期间若出现新的买入信号，则忽略该买入信号
4. 超额收益 = ETF择时累计收益率 - 跟踪指数累计涨幅
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置pandas显示选项
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)

def load_data():
    """加载所有必要的数据"""
    print("正在加载数据...")
    
    # 1. 加载ETF跟踪指数映射表
    etf_mapping = pd.read_excel('../市盈率信息/ETF跟踪指数.xlsx')
    etf_mapping['跟踪指数代码'] = etf_mapping['跟踪指数代码'].astype(str).str.zfill(6)
    
    # 2. 加载指数行情数据
    index_data = pd.read_excel('../指数行情序列.xlsx')
    index_data['时间'] = pd.to_datetime(index_data['时间'])
    index_data['代码'] = index_data['代码'].str.replace('.SH', '').str.replace('.SZ', '').str.zfill(6)
    
    # 3. 加载信号明细数据
    signals_path = '../回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv'
    signals_df = pd.read_csv(signals_path)
    signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
    signals_df['date'] = signals_df['datetime'].dt.date
    
    print(f"ETF映射关系: {len(etf_mapping)} 条")
    print(f"指数行情数据: {len(index_data)} 条")
    print(f"信号明细数据: {len(signals_df)} 条")
    
    return etf_mapping, index_data, signals_df

def get_trading_days(index_data, start_date, end_date):
    """获取指定时间范围内的交易日"""
    trading_days = index_data[
        (index_data['时间'].dt.date >= start_date) & 
        (index_data['时间'].dt.date <= end_date)
    ]['时间'].dt.date.unique()
    return sorted(trading_days)

def generate_buy_signals(signals_df, start_date, end_date):
    """
    生成买入信号
    规则：
    1. 只考虑通过PE筛选的信号
    2. 只考虑80分位以上的信号
    3. 每日每个ETF最多一个买入信号
    """
    # 筛选有效信号
    valid_signals = signals_df[
        (signals_df['pe_filter_passed'] == True) &
        (signals_df['threshold'] >= 80) &
        (signals_df['date'] >= start_date) &
        (signals_df['date'] <= end_date)
    ].copy()
    
    # 每日每个ETF只保留一个信号（取最早的信号）
    daily_signals = valid_signals.groupby(['etf_code', 'date']).first().reset_index()
    
    print(f"有效信号数: {len(valid_signals)}")
    print(f"每日合并后信号数: {len(daily_signals)}")
    
    return daily_signals

def calculate_etf_timing_returns(etf_code, signals, index_data, etf_mapping, trading_days):
    """
    计算单个ETF的择时收益
    """
    # 获取该ETF对应的指数代码
    etf_row = etf_mapping[etf_mapping['ETF代码'] == etf_code]
    if etf_row.empty:
        print(f"警告: ETF {etf_code} 未找到对应的指数")
        return None
    
    index_code = etf_row.iloc[0]['跟踪指数代码']
    
    # 获取指数行情数据 (处理格式不匹配问题)
    index_prices = index_data[index_data['代码'] == index_code].copy()
    if index_prices.empty and index_code == '746059':
        # 特殊处理746059指数的格式问题
        index_prices = index_data[index_data['代码'] == '746059.MSI'].copy()
        if not index_prices.empty:
            print(f"注意: 指数 {index_code} 使用 746059.MSI 数据")

    if index_prices.empty:
        print(f"警告: 指数 {index_code} 未找到行情数据")
        return None
    
    index_prices = index_prices.sort_values('时间')
    index_prices['date'] = index_prices['时间'].dt.date
    
    # 获取该ETF的信号
    etf_signals = signals[signals['etf_code'] == etf_code].copy()
    etf_signals = etf_signals.sort_values('date')
    
    if etf_signals.empty:
        return {
            'etf_code': etf_code,
            'index_code': index_code,
            'total_trades': 0,
            'successful_trades': 0,
            'total_return': 0.0,
            'trades': []
        }
    
    trades = []
    holding_end_date = None
    
    for _, signal in etf_signals.iterrows():
        signal_date = signal['date']
        
        # 如果还在持有期内，跳过新信号
        if holding_end_date and signal_date <= holding_end_date:
            continue
        
        # 找到下一个交易日作为买入日
        signal_date_idx = None
        for i, td in enumerate(trading_days):
            if td == signal_date:
                signal_date_idx = i
                break
        
        if signal_date_idx is None or signal_date_idx + 1 >= len(trading_days):
            continue
        
        buy_date = trading_days[signal_date_idx + 1]
        
        # 找到15个交易日后的卖出日
        if signal_date_idx + 16 >= len(trading_days):
            continue
        
        sell_date = trading_days[signal_date_idx + 16]
        
        # 获取买入日开盘价和卖出日收盘价
        buy_price_data = index_prices[index_prices['date'] == buy_date]
        sell_price_data = index_prices[index_prices['date'] == sell_date]
        
        if buy_price_data.empty or sell_price_data.empty:
            continue
        
        buy_price = buy_price_data.iloc[0]['开盘价(元)']
        sell_price = sell_price_data.iloc[0]['收盘价(元)']
        
        if pd.isna(buy_price) or pd.isna(sell_price) or buy_price <= 0:
            continue
        
        # 计算收益率
        return_rate = (sell_price - buy_price) / buy_price
        
        trades.append({
            'signal_date': signal_date,
            'buy_date': buy_date,
            'sell_date': sell_date,
            'buy_price': buy_price,
            'sell_price': sell_price,
            'return': return_rate
        })
        
        # 更新持有结束日期
        holding_end_date = sell_date
    
    # 计算总收益率（复利）
    total_return = 1.0
    for trade in trades:
        total_return *= (1 + trade['return'])
    total_return -= 1
    
    successful_trades = len([t for t in trades if t['return'] > 0])
    
    return {
        'etf_code': etf_code,
        'index_code': index_code,
        'total_trades': len(trades),
        'successful_trades': successful_trades,
        'win_rate': successful_trades / len(trades) * 100 if trades else 0,
        'total_return': total_return,
        'trades': trades
    }

def calculate_index_benchmark_return(index_code, index_data, start_date, end_date):
    """
    计算指数基准收益率
    """
    index_prices = index_data[index_data['代码'] == index_code].copy()
    if index_prices.empty and index_code == '746059':
        # 特殊处理746059指数的格式问题
        index_prices = index_data[index_data['代码'] == '746059.MSI'].copy()

    if index_prices.empty:
        return 0.0
    
    index_prices = index_prices.sort_values('时间')
    
    # 获取起始日期的开盘价和结束日期的收盘价
    start_data = index_prices[index_prices['时间'].dt.date >= start_date]
    end_data = index_prices[index_prices['时间'].dt.date <= end_date]
    
    if start_data.empty or end_data.empty:
        return 0.0
    
    start_price = start_data.iloc[0]['开盘价(元)']
    end_price = end_data.iloc[-1]['收盘价(元)']
    
    if pd.isna(start_price) or pd.isna(end_price) or start_price <= 0:
        return 0.0
    
    return (end_price - start_price) / start_price

def main():
    """主函数"""
    print("=== ETF择时策略超额收益分析 ===")
    
    # 分析时间范围
    start_date = pd.to_datetime('2024-04-30').date()
    end_date = pd.to_datetime('2025-04-30').date()
    
    print(f"分析时间范围: {start_date} 到 {end_date}")
    
    # 加载数据
    etf_mapping, index_data, signals_df = load_data()
    
    # 获取交易日
    trading_days = get_trading_days(index_data, start_date, end_date)
    print(f"交易日数量: {len(trading_days)}")
    
    # 生成买入信号
    buy_signals = generate_buy_signals(signals_df, start_date, end_date)
    
    # 计算每个ETF的择时收益
    results = []
    etf_codes = sorted(buy_signals['etf_code'].unique())
    
    print(f"\n开始计算 {len(etf_codes)} 个ETF的择时收益...")
    
    for etf_code in etf_codes:
        print(f"正在处理 ETF {etf_code}...")
        
        result = calculate_etf_timing_returns(
            etf_code, buy_signals, index_data, etf_mapping, trading_days
        )
        
        if result:
            # 计算对应指数的基准收益
            benchmark_return = calculate_index_benchmark_return(
                result['index_code'], index_data, start_date, end_date
            )
            
            result['benchmark_return'] = benchmark_return
            result['excess_return'] = result['total_return'] - benchmark_return
            
            results.append(result)
    
    # 生成分析报告
    generate_analysis_report(results, start_date, end_date)
    
    print("\n分析完成！结果已保存到 超额收益/ 文件夹中。")

def generate_analysis_report(results, start_date, end_date):
    """生成分析报告"""
    
    # 创建汇总表
    summary_data = []
    for result in results:
        summary_data.append({
            'ETF代码': result['etf_code'],
            '跟踪指数': result['index_code'],
            '交易次数': result['total_trades'],
            '成功交易': result['successful_trades'],
            '胜率(%)': round(result['win_rate'], 2),
            '择时累计收益率(%)': round(result['total_return'] * 100, 4),
            '指数基准收益率(%)': round(result['benchmark_return'] * 100, 4),
            '超额收益率(%)': round(result['excess_return'] * 100, 4)
        })
    
    summary_df = pd.DataFrame(summary_data)
    
    # 计算整体统计
    total_trades = summary_df['交易次数'].sum()
    total_successful = summary_df['成功交易'].sum()
    avg_win_rate = total_successful / total_trades * 100 if total_trades > 0 else 0
    
    # 加权平均收益率
    weights = summary_df['交易次数'] / summary_df['交易次数'].sum()
    weighted_timing_return = (summary_df['择时累计收益率(%)'] * weights).sum()
    weighted_benchmark_return = (summary_df['指数基准收益率(%)'] * weights).sum()
    weighted_excess_return = weighted_timing_return - weighted_benchmark_return
    
    # 保存Excel文件
    excel_path = 'ETF择时策略超额收益分析.xlsx'
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        summary_df.to_excel(writer, sheet_name='超额收益汇总', index=False)
        
        # 添加统计摘要
        stats_data = [
            ['分析时间范围', f'{start_date} 到 {end_date}'],
            ['参与ETF数量', len(results)],
            ['总交易次数', total_trades],
            ['总成功交易', total_successful],
            ['平均胜率(%)', round(avg_win_rate, 2)],
            ['加权平均择时收益率(%)', round(weighted_timing_return, 4)],
            ['加权平均基准收益率(%)', round(weighted_benchmark_return, 4)],
            ['加权平均超额收益率(%)', round(weighted_excess_return, 4)]
        ]
        stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
        stats_df.to_excel(writer, sheet_name='统计摘要', index=False)
    
    print(f"Excel报告已保存: {excel_path}")
    
    # 生成Markdown报告
    generate_markdown_report(summary_df, stats_data, start_date, end_date)

def generate_markdown_report(summary_df, stats_data, start_date, end_date):
    """生成Markdown分析报告"""
    
    md_content = f"""# ETF择时策略超额收益分析报告

## 分析概述

**分析时间范围**: {start_date} 到 {end_date}  
**策略说明**: ETF发出买入信号后，以下一交易日开盘价买入，持有15个交易日后以收盘价卖出

## 统计摘要

"""
    
    for stat in stats_data:
        md_content += f"- **{stat[0]}**: {stat[1]}\n"
    
    md_content += f"""

## 各ETF详细结果

| ETF代码 | 跟踪指数 | 交易次数 | 胜率(%) | 择时收益率(%) | 基准收益率(%) | 超额收益率(%) |
|---------|----------|----------|---------|---------------|---------------|---------------|
"""
    
    for _, row in summary_df.iterrows():
        md_content += f"| {row['ETF代码']} | {row['跟踪指数']} | {row['交易次数']} | {row['胜率(%)']} | {row['择时累计收益率(%)']} | {row['指数基准收益率(%)']} | {row['超额收益率(%)']} |\n"
    
    md_content += f"""

## 分析结论

### 1. 策略有效性
- 平均胜率: {stats_data[4][1]}%
- 加权平均超额收益率: {stats_data[7][1]}%

### 2. 风险收益特征
- 择时策略通过技术信号筛选，在一定程度上实现了超额收益
- 不同ETF的表现存在差异，需要进一步分析原因

### 3. 改进建议
- 可以考虑优化信号筛选条件
- 可以调整持有周期以获得更好的风险调整收益
- 建议结合更多基本面指标进行综合判断

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存Markdown文件
    md_path = 'ETF择时策略超额收益分析报告.md'
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(md_content)
    
    print(f"Markdown报告已保存: {md_path}")

if __name__ == "__main__":
    main()
