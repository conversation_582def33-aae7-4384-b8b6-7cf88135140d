#!/usr/bin/env python3
"""
分析基于申赎信号的跟踪相同指数ETF信号相关性
数据源：
1. 市盈率信息/ETF跟踪指数.xlsx
2. 分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv
"""

import pandas as pd
import numpy as np
from datetime import datetime
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

def standardize_etf_code(code):
    """标准化ETF代码，去除后缀"""
    if pd.isna(code):
        return None
    
    code_str = str(code)
    # 去除常见的后缀
    suffixes = ['.SH', '.SZ', '.XSHE', '.XSHG']
    for suffix in suffixes:
        if code_str.endswith(suffix):
            code_str = code_str[:-len(suffix)]
    
    # 确保是6位数字格式
    try:
        return int(code_str)
    except:
        return None

def load_complete_redemption_data():
    """加载完整的申赎信号数据"""
    print("=== 加载申赎信号完整数据 ===")
    
    # 1. 加载ETF跟踪指数映射关系
    etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
    print(f"ETF映射数据: {len(etf_mapping)} 个ETF")
    print(f"ETF映射列名: {etf_mapping.columns.tolist()}")
    
    # 2. 加载申赎信号数据
    signals_df = pd.read_csv('分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv')
    print(f"申赎信号数据: {len(signals_df)} 条")
    print(f"申赎信号列名: {signals_df.columns.tolist()}")
    
    # 转换时间格式
    if 'signal_date' in signals_df.columns:
        signals_df['signal_date'] = pd.to_datetime(signals_df['signal_date'])
        signals_df['date'] = signals_df['signal_date'].dt.date
    else:
        print("未找到signal_date列")
        return pd.DataFrame(), pd.DataFrame()
    
    # 标准化ETF代码
    if 'etf_code' in signals_df.columns:
        signals_df['etf_code_std'] = signals_df['etf_code'].apply(standardize_etf_code)
    else:
        print("未找到etf_code列")
        return pd.DataFrame(), pd.DataFrame()
    
    signals_df = signals_df.dropna(subset=['etf_code_std'])
    
    # 每日每个ETF只保留一个信号
    daily_signals = signals_df.groupby(['etf_code_std', 'date']).first().reset_index()
    daily_signals = daily_signals.sort_values(['etf_code_std', 'date'])
    
    print(f"每日合并后申赎信号: {len(daily_signals)} 条")
    print(f"涉及ETF数量: {daily_signals['etf_code_std'].nunique()}")
    
    # 显示各ETF信号数量
    etf_signal_counts = daily_signals['etf_code_std'].value_counts().sort_index()
    print(f"\n各ETF申赎信号数量:")
    for etf_code, count in etf_signal_counts.items():
        print(f"  ETF {etf_code}: {count}个")
    
    return etf_mapping, daily_signals

def analyze_redemption_etf_coverage(etf_mapping, daily_signals):
    """分析申赎信号ETF覆盖情况"""
    print(f"\n=== 申赎信号ETF覆盖分析 ===")
    
    # 所有ETF列表
    all_etfs = set(etf_mapping['ETF代码'].tolist())
    signal_etfs = set(daily_signals['etf_code_std'].tolist())
    
    # 有信号的ETF
    etfs_with_signals = signal_etfs.intersection(all_etfs)
    # 没有信号的ETF
    etfs_without_signals = all_etfs - signal_etfs
    
    print(f"总ETF数量: {len(all_etfs)}")
    print(f"有申赎信号的ETF: {len(etfs_with_signals)} 个")
    print(f"没有申赎信号的ETF: {len(etfs_without_signals)} 个")
    
    # 分析没有信号的ETF
    no_signal_analysis = []
    for etf_code in etfs_without_signals:
        etf_info = etf_mapping[etf_mapping['ETF代码'] == etf_code]
        if not etf_info.empty:
            index_code = etf_info.iloc[0]['跟踪指数代码']
            no_signal_analysis.append({
                'ETF代码': etf_code,
                '跟踪指数代码': index_code,
                '申赎信号数量': 0,
                '状态': '无申赎信号'
            })
    
    # 分析有信号的ETF
    signal_analysis = []
    for etf_code in etfs_with_signals:
        etf_signals = daily_signals[daily_signals['etf_code_std'] == etf_code]
        etf_info = etf_mapping[etf_mapping['ETF代码'] == etf_code]
        if not etf_info.empty:
            index_code = etf_info.iloc[0]['跟踪指数代码']
            signal_analysis.append({
                'ETF代码': etf_code,
                '跟踪指数代码': index_code,
                '申赎信号数量': len(etf_signals),
                '状态': '有申赎信号'
            })
    
    return signal_analysis, no_signal_analysis

def analyze_redemption_index_groups(etf_mapping, signal_analysis, no_signal_analysis):
    """分析申赎信号指数分组情况"""
    print(f"\n=== 申赎信号指数分组分析 ===")
    
    # 合并所有ETF分析结果
    all_etf_analysis = signal_analysis + no_signal_analysis
    all_etf_df = pd.DataFrame(all_etf_analysis)
    
    # 按指数分组
    index_groups = all_etf_df.groupby('跟踪指数代码').agg({
        'ETF代码': ['count', lambda x: list(x)],
        '申赎信号数量': 'sum',
        '状态': lambda x: list(x)
    }).reset_index()
    
    index_groups.columns = ['跟踪指数代码', 'ETF总数', 'ETF列表', '总申赎信号数', '状态列表']
    
    # 分类指数组
    single_etf_indices = []  # 只有1个ETF的指数
    multi_etf_indices = []   # 有多个ETF的指数
    
    for _, row in index_groups.iterrows():
        index_code = row['跟踪指数代码']
        etf_count = row['ETF总数']
        etf_list = row['ETF列表']
        total_signals = row['总申赎信号数']
        status_list = row['状态列表']
        
        # 统计有信号和无信号的ETF数量
        signal_etf_count = status_list.count('有申赎信号')
        no_signal_etf_count = status_list.count('无申赎信号')
        
        index_info = {
            '跟踪指数代码': index_code,
            'ETF总数': etf_count,
            'ETF列表': etf_list,
            '有申赎信号ETF数': signal_etf_count,
            '无申赎信号ETF数': no_signal_etf_count,
            '总申赎信号数': total_signals,
            '平均申赎信号数': total_signals / signal_etf_count if signal_etf_count > 0 else 0
        }
        
        if etf_count == 1:
            single_etf_indices.append(index_info)
        else:
            multi_etf_indices.append(index_info)
    
    print(f"只有1个ETF的指数: {len(single_etf_indices)} 个")
    print(f"有多个ETF的指数: {len(multi_etf_indices)} 个")
    
    return single_etf_indices, multi_etf_indices, all_etf_df

def calculate_redemption_correlation_for_multi_etf(multi_etf_indices, daily_signals):
    """计算多ETF指数的申赎信号相关性"""
    print(f"\n=== 计算多ETF指数申赎信号相关性 ===")
    
    correlation_results = []
    
    for index_info in multi_etf_indices:
        index_code = index_info['跟踪指数代码']
        etf_list = index_info['ETF列表']
        
        print(f"分析指数 {index_code}, ETF: {etf_list}")
        
        # 获取有信号的ETF
        etf_signals = {}
        for etf_code in etf_list:
            etf_data = daily_signals[daily_signals['etf_code_std'] == etf_code]
            if not etf_data.empty:
                etf_signals[etf_code] = etf_data
                print(f"  ETF {etf_code}: {len(etf_data)} 个申赎信号")
        
        # 计算两两相关性
        valid_etfs = list(etf_signals.keys())
        if len(valid_etfs) >= 2:
            for etf1, etf2 in combinations(valid_etfs, 2):
                # 计算Jaccard相似度
                etf1_dates = set(etf_signals[etf1]['date'])
                etf2_dates = set(etf_signals[etf2]['date'])
                
                overlap_dates = etf1_dates.intersection(etf2_dates)
                union_dates = etf1_dates.union(etf2_dates)
                
                jaccard_similarity = len(overlap_dates) / len(union_dates) if len(union_dates) > 0 else 0
                
                correlation_results.append({
                    '跟踪指数代码': index_code,
                    'ETF1代码': etf1,
                    'ETF2代码': etf2,
                    'ETF1申赎信号数': len(etf1_dates),
                    'ETF2申赎信号数': len(etf2_dates),
                    '重叠申赎信号数': len(overlap_dates),
                    'Jaccard相似度': jaccard_similarity,
                    '重叠日期': sorted([date.strftime('%Y-%m-%d') for date in overlap_dates])
                })
                
                print(f"    ETF {etf1} vs ETF {etf2}: Jaccard相似度 {jaccard_similarity:.4f}, 重叠 {len(overlap_dates)} 个")
        else:
            print(f"  指数 {index_code} 有申赎信号的ETF少于2个，无法计算相关性")
    
    return correlation_results

def generate_redemption_complete_report(single_etf_indices, multi_etf_indices, all_etf_df, correlation_results, signal_analysis, no_signal_analysis):
    """生成申赎信号完整分析报告"""
    
    print(f"\n=== 生成申赎信号完整分析报告 ===")
    
    # 统计数据
    total_etfs = len(all_etf_df)
    etfs_with_signals = len([x for x in signal_analysis])
    etfs_without_signals = len([x for x in no_signal_analysis])
    total_indices = len(single_etf_indices) + len(multi_etf_indices)
    
    report_content = f"""# 申赎信号ETF完整分析报告

## 报告概述

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**分析范围**: 所有ETF的申赎信号（95分位买入，PE筛选）  
**数据源**: 分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv

## 整体统计概览

### 基础统计
- **总ETF数量**: {total_etfs} 个
- **有申赎信号ETF数量**: {etfs_with_signals} 个 ({etfs_with_signals/total_etfs*100:.1f}%)
- **无申赎信号ETF数量**: {etfs_without_signals} 个 ({etfs_without_signals/total_etfs*100:.1f}%)
- **总指数数量**: {total_indices} 个
- **单ETF指数数量**: {len(single_etf_indices)} 个
- **多ETF指数数量**: {len(multi_etf_indices)} 个

### 申赎信号分布统计
- **总申赎信号数**: {sum([x['申赎信号数量'] for x in signal_analysis])} 个
- **平均每个有信号ETF的申赎信号数**: {sum([x['申赎信号数量'] for x in signal_analysis])/etfs_with_signals:.1f} 个

## 详细分析结果

### 1. 有申赎信号的ETF分析

#### 按申赎信号数量排序

| ETF代码 | 跟踪指数 | 申赎信号数量 | 信号密度等级 |
|---------|---------|-------------|-------------|
"""
    
    # 按信号数量排序
    signal_analysis_sorted = sorted(signal_analysis, key=lambda x: x['申赎信号数量'], reverse=True)
    
    for etf_info in signal_analysis_sorted:
        signal_count = etf_info['申赎信号数量']
        if signal_count >= 20:
            density = "高密度"
        elif signal_count >= 10:
            density = "中密度"
        elif signal_count >= 5:
            density = "低密度"
        else:
            density = "稀疏"
        
        report_content += f"| {etf_info['ETF代码']} | {etf_info['跟踪指数代码']} | {signal_count} | {density} |\n"
    
    report_content += f"""

### 2. 无申赎信号的ETF分析

#### 无申赎信号ETF列表

| ETF代码 | 跟踪指数 | 可能原因 |
|---------|---------|---------|
"""
    
    for etf_info in no_signal_analysis:
        # 判断可能的无信号原因
        index_code = etf_info['跟踪指数代码']
        same_index_etfs = [x for x in signal_analysis if x['跟踪指数代码'] == index_code]
        
        if same_index_etfs:
            reason = "申赎净额未达95分位或PE筛选未通过"
        else:
            reason = "该指数下所有ETF均无申赎信号"
        
        report_content += f"| {etf_info['ETF代码']} | {index_code} | {reason} |\n"
    
    report_content += f"""

### 3. 单ETF指数分析

#### 只有1个ETF的指数

| 指数代码 | ETF代码 | 申赎信号数量 | 分析说明 |
|---------|---------|-------------|---------|
"""
    
    for index_info in single_etf_indices:
        etf_code = index_info['ETF列表'][0]
        signal_count = index_info['总申赎信号数']
        
        if signal_count > 0:
            analysis = "独立申赎信号源，无相关性分析"
        else:
            analysis = "无申赎信号，无法分析"
        
        report_content += f"| {index_info['跟踪指数代码']} | {etf_code} | {signal_count} | {analysis} |\n"
    
    report_content += f"""

### 4. 多ETF指数申赎信号相关性分析

#### 各指数下ETF配置情况

| 指数代码 | ETF总数 | 有申赎信号ETF数 | 无申赎信号ETF数 | 总申赎信号数 | 可分析相关性 |
|---------|---------|----------------|----------------|-------------|-------------|
"""
    
    for index_info in multi_etf_indices:
        can_analyze = "是" if index_info['有申赎信号ETF数'] >= 2 else "否"
        report_content += f"| {index_info['跟踪指数代码']} | {index_info['ETF总数']} | {index_info['有申赎信号ETF数']} | {index_info['无申赎信号ETF数']} | {index_info['总申赎信号数']} | {can_analyze} |\n"
    
    # 添加相关性分析结果
    if correlation_results:
        correlation_df = pd.DataFrame(correlation_results)
        
        report_content += f"""

#### 多ETF指数申赎信号相关性详细结果

| 指数代码 | ETF1 | ETF2 | ETF1申赎信号数 | ETF2申赎信号数 | 重叠申赎信号数 | Jaccard相似度 | 相关性等级 |
|---------|------|------|---------------|---------------|---------------|-------------|-----------|
"""
        
        for _, row in correlation_df.iterrows():
            jaccard = row['Jaccard相似度']
            if jaccard >= 0.8:
                level = "极强相关"
            elif jaccard >= 0.6:
                level = "强相关"
            elif jaccard >= 0.4:
                level = "中等相关"
            else:
                level = "弱相关"
            
            report_content += f"| {row['跟踪指数代码']} | {row['ETF1代码']} | {row['ETF2代码']} | {row['ETF1申赎信号数']} | {row['ETF2申赎信号数']} | {row['重叠申赎信号数']} | {jaccard:.4f} | {level} |\n"
        
        # 相关性统计
        avg_correlation = correlation_df['Jaccard相似度'].mean()
        high_correlation_count = len(correlation_df[correlation_df['Jaccard相似度'] >= 0.6])
        
        report_content += f"""

#### 申赎信号相关性统计摘要
- **可分析相关性的ETF对数**: {len(correlation_df)} 对
- **平均Jaccard相似度**: {avg_correlation:.4f}
- **强相关ETF对数**: {high_correlation_count} 对 ({high_correlation_count/len(correlation_df)*100:.1f}%)
"""
    
    report_content += f"""

## 申赎信号完整性分析结论

### 1. ETF申赎信号覆盖情况
- **申赎信号覆盖率**: {etfs_with_signals/total_etfs*100:.1f}% ({etfs_with_signals}/{total_etfs})
- **申赎信号空白**: {etfs_without_signals} 个ETF无申赎信号

### 2. 指数分布特征
- **单ETF指数**: {len(single_etf_indices)} 个，无相关性分析需求
- **多ETF指数**: {len(multi_etf_indices)} 个，需要相关性管理

### 3. 申赎信号相关性管理建议

#### 对于多ETF指数
"""
    
    if correlation_results:
        correlation_df = pd.DataFrame(correlation_results)
        high_corr_indices = correlation_df[correlation_df['Jaccard相似度'] >= 0.6]['跟踪指数代码'].unique()
        
        report_content += f"""
- **高相关指数**: {len(high_corr_indices)} 个，需要避免重复配置
- **建议**: 每个高相关指数只选择1个最优ETF
"""
    
    report_content += f"""

#### 对于单ETF指数
- **独立性**: {len(single_etf_indices)} 个指数各有1个ETF，天然分散
- **建议**: 可以全部纳入申赎信号监控，无相关性风险

#### 对于无申赎信号ETF
- **排除建议**: {etfs_without_signals} 个ETF无申赎信号，暂时排除
- **监控建议**: 定期检查是否因市场环境变化而产生申赎信号

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存报告
    report_path = '申赎信号ETF完整分析报告.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    # 保存Excel结果
    excel_path = '申赎信号ETF完整分析结果.xlsx'
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        
        # 有申赎信号ETF
        pd.DataFrame(signal_analysis).to_excel(writer, sheet_name='有申赎信号ETF', index=False)
        
        # 无申赎信号ETF
        pd.DataFrame(no_signal_analysis).to_excel(writer, sheet_name='无申赎信号ETF', index=False)
        
        # 单ETF指数
        pd.DataFrame(single_etf_indices).to_excel(writer, sheet_name='单ETF指数', index=False)
        
        # 多ETF指数
        pd.DataFrame(multi_etf_indices).to_excel(writer, sheet_name='多ETF指数', index=False)
        
        # 申赎信号相关性分析
        if correlation_results:
            pd.DataFrame(correlation_results).to_excel(writer, sheet_name='申赎信号相关性分析', index=False)
        
        # 完整ETF列表
        all_etf_df.to_excel(writer, sheet_name='完整ETF列表', index=False)
    
    print(f"申赎信号完整分析报告已保存: {report_path}")
    print(f"申赎信号完整分析结果已保存: {excel_path}")
    
    return report_path, excel_path

def main():
    """主函数"""
    print("=== 申赎信号ETF完整分析 ===")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 加载数据
    etf_mapping, daily_signals = load_complete_redemption_data()
    
    if etf_mapping.empty or daily_signals.empty:
        print("数据加载失败")
        return
    
    # 分析ETF覆盖情况
    signal_analysis, no_signal_analysis = analyze_redemption_etf_coverage(etf_mapping, daily_signals)
    
    # 分析指数分组
    single_etf_indices, multi_etf_indices, all_etf_df = analyze_redemption_index_groups(etf_mapping, signal_analysis, no_signal_analysis)
    
    # 计算多ETF指数相关性
    correlation_results = calculate_redemption_correlation_for_multi_etf(multi_etf_indices, daily_signals)
    
    # 生成完整报告
    report_path, excel_path = generate_redemption_complete_report(
        single_etf_indices, multi_etf_indices, all_etf_df, 
        correlation_results, signal_analysis, no_signal_analysis
    )
    
    # 显示关键统计
    print(f"\n=== 申赎信号完整分析结果摘要 ===")
    print(f"总ETF数量: {len(all_etf_df)}")
    print(f"有申赎信号ETF: {len(signal_analysis)} 个")
    print(f"无申赎信号ETF: {len(no_signal_analysis)} 个")
    print(f"单ETF指数: {len(single_etf_indices)} 个")
    print(f"多ETF指数: {len(multi_etf_indices)} 个")
    if correlation_results:
        correlation_df = pd.DataFrame(correlation_results)
        print(f"可分析相关性ETF对: {len(correlation_df)} 对")
        print(f"平均相关性: {correlation_df['Jaccard相似度'].mean():.4f}")
    
    print(f"\n报告文件: {report_path}")
    print(f"数据文件: {excel_path}")

if __name__ == "__main__":
    main()
