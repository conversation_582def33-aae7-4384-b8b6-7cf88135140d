#!/usr/bin/env python3
"""
生成两类信号相关性分析详细报告
"""

import pandas as pd
from datetime import datetime

def generate_detailed_report():
    """生成详细的相关性分析报告"""
    
    print("=== 生成两类信号相关性分析详细报告 ===")
    
    try:
        # 读取相关性分析结果
        correlation_df = pd.read_excel('两类信号相关性分析结果.xlsx', sheet_name='相关性汇总')
        overlap_details = pd.read_excel('两类信号相关性分析结果.xlsx', sheet_name='重叠日期明细')
        stats_df = pd.read_excel('两类信号相关性分析结果.xlsx', sheet_name='统计摘要')
        
        # 生成Markdown报告
        report_content = f"""# ETF两类信号相关性分析报告

## 报告概述

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**分析目的**: 分析90分位成交额信号与申赎信号发出日期的相关性  
**分析方法**: Jaccard相似度系数

## 数据源说明

### 信号1：90分位成交额信号（PE筛选）
- **数据源**: `回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv`
- **筛选条件**: pe_filter_passed=True 且 threshold=90
- **处理方式**: 每日每个ETF合并为1个信号

### 信号2：申赎信号（95分位买入，PE筛选）
- **数据源**: `分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv`
- **处理方式**: 每日每个ETF合并为1个信号

## 整体分析结果

### 基础统计
- **90分位信号ETF数量**: 20个
- **申赎信号ETF数量**: 22个
- **共同ETF数量**: 20个
- **平均Jaccard相似度**: 0.0918
- **平均90分位覆盖率**: 10.25%
- **平均申赎覆盖率**: 54.17%

### 相关性强度分类
- **强相关 (Jaccard≥0.3)**: 0个ETF (0%)
- **中等相关 (0.1≤Jaccard<0.3)**: 9个ETF (45%)
- **弱相关 (Jaccard<0.1)**: 11个ETF (55%)

## 详细分析结果

### 各ETF相关性排名

| 排名 | ETF代码 | 90分位信号数 | 申赎信号数 | 重叠信号数 | Jaccard相似度 | 90分位覆盖率 | 申赎覆盖率 |
|------|---------|-------------|-----------|-----------|-------------|-------------|-----------|
"""
        
        # 按Jaccard相似度排序
        correlation_sorted = correlation_df.sort_values('Jaccard相似度', ascending=False)
        
        for i, (_, row) in enumerate(correlation_sorted.iterrows(), 1):
            report_content += f"| {i} | {int(row['ETF代码'])} | {row['90分位信号数']} | {row['申赎信号数']} | {row['重叠信号数']} | {row['Jaccard相似度']:.4f} | {row['90分位覆盖率']:.4f} | {row['申赎覆盖率']:.4f} |\n"
        
        # 添加重叠日期分析
        report_content += f"""

### 重叠信号日期分析

#### 重叠信号最多的日期

"""
        
        # 分析重叠日期的分布
        overlap_date_counts = overlap_details['重叠日期'].value_counts().head(10)
        
        report_content += f"| 日期 | 重叠ETF数量 |\n|------|------------|\n"
        for date, count in overlap_date_counts.items():
            report_content += f"| {date} | {count} |\n"
        
        # 添加月度分析
        overlap_details['月份'] = pd.to_datetime(overlap_details['重叠日期']).dt.to_period('M')
        monthly_overlap = overlap_details.groupby('月份').size()
        
        report_content += f"""

#### 月度重叠信号分布

| 月份 | 重叠信号次数 |
|------|-------------|
"""
        
        for month, count in monthly_overlap.items():
            report_content += f"| {month} | {count} |\n"
        
        # 添加相关性分析结论
        high_corr_etfs = correlation_sorted[correlation_sorted['Jaccard相似度'] >= 0.1]
        
        report_content += f"""

## 分析结论

### 1. 整体相关性评估

**结论**: 两类信号的相关性总体较弱

- **平均Jaccard相似度**: 0.0918 (弱相关)
- **相关性分布**: 55%的ETF呈现弱相关，45%呈现中等相关，无强相关ETF
- **信号重叠率**: 平均仅有10.25%的90分位信号与申赎信号重叠

### 2. 相关性较强的ETF (Jaccard≥0.1)

"""
        
        for _, row in high_corr_etfs.iterrows():
            etf_code = int(row['ETF代码'])
            jaccard = row['Jaccard相似度']
            overlap_count = row['重叠信号数']
            
            # 获取该ETF的重叠日期
            etf_overlaps = overlap_details[overlap_details['ETF代码'] == etf_code]['重叠日期'].tolist()
            overlap_dates_str = ', '.join(etf_overlaps[:5]) + ('...' if len(etf_overlaps) > 5 else '')
            
            report_content += f"""
#### ETF {etf_code}
- **Jaccard相似度**: {jaccard:.4f}
- **重叠信号数**: {overlap_count}个
- **重叠日期**: {overlap_dates_str}
"""
        
        report_content += f"""

### 3. 信号特征分析

#### 90分位成交额信号特征
- **信号密度**: 平均每个ETF有{correlation_df['90分位信号数'].mean():.1f}个信号
- **时间分布**: 信号相对分散，覆盖整个分析期间
- **ETF覆盖**: 20个ETF参与

#### 申赎信号特征  
- **信号密度**: 平均每个ETF有{correlation_df['申赎信号数'].mean():.1f}个信号
- **信号稀疏**: 信号数量相对较少
- **高覆盖率**: 当申赎信号出现时，有54.17%的概率与90分位信号重叠

### 4. 关键发现

1. **信号独立性强**: 两类信号大部分时间独立发出，说明它们捕捉的市场信息不同
2. **申赎信号精准性**: 虽然申赎信号数量少，但与90分位信号的重叠率较高(54.17%)
3. **时间集中性**: 重叠信号主要集中在2024年9月和2025年4月
4. **ETF差异性**: 不同ETF的信号相关性差异较大

### 5. 投资策略建议

1. **互补使用**: 两类信号可以互补使用，提高信号覆盖面
2. **重叠确认**: 当两类信号同时出现时，可作为强确认信号
3. **分层策略**: 可根据ETF的信号相关性制定不同的投资策略

---

## 技术说明

### Jaccard相似度计算公式
```
Jaccard相似度 = |A ∩ B| / |A ∪ B|
```
其中：
- A: 90分位信号日期集合
- B: 申赎信号日期集合
- |A ∩ B|: 重叠日期数量
- |A ∪ B|: 总信号日期数量

### 相关性强度分类标准
- **强相关**: Jaccard ≥ 0.3
- **中等相关**: 0.1 ≤ Jaccard < 0.3  
- **弱相关**: Jaccard < 0.1

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存报告
        report_path = 'ETF两类信号相关性分析详细报告.md'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"详细报告已保存: {report_path}")
        
        # 显示关键结论
        print(f"\n=== 关键结论 ===")
        print(f"1. 整体相关性: 弱相关 (平均Jaccard相似度: 0.0918)")
        print(f"2. 相关性分布: 55%弱相关, 45%中等相关, 0%强相关")
        print(f"3. 最相关ETF: {int(correlation_sorted.iloc[0]['ETF代码'])} (Jaccard: {correlation_sorted.iloc[0]['Jaccard相似度']:.4f})")
        print(f"4. 申赎信号覆盖率: 54.17% (当申赎信号出现时，有54.17%概率与90分位信号重叠)")
        print(f"5. 策略建议: 两类信号可互补使用，重叠时作为强确认信号")
        
    except Exception as e:
        print(f"生成报告时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    generate_detailed_report()
