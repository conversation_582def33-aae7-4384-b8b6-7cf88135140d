#!/usr/bin/env python3
"""
提取ETF信号详细日期信息
分别从90分位成交额信号和申赎信号中提取各ETF的信号发出日期
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def extract_90_percentile_signal_dates():
    """提取90分位成交额信号的详细日期"""
    print("=== 提取90分位成交额信号详细日期 ===")

    try:
        # 读取原始信号数据
        signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
        signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
        signals_df['date'] = signals_df['datetime'].dt.date

        # 筛选90分位且通过PE筛选的信号
        filtered_signals = signals_df[
            (signals_df['pe_filter_passed'] == True) &
            (signals_df['threshold'] == 90) &
            (signals_df['holding_period'] == 'next_15day_close')
        ].copy()

        print(f"90分位PE筛选后信号总数: {len(filtered_signals)}")

        # 分析时间范围
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()

        period_signals = filtered_signals[
            (filtered_signals['date'] >= start_date) &
            (filtered_signals['date'] <= end_date)
        ].copy()

        # 每日每个ETF只保留一个信号（取最早的）
        daily_signals = period_signals.groupby(['etf_code', 'date']).first().reset_index()
        daily_signals = daily_signals.sort_values(['etf_code', 'date'])

        print(f"分析期间内信号数: {len(daily_signals)}")
        print(f"涉及ETF数量: {daily_signals['etf_code'].nunique()}")

        # 按ETF整理信号日期
        etf_signal_dates = {}
        for etf_code, group in daily_signals.groupby('etf_code'):
            signal_dates = sorted(group['date'].tolist())
            etf_signal_dates[etf_code] = {
                'signal_count': len(signal_dates),
                'signal_dates': signal_dates,
                'date_strings': [date.strftime('%Y-%m-%d') for date in signal_dates]
            }

        return etf_signal_dates, daily_signals

    except Exception as e:
        print(f"提取90分位信号日期时出错: {e}")
        return {}, pd.DataFrame()

def extract_redemption_signal_dates():
    """提取申赎信号的详细日期"""
    print("\n=== 提取申赎信号详细日期 ===")
    
    try:
        # 查找申赎信号数据文件
        possible_paths = [
            '分析结果_PE筛选_20240430_20250430/PE筛选后信号数据汇总表.csv',
            '分析结果_买入95分位_卖出5分位_20240430_20250430/PE筛选后信号数据汇总表.csv',
            '超额收益_申赎信号/PE筛选后信号数据汇总表.csv'
        ]
        
        signals_file = None
        for path in possible_paths:
            try:
                if pd.read_csv(path) is not None:
                    signals_file = path
                    break
            except:
                continue
        
        if signals_file is None:
            print("未找到申赎信号数据文件")
            return {}, pd.DataFrame()
        
        print(f"使用申赎信号文件: {signals_file}")
        
        # 读取申赎信号数据
        signals_df = pd.read_csv(signals_file)
        signals_df['日期'] = pd.to_datetime(signals_df['日期'])
        
        # 分析时间范围
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()
        
        # 筛选时间范围内的signal_1_buy_pe_filtered信号
        period_signals = signals_df[
            (signals_df['日期'].dt.date >= start_date) &
            (signals_df['日期'].dt.date <= end_date) &
            (signals_df['PE筛选后信号1_买入'] == 1)
        ].copy()
        
        print(f"申赎信号总数: {len(period_signals)}")
        
        # 标准化ETF代码格式
        period_signals['ETF代码_数字'] = period_signals['ETF代码'].str.split('.').str[0].astype(int)
        period_signals = period_signals.sort_values(['ETF代码_数字', '日期'])
        
        print(f"涉及ETF数量: {period_signals['ETF代码_数字'].nunique()}")
        
        # 按ETF整理信号日期
        etf_signal_dates = {}
        for etf_code, group in period_signals.groupby('ETF代码_数字'):
            signal_dates = sorted(group['日期'].dt.date.tolist())
            etf_signal_dates[etf_code] = {
                'signal_count': len(signal_dates),
                'signal_dates': signal_dates,
                'date_strings': [date.strftime('%Y-%m-%d') for date in signal_dates]
            }
        
        return etf_signal_dates, period_signals
        
    except Exception as e:
        print(f"提取申赎信号日期时出错: {e}")
        return {}, pd.DataFrame()

def generate_detailed_report(turnover_signals, redemption_signals, turnover_df, redemption_df):
    """生成详细的信号日期报告"""
    
    # 读取Excel文件获取交易次数
    try:
        turnover_excel = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        redemption_excel = pd.read_excel('超额收益_申赎信号/ETF申赎信号择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
    except:
        print("无法读取Excel文件，使用默认数据")
        turnover_excel = pd.DataFrame()
        redemption_excel = pd.DataFrame()
    
    # 生成报告
    report = []
    report.append("# ETF信号详细日期报告")
    report.append(f"\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"分析时间范围: 2024-04-30 到 2025-04-30")
    
    # 90分位成交额信号部分
    report.append("\n## 一、90分位成交额信号详细日期")
    report.append(f"\n### 概览统计")
    report.append(f"- 总信号数: {sum(data['signal_count'] for data in turnover_signals.values())}")
    report.append(f"- 涉及ETF数量: {len(turnover_signals)}")
    
    if not turnover_excel.empty:
        report.append(f"- 实际交易次数: {turnover_excel['交易次数'].sum()}")
        report.append(f"- 平均胜率: {turnover_excel['胜率(%)'].mean():.2f}%")
    
    report.append(f"\n### 各ETF信号详情")
    
    for etf_code in sorted(turnover_signals.keys()):
        data = turnover_signals[etf_code]
        
        # 获取交易次数
        trades = 0
        if not turnover_excel.empty:
            etf_row = turnover_excel[turnover_excel['ETF代码'] == etf_code]
            if not etf_row.empty:
                trades = etf_row.iloc[0]['交易次数']
        
        report.append(f"\n**ETF {etf_code}**:")
        report.append(f"- 信号总数: {data['signal_count']}个")
        report.append(f"- 实际交易次数: {trades}次")
        report.append(f"- 信号日期:")
        
        # 按月分组显示日期
        dates_by_month = {}
        for date_str in data['date_strings']:
            month = date_str[:7]  # YYYY-MM
            if month not in dates_by_month:
                dates_by_month[month] = []
            dates_by_month[month].append(date_str[8:])  # DD
        
        for month in sorted(dates_by_month.keys()):
            days = ', '.join(dates_by_month[month])
            report.append(f"  - {month}: {days}")
    
    # 申赎信号部分
    report.append("\n## 二、申赎信号详细日期")
    report.append(f"\n### 概览统计")
    report.append(f"- 总信号数: {sum(data['signal_count'] for data in redemption_signals.values())}")
    report.append(f"- 涉及ETF数量: {len(redemption_signals)}")
    
    if not redemption_excel.empty:
        report.append(f"- 实际交易次数: {redemption_excel['交易次数'].sum()}")
        report.append(f"- 平均胜率: {redemption_excel['胜率(%)'].mean():.2f}%")
    
    report.append(f"\n### 各ETF信号详情")
    
    for etf_code in sorted(redemption_signals.keys()):
        data = redemption_signals[etf_code]
        
        # 获取交易次数
        trades = 0
        if not redemption_excel.empty:
            etf_row = redemption_excel[redemption_excel['ETF代码'] == etf_code]
            if not etf_row.empty:
                trades = etf_row.iloc[0]['交易次数']
        
        report.append(f"\n**ETF {etf_code}**:")
        report.append(f"- 信号总数: {data['signal_count']}个")
        report.append(f"- 实际交易次数: {trades}次")
        report.append(f"- 信号日期:")
        
        # 按月分组显示日期
        dates_by_month = {}
        for date_str in data['date_strings']:
            month = date_str[:7]  # YYYY-MM
            if month not in dates_by_month:
                dates_by_month[month] = []
            dates_by_month[month].append(date_str[8:])  # DD
        
        for month in sorted(dates_by_month.keys()):
            days = ', '.join(dates_by_month[month])
            report.append(f"  - {month}: {days}")
    
    # 保存报告
    report_content = '\n'.join(report)
    with open('ETF信号详细日期报告.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n详细报告已保存: ETF信号详细日期报告.md")
    
    return report_content

def main():
    """主函数"""
    print("=== ETF信号详细日期提取工具 ===")
    
    # 提取90分位成交额信号日期
    turnover_signals, turnover_df = extract_90_percentile_signal_dates()
    
    # 提取申赎信号日期
    redemption_signals, redemption_df = extract_redemption_signal_dates()
    
    # 生成详细报告
    if turnover_signals or redemption_signals:
        generate_detailed_report(turnover_signals, redemption_signals, turnover_df, redemption_df)
        
        print(f"\n=== 提取结果概览 ===")
        print(f"90分位成交额信号:")
        print(f"  - 涉及ETF数量: {len(turnover_signals)}")
        print(f"  - 总信号数: {sum(data['signal_count'] for data in turnover_signals.values())}")
        
        print(f"申赎信号:")
        print(f"  - 涉及ETF数量: {len(redemption_signals)}")
        print(f"  - 总信号数: {sum(data['signal_count'] for data in redemption_signals.values())}")
    else:
        print("未能提取到有效的信号数据")

if __name__ == "__main__":
    main()
