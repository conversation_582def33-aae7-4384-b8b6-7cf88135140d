#!/usr/bin/env python3
"""
详细显示ETF信号日期信息
"""

import pandas as pd
import warnings
warnings.filterwarnings('ignore')

def show_detailed_signal_dates():
    """显示详细的信号日期信息"""
    
    print("=" * 80)
    print("ETF信号详细日期报告")
    print("=" * 80)
    print(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"分析时间范围: 2024-04-30 到 2025-04-30")
    
    # 1. 90分位成交额信号
    print("\n" + "=" * 50)
    print("一、90分位成交额信号详细日期")
    print("=" * 50)
    
    try:
        # 读取90分位信号数据
        signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
        signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
        signals_df['date'] = signals_df['datetime'].dt.date
        
        # 筛选条件
        filtered_signals = signals_df[
            (signals_df['pe_filter_passed'] == True) &
            (signals_df['threshold'] == 90) &
            (signals_df['holding_period'] == 'next_15day_close')
        ].copy()
        
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()
        
        period_signals = filtered_signals[
            (filtered_signals['date'] >= start_date) &
            (filtered_signals['date'] <= end_date)
        ].copy()
        
        # 每日每个ETF只保留一个信号
        daily_signals = period_signals.groupby(['etf_code', 'date']).first().reset_index()
        
        # 读取交易次数
        try:
            excel_df = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
            trades_dict = dict(zip(excel_df['ETF代码'], excel_df['交易次数']))
        except:
            trades_dict = {}
        
        print(f"总信号数: {len(daily_signals)}")
        print(f"涉及ETF数量: {daily_signals['etf_code'].nunique()}")
        
        # 按ETF显示详细信息
        for etf_code in sorted(daily_signals['etf_code'].unique()):
            etf_signals = daily_signals[daily_signals['etf_code'] == etf_code].sort_values('date')
            signal_dates = etf_signals['date'].tolist()
            trades = trades_dict.get(etf_code, 0)
            
            print(f"\nETF {etf_code}:")
            print(f"  信号总数: {len(signal_dates)}个")
            print(f"  实际交易次数: {trades}次")
            print(f"  信号日期:")
            
            # 按月分组显示
            dates_by_month = {}
            for date in signal_dates:
                month = date.strftime('%Y-%m')
                day = date.strftime('%d')
                if month not in dates_by_month:
                    dates_by_month[month] = []
                dates_by_month[month].append(day)
            
            for month in sorted(dates_by_month.keys()):
                days = ', '.join(dates_by_month[month])
                print(f"    {month}: {days}")
        
    except Exception as e:
        print(f"处理90分位信号时出错: {e}")
    
    # 2. 申赎信号
    print("\n" + "=" * 50)
    print("二、申赎信号详细日期")
    print("=" * 50)
    
    try:
        # 读取申赎信号数据
        signals_df = pd.read_csv('分析结果_PE筛选_20240430_20250430/PE筛选后信号数据汇总表.csv')
        signals_df['日期'] = pd.to_datetime(signals_df['日期'])
        
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()
        
        # 筛选signal_1_buy_pe_filtered信号
        period_signals = signals_df[
            (signals_df['日期'].dt.date >= start_date) &
            (signals_df['日期'].dt.date <= end_date) &
            (signals_df['PE筛选后信号1_买入'] == 1)
        ].copy()
        
        period_signals['ETF代码_数字'] = period_signals['ETF代码'].str.split('.').str[0].astype(int)
        
        # 读取交易次数
        try:
            excel_df = pd.read_excel('超额收益_申赎信号/ETF申赎信号择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
            trades_dict = dict(zip(excel_df['ETF代码'], excel_df['交易次数']))
        except:
            trades_dict = {}
        
        print(f"总信号数: {len(period_signals)}")
        print(f"涉及ETF数量: {period_signals['ETF代码_数字'].nunique()}")
        
        # 按ETF显示详细信息
        for etf_code in sorted(period_signals['ETF代码_数字'].unique()):
            etf_signals = period_signals[period_signals['ETF代码_数字'] == etf_code].sort_values('日期')
            signal_dates = etf_signals['日期'].dt.date.tolist()
            trades = trades_dict.get(etf_code, 0)
            
            print(f"\nETF {etf_code}:")
            print(f"  信号总数: {len(signal_dates)}个")
            print(f"  实际交易次数: {trades}次")
            print(f"  信号日期:")
            
            # 按月分组显示
            dates_by_month = {}
            for date in signal_dates:
                month = date.strftime('%Y-%m')
                day = date.strftime('%d')
                if month not in dates_by_month:
                    dates_by_month[month] = []
                dates_by_month[month].append(day)
            
            for month in sorted(dates_by_month.keys()):
                days = ', '.join(dates_by_month[month])
                print(f"    {month}: {days}")
        
    except Exception as e:
        print(f"处理申赎信号时出错: {e}")
    
    print("\n" + "=" * 80)
    print("报告完成")
    print("=" * 80)

if __name__ == "__main__":
    show_detailed_signal_dates()
