#!/usr/bin/env python3
"""
将90分位成交额信号数据重新整理成新的表格格式
输出格式：
- A列：日期（2024-4-30至2025-4-30的全部交易日）
- B列开始：各ETF在对应交易日的信号数量
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def standardize_etf_code(code):
    """标准化ETF代码，去除后缀"""
    if pd.isna(code):
        return None
    
    code_str = str(code)
    # 去除常见的后缀
    suffixes = ['.SH', '.SZ', '.XSHE', '.XSHG']
    for suffix in suffixes:
        if code_str.endswith(suffix):
            code_str = code_str[:-len(suffix)]
    
    # 确保是6位数字格式
    try:
        return int(code_str)
    except:
        return None

def generate_trading_days(start_date, end_date):
    """生成交易日列表（排除周末）"""
    print(f"=== 生成交易日列表 ===")
    
    start = pd.to_datetime(start_date)
    end = pd.to_datetime(end_date)
    
    # 生成日期范围
    date_range = pd.date_range(start=start, end=end, freq='D')
    
    # 排除周末（周六=5，周日=6）
    trading_days = [date for date in date_range if date.weekday() < 5]
    
    print(f"日期范围: {start_date} 到 {end_date}")
    print(f"总交易日数量: {len(trading_days)}")
    print(f"起始交易日: {trading_days[0].strftime('%Y-%m-%d')}")
    print(f"结束交易日: {trading_days[-1].strftime('%Y-%m-%d')}")
    
    return [date.date() for date in trading_days]

def load_and_process_signals():
    """加载并处理90分位成交额信号数据"""
    print(f"\n=== 加载90分位成交额信号数据 ===")
    
    try:
        # 读取信号数据
        signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
        
        print(f"原始数据行数: {len(signals_df)}")
        print(f"列名: {signals_df.columns.tolist()}")
        
        # 转换时间格式
        signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
        signals_df['date'] = signals_df['datetime'].dt.date
        
        # 筛选条件：pe_filter_passed=True且threshold=90
        filtered_signals = signals_df[
            (signals_df['pe_filter_passed'] == True) &
            (signals_df['threshold'] == 90)
        ].copy()
        
        print(f"PE筛选且90分位信号数: {len(filtered_signals)}")
        
        # 标准化ETF代码
        filtered_signals['etf_code_std'] = filtered_signals['etf_code'].apply(standardize_etf_code)
        filtered_signals = filtered_signals.dropna(subset=['etf_code_std'])
        
        print(f"标准化后信号数: {len(filtered_signals)}")
        
        # 统计各ETF的信号数量
        etf_counts = filtered_signals['etf_code_std'].value_counts().sort_index()
        print(f"涉及ETF数量: {len(etf_counts)}")
        print(f"ETF列表: {sorted(etf_counts.index.tolist())}")
        
        return filtered_signals
        
    except Exception as e:
        print(f"加载信号数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def get_etf_list_from_mapping():
    """从ETF跟踪指数文件获取22只ETF列表"""
    print(f"\n=== 获取ETF列表 ===")
    
    try:
        etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
        etf_list = sorted(etf_mapping['ETF代码'].tolist())
        
        print(f"ETF映射文件中的ETF数量: {len(etf_list)}")
        print(f"ETF列表: {etf_list}")
        
        return etf_list
        
    except Exception as e:
        print(f"获取ETF列表时出错: {e}")
        # 如果无法读取映射文件，使用信号数据中的ETF
        return None

def create_signal_matrix(trading_days, etf_list, signals_df):
    """创建信号矩阵"""
    print(f"\n=== 创建信号矩阵 ===")
    
    # 按日期和ETF分组，计算每日每个ETF的信号数量
    daily_signals = signals_df.groupby(['date', 'etf_code_std']).size().reset_index()
    daily_signals.columns = ['date', 'etf_code', 'signal_count']
    
    print(f"每日信号统计行数: {len(daily_signals)}")
    
    # 创建基础矩阵
    result_data = []
    
    for date in trading_days:
        row_data = {'日期': date}
        
        # 获取该日期的所有信号
        date_signals = daily_signals[daily_signals['date'] == date]
        
        # 为每个ETF填充信号数量
        for etf_code in etf_list:
            etf_signals = date_signals[date_signals['etf_code'] == etf_code]
            
            if not etf_signals.empty:
                # 如果有信号，记录信号数量
                signal_count = etf_signals['signal_count'].sum()
                row_data[str(etf_code)] = signal_count
            else:
                # 如果没有信号，保持为空（NaN）
                row_data[str(etf_code)] = np.nan
        
        result_data.append(row_data)
    
    # 转换为DataFrame
    result_df = pd.DataFrame(result_data)
    
    # 将日期列转换为字符串格式
    result_df['日期'] = result_df['日期'].apply(lambda x: x.strftime('%Y-%m-%d'))
    
    print(f"结果矩阵形状: {result_df.shape}")
    print(f"列名: {result_df.columns.tolist()}")
    
    # 统计信号分布
    total_signals = 0
    signal_days = 0
    
    for col in result_df.columns[1:]:  # 跳过日期列
        col_signals = result_df[col].dropna()
        if not col_signals.empty:
            total_signals += col_signals.sum()
            signal_days += len(col_signals)
    
    print(f"总信号数量: {total_signals}")
    print(f"有信号的ETF-日期组合: {signal_days}")
    
    return result_df

def save_results(result_df):
    """保存结果"""
    print(f"\n=== 保存结果 ===")
    
    # 保存为Excel文件
    excel_path = '90分位成交额信号整理表_2024-2025.xlsx'
    
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        result_df.to_excel(writer, sheet_name='90分位信号明细', index=False)
        
        # 获取工作表和工作簿对象
        workbook = writer.book
        worksheet = writer.sheets['90分位信号明细']
        
        # 设置列宽
        worksheet.set_column('A:A', 12)  # 日期列
        for i in range(1, len(result_df.columns)):
            worksheet.set_column(i, i, 8)  # ETF列
        
        # 设置数字格式（整数）
        number_format = workbook.add_format({'num_format': '0'})
        for i in range(1, len(result_df.columns)):
            worksheet.set_column(i, i, 8, number_format)
        
        # 添加统计信息sheet
        stats_data = []
        
        # 计算各ETF的统计信息
        for col in result_df.columns[1:]:
            etf_code = col
            etf_signals = result_df[col].dropna()
            
            if not etf_signals.empty:
                total_signals = etf_signals.sum()
                signal_days = len(etf_signals)
                avg_signals_per_day = etf_signals.mean()
                max_signals_per_day = etf_signals.max()
                min_signals_per_day = etf_signals.min()
            else:
                total_signals = 0
                signal_days = 0
                avg_signals_per_day = 0
                max_signals_per_day = 0
                min_signals_per_day = 0
            
            stats_data.append({
                'ETF代码': etf_code,
                '总信号数': total_signals,
                '有信号天数': signal_days,
                '平均每日信号数': round(avg_signals_per_day, 2),
                '最大单日信号数': max_signals_per_day,
                '最小单日信号数': min_signals_per_day
            })
        
        stats_df = pd.DataFrame(stats_data)
        stats_df = stats_df.sort_values('总信号数', ascending=False)
        stats_df.to_excel(writer, sheet_name='ETF信号统计', index=False)
        
        # 添加日期统计sheet
        date_stats = []
        for _, row in result_df.iterrows():
            date = row['日期']
            daily_total = 0
            active_etfs = 0
            
            for col in result_df.columns[1:]:
                if not pd.isna(row[col]):
                    daily_total += row[col]
                    active_etfs += 1
            
            if daily_total > 0:
                date_stats.append({
                    '日期': date,
                    '总信号数': daily_total,
                    '活跃ETF数': active_etfs,
                    '平均每ETF信号数': round(daily_total / active_etfs, 2) if active_etfs > 0 else 0
                })
        
        if date_stats:
            date_stats_df = pd.DataFrame(date_stats)
            date_stats_df = date_stats_df.sort_values('总信号数', ascending=False)
            date_stats_df.to_excel(writer, sheet_name='日期信号统计', index=False)
    
    print(f"Excel文件已保存: {excel_path}")
    
    # 生成简要报告
    report_content = f"""# 90分位成交额信号整理报告

## 数据概述

**处理时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**数据源**: 回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv  
**筛选条件**: pe_filter_passed=True 且 threshold=90

## 输出表格结构

### 主表：90分位信号明细
- **A列**: 日期（2024-04-30 到 2025-04-30的交易日）
- **B列开始**: 各ETF代码列，数值为当日信号数量
- **空值处理**: 无信号的日期显示为空

### 统计表格
1. **ETF信号统计**: 各ETF的信号汇总统计
2. **日期信号统计**: 各交易日的信号分布统计

## 数据统计

- **交易日总数**: {len(result_df)} 天
- **ETF数量**: {len(result_df.columns) - 1} 个
- **总信号数**: {sum([result_df[col].dropna().sum() for col in result_df.columns[1:]])} 个
- **有信号的交易日**: {len([row for _, row in result_df.iterrows() if any(not pd.isna(row[col]) for col in result_df.columns[1:])])} 天

## 主要ETF信号分布

"""
    
    # 添加前10个ETF的统计
    stats_df = pd.DataFrame(stats_data).sort_values('总信号数', ascending=False)
    
    for i, (_, row) in enumerate(stats_df.head(10).iterrows(), 1):
        report_content += f"{i}. **ETF {row['ETF代码']}**: {row['总信号数']}个信号, {row['有信号天数']}天, 平均{row['平均每日信号数']:.2f}个/天\n"
    
    report_content += f"""

## 使用说明

1. **数据格式**: Excel文件，包含3个工作表
2. **主要用途**: 时间序列分析、信号分布研究、相关性分析
3. **注意事项**: 空值表示当日无信号，非零值表示信号数量

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存报告
    report_path = '90分位成交额信号整理报告.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"报告已保存: {report_path}")
    
    return excel_path, report_path

def main():
    """主函数"""
    print("=== 90分位成交额信号数据整理 ===")
    print(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 生成交易日列表
    trading_days = generate_trading_days('2024-04-30', '2025-04-30')
    
    # 加载信号数据
    signals_df = load_and_process_signals()
    
    if signals_df.empty:
        print("信号数据为空，无法继续处理")
        return
    
    # 获取ETF列表
    etf_list = get_etf_list_from_mapping()
    
    if etf_list is None:
        # 如果无法从映射文件获取，使用信号数据中的ETF
        etf_list = sorted(signals_df['etf_code_std'].unique())
        print(f"使用信号数据中的ETF列表: {etf_list}")
    
    # 创建信号矩阵
    result_df = create_signal_matrix(trading_days, etf_list, signals_df)
    
    # 保存结果
    excel_path, report_path = save_results(result_df)
    
    # 显示完成信息
    print(f"\n=== 处理完成 ===")
    print(f"输出文件: {excel_path}")
    print(f"报告文件: {report_path}")
    print(f"数据维度: {result_df.shape[0]} 个交易日 × {result_df.shape[1]-1} 个ETF")
    
    # 显示数据预览
    print(f"\n=== 数据预览 ===")
    print("前5行数据:")
    print(result_df.head().to_string(index=False))
    
    print(f"\n后5行数据:")
    print(result_df.tail().to_string(index=False))

if __name__ == "__main__":
    main()
