#!/usr/bin/env python3
"""
修复Excel文件中缺失的588050和588080 ETF数据
"""

import pandas as pd
import warnings
warnings.filterwarnings('ignore')

def main():
    print("=== 修复缺失的ETF数据 ===")
    
    # 读取ETF映射关系
    etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
    etf_mapping['跟踪指数代码'] = etf_mapping['跟踪指数代码'].astype(str).str.zfill(6)
    print(f"ETF映射关系: {len(etf_mapping)} 条")
    
    # 读取指数行情数据
    index_data = pd.read_excel('指数行情序列.xlsx')
    index_data['时间'] = pd.to_datetime(index_data['时间'])
    print(f"指数行情数据: {len(index_data)} 条")
    
    # 读取现有的Excel文件
    excel_path = '超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx'
    
    try:
        existing_df = pd.read_excel(excel_path, sheet_name='超额收益汇总')
        print(f"现有Excel文件包含 {len(existing_df)} 个ETF")
        print("现有ETF代码:", sorted(existing_df['ETF代码'].tolist()))
    except Exception as e:
        print(f"读取现有Excel文件失败: {e}")
        existing_df = pd.DataFrame()
    
    # 检查缺失的ETF
    missing_etfs = ['588050', '588080']
    existing_etfs = existing_df['ETF代码'].tolist() if not existing_df.empty else []
    
    print(f"\n检查缺失的ETF:")
    for etf in missing_etfs:
        if etf in existing_etfs:
            print(f"  {etf}: 已存在")
        else:
            print(f"  {etf}: 缺失，需要添加")
    
    # 分析时间范围
    start_date = pd.to_datetime('2024-04-30').date()
    end_date = pd.to_datetime('2025-04-30').date()
    
    # 为缺失的ETF创建记录
    new_records = []
    for etf_code in missing_etfs:
        if etf_code not in existing_etfs:
            # 获取ETF信息
            etf_info = etf_mapping[etf_mapping['ETF代码'] == etf_code]
            if etf_info.empty:
                print(f"警告: ETF {etf_code} 在映射关系中未找到")
                continue
            
            etf_info = etf_info.iloc[0]
            index_code = etf_info['跟踪指数代码']
            
            # 计算基准收益
            benchmark_return = calculate_index_benchmark_return(
                index_code, index_data, start_date, end_date
            )
            
            # 创建记录
            record = {
                'ETF代码': etf_code,
                '跟踪指数': index_code,
                '交易次数': 0,
                '成功交易': 0,
                '胜率(%)': 0.0,
                '择时累计收益率(%)': 0.0,
                '指数基准收益率(%)': round(benchmark_return * 100, 4),
                '超额收益率(%)': round(-benchmark_return * 100, 4)  # 没有择时收益，超额收益为负的基准收益
            }
            
            new_records.append(record)
            print(f"为 {etf_code} 创建记录: 基准收益率 {benchmark_return*100:.4f}%")
    
    # 更新Excel文件
    if new_records:
        # 合并现有数据和新记录
        new_df = pd.DataFrame(new_records)
        if not existing_df.empty:
            updated_df = pd.concat([existing_df, new_df], ignore_index=True)
        else:
            updated_df = new_df
        
        # 按ETF代码排序
        updated_df = updated_df.sort_values('ETF代码').reset_index(drop=True)
        
        # 重新计算统计摘要
        total_trades = updated_df['交易次数'].sum()
        total_successful = updated_df['成功交易'].sum()
        avg_win_rate = total_successful / total_trades * 100 if total_trades > 0 else 0
        
        # 加权平均收益率（只考虑有交易的ETF）
        trading_df = updated_df[updated_df['交易次数'] > 0]
        if not trading_df.empty:
            weights = trading_df['交易次数'] / trading_df['交易次数'].sum()
            weighted_timing_return = (trading_df['择时累计收益率(%)'] * weights).sum()
            weighted_benchmark_return = (trading_df['指数基准收益率(%)'] * weights).sum()
            weighted_excess_return = weighted_timing_return - weighted_benchmark_return
        else:
            weighted_timing_return = 0
            weighted_benchmark_return = 0
            weighted_excess_return = 0
        
        # 保存更新的Excel文件
        with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
            updated_df.to_excel(writer, sheet_name='超额收益汇总', index=False)
            
            # 添加统计摘要
            stats_data = [
                ['分析时间范围', f'{start_date} 到 {end_date}'],
                ['参与ETF数量', len(updated_df)],
                ['总交易次数', total_trades],
                ['总成功交易', total_successful],
                ['平均胜率(%)', round(avg_win_rate, 2)],
                ['加权平均择时收益率(%)', round(weighted_timing_return, 4)],
                ['加权平均基准收益率(%)', round(weighted_benchmark_return, 4)],
                ['加权平均超额收益率(%)', round(weighted_excess_return, 4)],
                ['持有周期', '15个交易日'],
                ['信号来源', '90分位成交额信号且通过PE筛选'],
                ['信号处理', '每日每个ETF最多一个信号']
            ]
            stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
            stats_df.to_excel(writer, sheet_name='统计摘要', index=False)
        
        print(f"\nExcel文件已更新: {excel_path}")
        print(f"现在包含 {len(updated_df)} 个ETF")
        print("更新后的ETF代码:", sorted(updated_df['ETF代码'].tolist()))
        
        # 显示新添加的记录
        print("\n新添加的记录:")
        for record in new_records:
            print(f"  {record['ETF代码']}: 基准收益率 {record['指数基准收益率(%)']}%, 超额收益率 {record['超额收益率(%)']}%")
    else:
        print("\n所有ETF都已存在，无需更新")

def calculate_index_benchmark_return(index_code, index_data, start_date, end_date):
    """计算指数基准收益率"""
    index_prices = index_data[index_data['代码'] == index_code].copy()
    
    # 处理特殊格式问题
    if index_prices.empty and index_code == '746059':
        index_prices = index_data[index_data['代码'] == '746059.MSI'].copy()
    
    if index_prices.empty:
        print(f"警告: 指数 {index_code} 未找到行情数据")
        return 0.0
    
    index_prices = index_prices.sort_values('时间')
    
    # 获取起始日期的开盘价和结束日期的收盘价
    start_data = index_prices[index_prices['时间'].dt.date >= start_date]
    end_data = index_prices[index_prices['时间'].dt.date <= end_date]
    
    if start_data.empty or end_data.empty:
        print(f"警告: 指数 {index_code} 在指定时间范围内无数据")
        return 0.0
    
    start_price = start_data.iloc[0]['开盘价(元)']
    end_price = end_data.iloc[-1]['收盘价(元)']
    
    if pd.isna(start_price) or pd.isna(end_price) or start_price <= 0:
        print(f"警告: 指数 {index_code} 价格数据异常")
        return 0.0
    
    return (end_price - start_price) / start_price

if __name__ == "__main__":
    main()
