#!/usr/bin/env python3
"""
分析"成功交易"和"交易次数"的定义和区别
"""

import pandas as pd

def analyze_success_trades():
    """分析成功交易的定义"""
    
    print("=== 分析90分位成交额信号中的'成功交易'定义 ===")
    
    try:
        # 读取90分位信号超额收益分析文件
        excel_file = '超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx'
        df = pd.read_excel(excel_file, sheet_name='超额收益汇总')
        
        print("文件列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. {col}")
        
        print(f"\n数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        
        # 显示关键列的数据
        key_columns = ['ETF代码', '交易次数', '成功交易', '胜率(%)', '择时累计收益率(%)', '超额收益率(%)']
        available_columns = [col for col in key_columns if col in df.columns]
        
        print(f"\n=== 关键数据对比 ===")
        comparison_df = df[available_columns].copy()
        
        # 计算成功率
        if '交易次数' in df.columns and '成功交易' in df.columns:
            comparison_df['计算成功率(%)'] = (comparison_df['成功交易'] / comparison_df['交易次数'] * 100).round(2)
            
            # 检查胜率和成功率是否一致
            if '胜率(%)' in df.columns:
                comparison_df['胜率差异'] = (comparison_df['胜率(%)'] - comparison_df['计算成功率(%)']).round(2)
        
        print(comparison_df)
        
        # 统计分析
        print(f"\n=== 统计分析 ===")
        if '交易次数' in df.columns and '成功交易' in df.columns:
            total_trades = df['交易次数'].sum()
            total_success = df['成功交易'].sum()
            overall_success_rate = total_success / total_trades * 100 if total_trades > 0 else 0
            
            print(f"总交易次数: {total_trades}")
            print(f"总成功交易: {total_success}")
            print(f"整体成功率: {overall_success_rate:.2f}%")
            
            if '胜率(%)' in df.columns:
                avg_win_rate = df['胜率(%)'].mean()
                print(f"平均胜率: {avg_win_rate:.2f}%")
        
        # 检查是否有不一致的情况
        print(f"\n=== 胜率与成功率一致性检查 ===")
        if '胜率(%)' in df.columns and '交易次数' in df.columns and '成功交易' in df.columns:
            inconsistent_count = 0
            for _, row in df.iterrows():
                if row['交易次数'] > 0:
                    calculated_success_rate = row['成功交易'] / row['交易次数'] * 100
                    win_rate = row['胜率(%)']
                    if abs(calculated_success_rate - win_rate) > 0.01:  # 允许小数点误差
                        print(f"ETF {row['ETF代码']}: 胜率{win_rate}% vs 成功率{calculated_success_rate:.2f}% (差异: {abs(calculated_success_rate - win_rate):.2f}%)")
                        inconsistent_count += 1
            
            if inconsistent_count == 0:
                print("✓ 所有ETF的胜率和成功率都一致")
                print("结论: '成功交易'列 = 盈利的交易次数")
                print("      '胜率(%)' = 成功交易 / 交易次数 * 100")
            else:
                print(f"发现 {inconsistent_count} 个ETF的胜率和成功率不一致")
        
        # 分析申赎信号进行对比
        print(f"\n=== 对比申赎信号数据 ===")
        try:
            redemption_file = '超额收益_申赎信号/ETF申赎信号择时策略超额收益分析.xlsx'
            redemption_df = pd.read_excel(redemption_file, sheet_name='超额收益汇总')
            
            print("申赎信号文件列名:")
            for i, col in enumerate(redemption_df.columns):
                print(f"  {i+1}. {col}")
            
            # 对比关键数据
            if '交易次数' in redemption_df.columns and '成功交易' in redemption_df.columns:
                redemption_comparison = redemption_df[available_columns].copy()
                redemption_comparison['计算成功率(%)'] = (redemption_comparison['成功交易'] / redemption_comparison['交易次数'] * 100).round(2)
                
                if '胜率(%)' in redemption_df.columns:
                    redemption_comparison['胜率差异'] = (redemption_comparison['胜率(%)'] - redemption_comparison['计算成功率(%)']).round(2)
                
                print(f"\n申赎信号前5行数据:")
                print(redemption_comparison.head())
                
                # 统计
                total_trades_r = redemption_df['交易次数'].sum()
                total_success_r = redemption_df['成功交易'].sum()
                overall_success_rate_r = total_success_r / total_trades_r * 100 if total_trades_r > 0 else 0
                
                print(f"\n申赎信号统计:")
                print(f"总交易次数: {total_trades_r}")
                print(f"总成功交易: {total_success_r}")
                print(f"整体成功率: {overall_success_rate_r:.2f}%")
                
        except Exception as e:
            print(f"读取申赎信号文件时出错: {e}")
        
    except Exception as e:
        print(f"分析时出错: {e}")
        import traceback
        traceback.print_exc()

def check_original_signal_data():
    """检查原始信号数据中的收益情况"""
    
    print(f"\n=== 检查原始信号数据中的收益分布 ===")
    
    try:
        # 读取原始信号数据
        signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
        
        # 筛选90分位且通过PE筛选的信号
        filtered_signals = signals_df[
            (signals_df['pe_filter_passed'] == True) &
            (signals_df['threshold'] == 90) &
            (signals_df['holding_period'] == 'next_15day_close')
        ].copy()
        
        print(f"原始90分位PE筛选信号总数: {len(filtered_signals)}")
        
        # 检查收益分布
        if 'return' in filtered_signals.columns:
            positive_returns = (filtered_signals['return'] > 0).sum()
            total_signals = len(filtered_signals)
            success_rate = positive_returns / total_signals * 100 if total_signals > 0 else 0
            
            print(f"正收益信号数: {positive_returns}")
            print(f"总信号数: {total_signals}")
            print(f"原始信号成功率: {success_rate:.2f}%")
            
            # 按ETF分组分析
            print(f"\n各ETF原始信号成功率:")
            etf_success = filtered_signals.groupby('etf_code').agg({
                'return': ['count', lambda x: (x > 0).sum(), 'mean']
            }).round(4)
            etf_success.columns = ['总信号数', '成功信号数', '平均收益率']
            etf_success['成功率(%)'] = (etf_success['成功信号数'] / etf_success['总信号数'] * 100).round(2)
            
            print(etf_success)
        
    except Exception as e:
        print(f"检查原始信号数据时出错: {e}")

if __name__ == "__main__":
    analyze_success_trades()
    check_original_signal_data()
