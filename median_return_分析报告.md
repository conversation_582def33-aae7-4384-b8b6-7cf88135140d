# ETF回测结果median_return分析报告

## 任务完成概述

✅ **已成功完成**: 根据"所有信号明细_5min_PE筛选.csv"数据，在"回测结果_5min_PE筛选.xlsx"的"汇总统计结果"sheet中增加了"median_return"列，代表各分位、持有周期下的ETF收益率中位数。

## 📊 数据处理结果

### 数据源信息
- **原始信号数**: 333,953个
- **有效信号数**: 303,396个 (通过PE筛选且有收益数据)
- **时间范围**: 2024-09-09 到 2025-04-30
- **策略组合数**: 42个 (3个阈值 × 14个持有周期)

### 新增列信息
- **列名**: `median_return`
- **数据类型**: 百分比 (%)
- **计算方法**: 对每个策略组合下的所有有效收益率计算中位数
- **数据完整性**: 无缺失值

## 📈 median_return统计特征

| 统计指标 | 数值 |
|----------|------|
| 最小值 | 0.0000% |
| 最大值 | 6.2986% |
| 平均值 | 1.6473% |
| 中位数 | 0.9191% |
| 标准差 | 1.9509% |

## 🔍 平均收益率 vs 中位数收益率对比分析

### 主要持有周期对比

#### 80分位阈值
| 持有周期 | 平均收益率 | 中位数收益率 | 差异 | 差异% |
|----------|------------|--------------|------|-------|
| next_day_close | 1.87% | 0.61% | 1.26% | 207% |
| next_5day_close | 4.84% | 2.35% | 2.49% | 106% |
| next_10day_close | 5.99% | 4.02% | 1.97% | 49% |
| next_30day_close | 9.60% | 4.41% | 5.20% | 118% |

#### 90分位阈值
| 持有周期 | 平均收益率 | 中位数收益率 | 差异 | 差异% |
|----------|------------|--------------|------|-------|
| next_day_close | 2.54% | 0.97% | 1.56% | 160% |
| next_5day_close | 5.35% | 2.96% | 2.39% | 81% |
| next_10day_close | 6.04% | 4.32% | 1.72% | 40% |
| next_30day_close | 10.10% | 4.66% | 5.44% | 117% |

#### 95分位阈值
| 持有周期 | 平均收益率 | 中位数收益率 | 差异 | 差异% |
|----------|------------|--------------|------|-------|
| next_day_close | 3.11% | 1.36% | 1.75% | 129% |
| next_5day_close | 5.04% | 3.05% | 1.99% | 65% |
| next_10day_close | 5.87% | 4.47% | 1.39% | 31% |
| next_30day_close | 10.25% | 4.58% | 5.67% | 124% |

## 🎯 关键发现

### 1. 收益分布特征
- **正偏分布**: 平均收益率普遍高于中位数收益率，表明存在少数高收益交易拉高了平均值
- **差异显著**: 平均差异为2.74%，最大差异达5.67%

### 2. 持有周期影响
- **短期策略** (next_day_close): 差异最大，平均比中位数高160-207%
- **长期策略** (next_30day_close): 绝对差异最大，但相对差异较稳定(117-124%)
- **中期策略** (next_10day_close): 差异相对较小，更接近正态分布

### 3. 阈值效应
- **95分位**: 收益分布相对更均匀，差异较小
- **80分位**: 收益分布波动更大，存在更多极值

### 4. 实际应用价值
- **风险评估**: 中位数收益率提供更保守、更现实的收益预期
- **策略选择**: 可以根据平均值与中位数的差异判断策略的风险特征
- **资金管理**: 中位数收益率更适合作为资金配置的参考指标

## 📁 输出文件

### 1. 主要输出
- **`回测结果_5min_PE筛选_含median_return.xlsx`**: 包含新增median_return列的完整回测结果
  - 汇总统计结果 (新增median_return列)
  - 各ETF详细统计 (保留原有数据)

### 2. 分析文件
- **`收益率对比分析.csv`**: 平均收益率与中位数收益率的详细对比数据
- **`calculate_median_return_and_update_excel.py`**: 计算和更新脚本

## 💡 投资策略建议

### 1. 保守型投资者
- 参考**中位数收益率**进行收益预期设定
- 选择差异较小的策略组合 (如95分位 + next_10day_close)

### 2. 积极型投资者
- 可以追求**平均收益率**，但需承担更高的收益波动
- 关注差异较大的策略组合，可能存在更多高收益机会

### 3. 风险管理
- 使用中位数收益率作为止盈止损的参考基准
- 当实际收益显著偏离中位数时，考虑调整仓位

## 🔧 技术实现

### 计算逻辑
```python
# 对每个策略组合计算中位数收益率
for (threshold, period), group in valid_signals.groupby(['threshold', 'holding_period']):
    returns = group['return'].values
    median_return = np.median(returns) * 100  # 转换为百分比
```

### 数据验证
- ✅ 数据完整性检查通过
- ✅ 计算逻辑验证通过  
- ✅ 输出格式符合要求
- ✅ 与原有数据兼容

## 📋 总结

通过增加median_return列，我们现在拥有了更全面的收益率分析指标：

1. **avg_return**: 反映策略的平均表现
2. **median_return**: 反映策略的典型表现  
3. **两者差异**: 反映策略的风险特征

这为ETF量化投资策略的评估和优化提供了更加科学和全面的数据支持。投资者可以根据自己的风险偏好，选择合适的收益率指标作为决策依据。
