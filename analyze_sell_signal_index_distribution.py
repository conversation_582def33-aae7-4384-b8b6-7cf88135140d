#!/usr/bin/env python3
"""
分析申赎卖出信号在各指数上的分布情况
数据源：
1. 分析结果_买入95分位_卖出5分位_20240430_20250430/signal_2_sell_pe_filtered_详细结果.csv
2. 市盈率信息/ETF跟踪指数.xlsx
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def standardize_etf_code(code):
    """标准化ETF代码，去除后缀"""
    if pd.isna(code):
        return None
    
    code_str = str(code)
    # 去除常见的后缀
    suffixes = ['.SH', '.SZ', '.XSHE', '.XSHG']
    for suffix in suffixes:
        if code_str.endswith(suffix):
            code_str = code_str[:-len(suffix)]
    
    # 确保是6位数字格式
    try:
        return int(code_str)
    except:
        return None

def load_sell_data():
    """加载卖出信号数据"""
    print("=== 加载卖出信号数据 ===")
    
    # 1. 加载ETF跟踪指数映射关系
    etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
    print(f"ETF映射数据: {len(etf_mapping)} 个ETF")
    
    # 2. 加载申赎卖出信号数据
    signals_df = pd.read_csv('分析结果_买入95分位_卖出5分位_20240430_20250430/signal_2_sell_pe_filtered_详细结果.csv')
    print(f"申赎卖出信号数据: {len(signals_df)} 条")
    
    # 转换时间格式
    signals_df['signal_date'] = pd.to_datetime(signals_df['signal_date'])
    signals_df['date'] = signals_df['signal_date'].dt.date
    
    # 标准化ETF代码
    signals_df['etf_code_std'] = signals_df['etf_code'].apply(standardize_etf_code)
    signals_df = signals_df.dropna(subset=['etf_code_std'])
    
    print(f"处理后卖出信号数据: {len(signals_df)} 条")
    print(f"信号时间范围: {signals_df['date'].min()} 到 {signals_df['date'].max()}")
    
    return etf_mapping, signals_df

def map_sell_signals_to_indices(etf_mapping, signals_df):
    """将ETF卖出信号映射到指数"""
    print(f"\n=== 将ETF卖出信号映射到指数 ===")
    
    # 创建ETF到指数的映射字典
    etf_to_index = {}
    for _, row in etf_mapping.iterrows():
        etf_code = row['ETF代码']
        index_code = row['跟踪指数代码']
        etf_to_index[etf_code] = index_code
    
    # 为信号数据添加指数代码
    signals_df['index_code'] = signals_df['etf_code_std'].map(etf_to_index)
    
    # 过滤掉无法映射到指数的信号
    mapped_signals = signals_df.dropna(subset=['index_code'])
    
    print(f"成功映射到指数的卖出信号: {len(mapped_signals)} 条")
    print(f"无法映射的卖出信号: {len(signals_df) - len(mapped_signals)} 条")
    
    # 显示各指数的ETF数量
    index_etf_counts = etf_mapping.groupby('跟踪指数代码')['ETF代码'].count().sort_values(ascending=False)
    print(f"\n各指数跟踪的ETF数量:")
    for index_code, count in index_etf_counts.items():
        print(f"  指数 {index_code}: {count} 个ETF")
    
    return mapped_signals

def analyze_sell_index_signal_distribution(mapped_signals):
    """分析各指数的卖出信号分布"""
    print(f"\n=== 分析各指数卖出信号分布 ===")
    
    # 按指数和日期汇总信号
    index_daily_signals = mapped_signals.groupby(['index_code', 'date']).agg({
        'etf_code_std': 'count',  # 每日信号数量
        'signal_date': 'first'   # 保留信号日期
    }).reset_index()
    
    index_daily_signals.columns = ['index_code', 'date', 'daily_signal_count', 'signal_date']
    
    # 计算各指数的统计指标
    index_stats = []
    
    for index_code in sorted(mapped_signals['index_code'].unique()):
        index_signals = index_daily_signals[index_daily_signals['index_code'] == index_code]
        
        # 基础统计
        total_signal_days = len(index_signals)  # 有信号的交易日数量
        total_signals = index_signals['daily_signal_count'].sum()  # 总信号数量
        avg_signals_per_day = index_signals['daily_signal_count'].mean()  # 平均每日信号数
        max_signals_per_day = index_signals['daily_signal_count'].max()  # 最大单日信号数
        min_signals_per_day = index_signals['daily_signal_count'].min()  # 最小单日信号数
        
        # 信号分布统计
        signal_counts = index_signals['daily_signal_count'].value_counts().sort_index()
        
        # 时间分布
        start_date = index_signals['date'].min()
        end_date = index_signals['date'].max()
        
        # 月度分布
        index_signals['month'] = pd.to_datetime(index_signals['date']).dt.to_period('M')
        monthly_distribution = index_signals.groupby('month').agg({
            'daily_signal_count': ['count', 'sum', 'mean']
        }).round(2)
        monthly_distribution.columns = ['有信号天数', '总信号数', '平均每日信号数']
        
        index_stats.append({
            'index_code': index_code,
            'total_signal_days': total_signal_days,
            'total_signals': total_signals,
            'avg_signals_per_day': avg_signals_per_day,
            'max_signals_per_day': max_signals_per_day,
            'min_signals_per_day': min_signals_per_day,
            'start_date': start_date,
            'end_date': end_date,
            'signal_counts_distribution': signal_counts.to_dict(),
            'monthly_distribution': monthly_distribution
        })
        
        print(f"\n指数 {index_code}:")
        print(f"  有信号交易日数: {total_signal_days} 天")
        print(f"  总信号数量: {total_signals} 个")
        print(f"  平均每日信号数: {avg_signals_per_day:.2f} 个")
        print(f"  最大单日信号数: {max_signals_per_day} 个")
        print(f"  最小单日信号数: {min_signals_per_day} 个")
        print(f"  信号时间范围: {start_date} 到 {end_date}")
        print(f"  每日信号数分布: {dict(signal_counts)}")
    
    return index_stats, index_daily_signals

def generate_sell_detailed_analysis(index_stats, index_daily_signals, mapped_signals):
    """生成卖出信号详细分析"""
    print(f"\n=== 生成卖出信号详细分析 ===")
    
    # 创建汇总表
    summary_data = []
    for stat in index_stats:
        summary_data.append({
            '指数代码': stat['index_code'],
            '有信号交易日数': stat['total_signal_days'],
            '总信号数量': stat['total_signals'],
            '平均每日信号数': round(stat['avg_signals_per_day'], 2),
            '最大单日信号数': stat['max_signals_per_day'],
            '最小单日信号数': stat['min_signals_per_day'],
            '信号开始日期': stat['start_date'],
            '信号结束日期': stat['end_date'],
            '信号密度等级': '高密度' if stat['avg_signals_per_day'] >= 2 else '中密度' if stat['avg_signals_per_day'] >= 1 else '低密度'
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_df = summary_df.sort_values('总信号数量', ascending=False)
    
    # 创建每日信号分布表
    daily_distribution_data = []
    for stat in index_stats:
        for signal_count, days in stat['signal_counts_distribution'].items():
            daily_distribution_data.append({
                '指数代码': stat['index_code'],
                '每日信号数': signal_count,
                '天数': days,
                '占比(%)': round(days / stat['total_signal_days'] * 100, 2)
            })
    
    daily_distribution_df = pd.DataFrame(daily_distribution_data)
    
    # 创建月度分布表
    monthly_data = []
    for stat in index_stats:
        monthly_dist = stat['monthly_distribution']
        for month, row in monthly_dist.iterrows():
            monthly_data.append({
                '指数代码': stat['index_code'],
                '月份': str(month),
                '有信号天数': row['有信号天数'],
                '总信号数': row['总信号数'],
                '平均每日信号数': row['平均每日信号数']
            })
    
    monthly_df = pd.DataFrame(monthly_data)
    
    # 创建信号明细表（按指数汇总）
    signal_details = mapped_signals.groupby(['index_code', 'date']).agg({
        'etf_code_std': lambda x: list(x),  # ETF列表
        'signal_date': 'first'
    }).reset_index()
    
    signal_details['etf_count'] = signal_details['etf_code_std'].apply(len)
    signal_details['etf_list'] = signal_details['etf_code_std'].apply(lambda x: ','.join(map(str, x)))
    signal_details = signal_details.drop('etf_code_std', axis=1)
    signal_details.columns = ['指数代码', '信号日期', '信号时间', 'ETF数量', 'ETF列表']
    
    return summary_df, daily_distribution_df, monthly_df, signal_details

def save_sell_results(summary_df, daily_distribution_df, monthly_df, signal_details, index_stats):
    """保存卖出信号分析结果"""
    print(f"\n=== 保存卖出信号分析结果 ===")
    
    # 保存Excel文件
    excel_path = '指数信号分布分析结果_申赎卖出5分位.xlsx'
    
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        
        # 指数汇总统计
        summary_df.to_excel(writer, sheet_name='指数汇总统计', index=False)
        
        # 每日信号分布
        daily_distribution_df.to_excel(writer, sheet_name='每日信号分布', index=False)
        
        # 月度信号分布
        monthly_df.to_excel(writer, sheet_name='月度信号分布', index=False)
        
        # 信号明细
        signal_details.to_excel(writer, sheet_name='信号明细', index=False)
        
        # 各指数详细统计
        for stat in index_stats:
            index_code = stat['index_code']
            
            # 创建该指数的详细数据
            index_detail_data = []
            monthly_dist = stat['monthly_distribution']
            
            for month, row in monthly_dist.iterrows():
                index_detail_data.append({
                    '月份': str(month),
                    '有信号天数': row['有信号天数'],
                    '总信号数': row['总信号数'],
                    '平均每日信号数': row['平均每日信号数']
                })
            
            if index_detail_data:
                index_detail_df = pd.DataFrame(index_detail_data)
                sheet_name = f'指数{index_code}详细'
                index_detail_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # 设置指数汇总统计sheet的列宽
        workbook = writer.book
        worksheet = writer.sheets['指数汇总统计']
        
        worksheet.set_column('A:A', 12)  # 指数代码
        worksheet.set_column('B:B', 15)  # 有信号交易日数
        worksheet.set_column('C:C', 12)  # 总信号数量
        worksheet.set_column('D:D', 15)  # 平均每日信号数
        worksheet.set_column('E:E', 15)  # 最大单日信号数
        worksheet.set_column('F:F', 15)  # 最小单日信号数
        worksheet.set_column('G:G', 15)  # 信号开始日期
        worksheet.set_column('H:H', 15)  # 信号结束日期
        worksheet.set_column('I:I', 12)  # 信号密度等级
    
    print(f"Excel文件已保存: {excel_path}")
    
    # 生成Markdown报告
    report_content = f"""# 申赎卖出信号指数分布分析报告

## 报告概述

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**数据源**: 分析结果_买入95分位_卖出5分位_20240430_20250430/signal_2_sell_pe_filtered_详细结果.csv  
**分析范围**: 申赎卖出信号（5分位，PE筛选）在各指数上的分布

## 指数汇总统计

| 指数代码 | 有信号交易日数 | 总信号数量 | 平均每日信号数 | 最大单日信号数 | 最小单日信号数 | 信号密度等级 | 信号时间范围 |
|---------|---------------|-----------|---------------|---------------|---------------|-------------|-------------|
"""
    
    for _, row in summary_df.iterrows():
        report_content += f"| {row['指数代码']} | {row['有信号交易日数']} | {row['总信号数量']} | {row['平均每日信号数']} | {row['最大单日信号数']} | {row['最小单日信号数']} | {row['信号密度等级']} | {row['信号开始日期']} 到 {row['信号结束日期']} |\n"
    
    report_content += f"""

## 详细分析结果

### 卖出信号分布特征

1. **最活跃指数**: {summary_df.iloc[0]['指数代码']} (总信号数: {summary_df.iloc[0]['总信号数量']})
2. **信号密度最高**: {summary_df.loc[summary_df['平均每日信号数'].idxmax(), '指数代码']} (平均每日: {summary_df['平均每日信号数'].max():.2f}个)
3. **覆盖指数数量**: {len(summary_df)} 个
4. **总信号数量**: {summary_df['总信号数量'].sum()} 个

### 各指数卖出信号特征分析

"""
    
    for stat in index_stats:
        index_code = stat['index_code']
        report_content += f"""
#### 指数 {index_code}

**基础统计**:
- 有信号交易日数: {stat['total_signal_days']} 天
- 总信号数量: {stat['total_signals']} 个
- 平均每日信号数: {stat['avg_signals_per_day']:.2f} 个
- 最大单日信号数: {stat['max_signals_per_day']} 个
- 最小单日信号数: {stat['min_signals_per_day']} 个

**每日信号分布**:
"""
        
        for signal_count, days in stat['signal_counts_distribution'].items():
            percentage = days / stat['total_signal_days'] * 100
            report_content += f"- {signal_count}个信号/天: {days}天 ({percentage:.1f}%)\n"
        
        report_content += f"""
**月度分布**:
"""
        monthly_dist = stat['monthly_distribution']
        for month, row in monthly_dist.iterrows():
            report_content += f"- {month}: {row['有信号天数']}天, {row['总信号数']}个信号, 平均{row['平均每日信号数']:.2f}个/天\n"
    
    report_content += f"""

## 分析结论

### 1. 卖出信号分布特征

- **指数覆盖**: 共{len(summary_df)}个指数有申赎卖出信号
- **信号总量**: {summary_df['总信号数量'].sum()}个信号
- **平均密度**: {summary_df['平均每日信号数'].mean():.2f}个信号/天
- **最高密度**: {summary_df['平均每日信号数'].max():.2f}个信号/天

### 2. 指数活跃度排名

"""
    
    for i, (_, row) in enumerate(summary_df.iterrows(), 1):
        report_content += f"{i}. **指数{row['指数代码']}**: {row['总信号数量']}个信号, {row['平均每日信号数']:.2f}个/天\n"
    
    # 信号密度分类
    high_density = summary_df[summary_df['信号密度等级'] == '高密度']
    medium_density = summary_df[summary_df['信号密度等级'] == '中密度']
    low_density = summary_df[summary_df['信号密度等级'] == '低密度']
    
    report_content += f"""

### 3. 投资策略建议

#### 基于卖出信号密度的策略
1. **高密度指数** (≥2个/天): {len(high_density)}个 - 适合频繁风险管理
2. **中密度指数** (1-2个/天): {len(medium_density)}个 - 适合常规风险控制
3. **低密度指数** (<1个/天): {len(low_density)}个 - 适合长期风险监控

#### 风险管理建议
1. **优先关注**: 卖出信号数量多且密度适中的指数
2. **分散监控**: 选择不同密度等级的指数进行组合监控
3. **及时响应**: 结合卖出信号进行风险预警和止损

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存Markdown报告
    report_path = '申赎卖出信号指数分布分析报告.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"Markdown报告已保存: {report_path}")
    
    return excel_path, report_path

def main():
    """主函数"""
    print("=== 申赎卖出信号指数分布分析 ===")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 加载数据
    etf_mapping, signals_df = load_sell_data()
    
    # 将信号映射到指数
    mapped_signals = map_sell_signals_to_indices(etf_mapping, signals_df)
    
    # 分析指数信号分布
    index_stats, index_daily_signals = analyze_sell_index_signal_distribution(mapped_signals)
    
    # 生成详细分析
    summary_df, daily_distribution_df, monthly_df, signal_details = generate_sell_detailed_analysis(
        index_stats, index_daily_signals, mapped_signals
    )
    
    # 保存结果
    excel_path, report_path = save_sell_results(
        summary_df, daily_distribution_df, monthly_df, signal_details, index_stats
    )
    
    # 显示关键统计
    print(f"\n=== 卖出信号分析结果摘要 ===")
    print(f"覆盖指数数量: {len(summary_df)}")
    print(f"总信号数量: {summary_df['总信号数量'].sum()}")
    print(f"平均每个指数信号数: {summary_df['总信号数量'].mean():.1f}")
    print(f"最活跃指数: {summary_df.iloc[0]['指数代码']} ({summary_df.iloc[0]['总信号数量']}个信号)")
    print(f"信号密度最高指数: {summary_df.loc[summary_df['平均每日信号数'].idxmax(), '指数代码']} ({summary_df['平均每日信号数'].max():.2f}个/天)")
    
    print(f"\n结果文件:")
    print(f"Excel文件: {excel_path}")
    print(f"报告文件: {report_path}")

if __name__ == "__main__":
    main()
