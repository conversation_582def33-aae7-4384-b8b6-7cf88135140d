#!/usr/bin/env python3
"""
完整的ETF信号分析，包括：
1. 跟踪相同指数的ETF信号相关性
2. 没有发出信号的ETF
3. 跟踪指数只有1个ETF的情况
"""

import pandas as pd
import numpy as np
from datetime import datetime
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

def load_complete_data():
    """加载完整的数据"""
    print("=== 加载完整数据 ===")
    
    # 1. 加载ETF跟踪指数映射关系
    etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
    print(f"ETF映射数据: {len(etf_mapping)} 个ETF")
    
    # 2. 加载信号数据
    signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
    signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
    signals_df['date'] = signals_df['datetime'].dt.date
    
    # 筛选90分位PE信号
    filtered_signals = signals_df[
        (signals_df['pe_filter_passed'] == True) &
        (signals_df['threshold'] == 90)
    ].copy()
    
    # 每日每个ETF只保留一个信号
    daily_signals = filtered_signals.groupby(['etf_code', 'date']).first().reset_index()
    
    print(f"原始信号数据: {len(signals_df)} 条")
    print(f"90分位PE筛选信号: {len(filtered_signals)} 条")
    print(f"每日合并后信号: {len(daily_signals)} 条")
    
    return etf_mapping, daily_signals

def analyze_complete_etf_coverage(etf_mapping, daily_signals):
    """分析完整的ETF覆盖情况"""
    print(f"\n=== 完整ETF覆盖分析 ===")
    
    # 所有ETF列表
    all_etfs = set(etf_mapping['ETF代码'].tolist())
    signal_etfs = set(daily_signals['etf_code'].tolist())
    
    # 有信号的ETF
    etfs_with_signals = signal_etfs.intersection(all_etfs)
    # 没有信号的ETF
    etfs_without_signals = all_etfs - signal_etfs
    
    print(f"总ETF数量: {len(all_etfs)}")
    print(f"有信号的ETF: {len(etfs_with_signals)} 个")
    print(f"没有信号的ETF: {len(etfs_without_signals)} 个")
    
    # 分析没有信号的ETF
    no_signal_analysis = []
    for etf_code in etfs_without_signals:
        etf_info = etf_mapping[etf_mapping['ETF代码'] == etf_code]
        if not etf_info.empty:
            index_code = etf_info.iloc[0]['跟踪指数代码']
            no_signal_analysis.append({
                'ETF代码': etf_code,
                '跟踪指数代码': index_code,
                '信号数量': 0,
                '状态': '无信号'
            })
    
    # 分析有信号的ETF
    signal_analysis = []
    for etf_code in etfs_with_signals:
        etf_signals = daily_signals[daily_signals['etf_code'] == etf_code]
        etf_info = etf_mapping[etf_mapping['ETF代码'] == etf_code]
        if not etf_info.empty:
            index_code = etf_info.iloc[0]['跟踪指数代码']
            signal_analysis.append({
                'ETF代码': etf_code,
                '跟踪指数代码': index_code,
                '信号数量': len(etf_signals),
                '状态': '有信号'
            })
    
    return signal_analysis, no_signal_analysis

def analyze_index_groups(etf_mapping, signal_analysis, no_signal_analysis):
    """分析指数分组情况"""
    print(f"\n=== 指数分组分析 ===")
    
    # 合并所有ETF分析结果
    all_etf_analysis = signal_analysis + no_signal_analysis
    all_etf_df = pd.DataFrame(all_etf_analysis)
    
    # 按指数分组
    index_groups = all_etf_df.groupby('跟踪指数代码').agg({
        'ETF代码': ['count', lambda x: list(x)],
        '信号数量': 'sum',
        '状态': lambda x: list(x)
    }).reset_index()

    index_groups.columns = ['跟踪指数代码', 'ETF总数', 'ETF列表', '总信号数', '状态列表']
    
    # 分类指数组
    single_etf_indices = []  # 只有1个ETF的指数
    multi_etf_indices = []   # 有多个ETF的指数
    
    for _, row in index_groups.iterrows():
        index_code = row['跟踪指数代码']
        etf_count = row['ETF总数']
        etf_list = row['ETF列表']
        total_signals = row['总信号数']
        status_list = row['状态列表']
        
        # 统计有信号和无信号的ETF数量
        signal_etf_count = status_list.count('有信号')
        no_signal_etf_count = status_list.count('无信号')
        
        index_info = {
            '跟踪指数代码': index_code,
            'ETF总数': etf_count,
            'ETF列表': etf_list,
            '有信号ETF数': signal_etf_count,
            '无信号ETF数': no_signal_etf_count,
            '总信号数': total_signals,
            '平均信号数': total_signals / signal_etf_count if signal_etf_count > 0 else 0
        }
        
        if etf_count == 1:
            single_etf_indices.append(index_info)
        else:
            multi_etf_indices.append(index_info)
    
    print(f"只有1个ETF的指数: {len(single_etf_indices)} 个")
    print(f"有多个ETF的指数: {len(multi_etf_indices)} 个")
    
    return single_etf_indices, multi_etf_indices, all_etf_df

def calculate_correlation_for_multi_etf(multi_etf_indices, daily_signals):
    """计算多ETF指数的相关性"""
    print(f"\n=== 计算多ETF指数相关性 ===")
    
    correlation_results = []
    
    for index_info in multi_etf_indices:
        index_code = index_info['跟踪指数代码']
        etf_list = index_info['ETF列表']
        
        print(f"分析指数 {index_code}, ETF: {etf_list}")
        
        # 获取有信号的ETF
        etf_signals = {}
        for etf_code in etf_list:
            etf_data = daily_signals[daily_signals['etf_code'] == etf_code]
            if not etf_data.empty:
                etf_signals[etf_code] = etf_data
        
        # 计算两两相关性
        valid_etfs = list(etf_signals.keys())
        if len(valid_etfs) >= 2:
            for etf1, etf2 in combinations(valid_etfs, 2):
                # 计算Jaccard相似度
                etf1_dates = set(etf_signals[etf1]['date'])
                etf2_dates = set(etf_signals[etf2]['date'])
                
                overlap_dates = etf1_dates.intersection(etf2_dates)
                union_dates = etf1_dates.union(etf2_dates)
                
                jaccard_similarity = len(overlap_dates) / len(union_dates) if len(union_dates) > 0 else 0
                
                correlation_results.append({
                    '跟踪指数代码': index_code,
                    'ETF1代码': etf1,
                    'ETF2代码': etf2,
                    'ETF1信号数': len(etf1_dates),
                    'ETF2信号数': len(etf2_dates),
                    '重叠信号数': len(overlap_dates),
                    'Jaccard相似度': jaccard_similarity
                })
        else:
            print(f"  指数 {index_code} 有信号的ETF少于2个，无法计算相关性")
    
    return correlation_results

def generate_complete_report(single_etf_indices, multi_etf_indices, all_etf_df, correlation_results, signal_analysis, no_signal_analysis):
    """生成完整的分析报告"""
    
    print(f"\n=== 生成完整分析报告 ===")
    
    # 统计数据
    total_etfs = len(all_etf_df)
    etfs_with_signals = len([x for x in signal_analysis])
    etfs_without_signals = len([x for x in no_signal_analysis])
    total_indices = len(single_etf_indices) + len(multi_etf_indices)
    
    report_content = f"""# ETF信号完整分析报告

## 报告概述

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**分析范围**: 所有ETF的90分位成交额信号（PE筛选）  
**分析目的**: 完整评估ETF信号覆盖情况和相关性

## 整体统计概览

### 基础统计
- **总ETF数量**: {total_etfs} 个
- **有信号ETF数量**: {etfs_with_signals} 个 ({etfs_with_signals/total_etfs*100:.1f}%)
- **无信号ETF数量**: {etfs_without_signals} 个 ({etfs_without_signals/total_etfs*100:.1f}%)
- **总指数数量**: {total_indices} 个
- **单ETF指数数量**: {len(single_etf_indices)} 个
- **多ETF指数数量**: {len(multi_etf_indices)} 个

### 信号分布统计
- **总信号数**: {sum([x['信号数量'] for x in signal_analysis])} 个
- **平均每个有信号ETF的信号数**: {sum([x['信号数量'] for x in signal_analysis])/etfs_with_signals:.1f} 个

## 详细分析结果

### 1. 有信号的ETF分析

#### 按信号数量排序

| ETF代码 | 跟踪指数 | 信号数量 | 信号密度等级 |
|---------|---------|---------|-------------|
"""
    
    # 按信号数量排序
    signal_analysis_sorted = sorted(signal_analysis, key=lambda x: x['信号数量'], reverse=True)
    
    for etf_info in signal_analysis_sorted:
        signal_count = etf_info['信号数量']
        if signal_count >= 80:
            density = "高密度"
        elif signal_count >= 40:
            density = "中密度"
        elif signal_count >= 20:
            density = "低密度"
        else:
            density = "稀疏"
        
        report_content += f"| {etf_info['ETF代码']} | {etf_info['跟踪指数代码']} | {signal_count} | {density} |\n"
    
    report_content += f"""

### 2. 无信号的ETF分析

#### 无信号ETF列表

| ETF代码 | 跟踪指数 | 可能原因 |
|---------|---------|---------|
"""
    
    for etf_info in no_signal_analysis:
        # 判断可能的无信号原因
        index_code = etf_info['跟踪指数代码']
        same_index_etfs = [x for x in signal_analysis if x['跟踪指数代码'] == index_code]
        
        if same_index_etfs:
            reason = "PE筛选未通过或成交额不足"
        else:
            reason = "该指数下所有ETF均无信号"
        
        report_content += f"| {etf_info['ETF代码']} | {index_code} | {reason} |\n"
    
    report_content += f"""

### 3. 单ETF指数分析

#### 只有1个ETF的指数

| 指数代码 | ETF代码 | 信号数量 | 分析说明 |
|---------|---------|---------|---------|
"""
    
    for index_info in single_etf_indices:
        etf_code = index_info['ETF列表'][0]
        signal_count = index_info['总信号数']
        
        if signal_count > 0:
            analysis = "独立信号源，无相关性分析"
        else:
            analysis = "无信号，无法分析"
        
        report_content += f"| {index_info['跟踪指数代码']} | {etf_code} | {signal_count} | {analysis} |\n"
    
    report_content += f"""

### 4. 多ETF指数相关性分析

#### 各指数下ETF配置情况

| 指数代码 | ETF总数 | 有信号ETF数 | 无信号ETF数 | 总信号数 | 可分析相关性 |
|---------|---------|-------------|-------------|---------|-------------|
"""
    
    for index_info in multi_etf_indices:
        can_analyze = "是" if index_info['有信号ETF数'] >= 2 else "否"
        report_content += f"| {index_info['跟踪指数代码']} | {index_info['ETF总数']} | {index_info['有信号ETF数']} | {index_info['无信号ETF数']} | {index_info['总信号数']} | {can_analyze} |\n"
    
    # 添加相关性分析结果
    if correlation_results:
        correlation_df = pd.DataFrame(correlation_results)
        
        report_content += f"""

#### 多ETF指数相关性详细结果

| 指数代码 | ETF1 | ETF2 | ETF1信号数 | ETF2信号数 | 重叠信号数 | Jaccard相似度 | 相关性等级 |
|---------|------|------|-----------|-----------|-----------|-------------|-----------|
"""
        
        for _, row in correlation_df.iterrows():
            jaccard = row['Jaccard相似度']
            if jaccard >= 0.8:
                level = "极强相关"
            elif jaccard >= 0.6:
                level = "强相关"
            elif jaccard >= 0.4:
                level = "中等相关"
            else:
                level = "弱相关"
            
            report_content += f"| {row['跟踪指数代码']} | {row['ETF1代码']} | {row['ETF2代码']} | {row['ETF1信号数']} | {row['ETF2信号数']} | {row['重叠信号数']} | {jaccard:.4f} | {level} |\n"
        
        # 相关性统计
        avg_correlation = correlation_df['Jaccard相似度'].mean()
        high_correlation_count = len(correlation_df[correlation_df['Jaccard相似度'] >= 0.6])
        
        report_content += f"""

#### 相关性统计摘要
- **可分析相关性的ETF对数**: {len(correlation_df)} 对
- **平均Jaccard相似度**: {avg_correlation:.4f}
- **强相关ETF对数**: {high_correlation_count} 对 ({high_correlation_count/len(correlation_df)*100:.1f}%)
"""
    
    report_content += f"""

## 完整性分析结论

### 1. ETF信号覆盖情况
- **信号覆盖率**: {etfs_with_signals/total_etfs*100:.1f}% ({etfs_with_signals}/{total_etfs})
- **信号空白**: {etfs_without_signals} 个ETF无信号，需要关注原因

### 2. 指数分布特征
- **单ETF指数**: {len(single_etf_indices)} 个，无相关性分析需求
- **多ETF指数**: {len(multi_etf_indices)} 个，需要相关性管理

### 3. 相关性管理建议

#### 对于多ETF指数
"""
    
    if correlation_results:
        correlation_df = pd.DataFrame(correlation_results)
        high_corr_indices = correlation_df[correlation_df['Jaccard相似度'] >= 0.6]['跟踪指数代码'].unique()
        
        report_content += f"""
- **高相关指数**: {len(high_corr_indices)} 个，需要避免重复配置
- **建议**: 每个高相关指数只选择1个最优ETF
"""
    
    report_content += f"""

#### 对于单ETF指数
- **独立性**: {len(single_etf_indices)} 个指数各有1个ETF，天然分散
- **建议**: 可以全部纳入投资组合，无相关性风险

#### 对于无信号ETF
- **排除建议**: {etfs_without_signals} 个ETF无信号，暂时排除
- **监控建议**: 定期检查是否因市场环境变化而产生信号

### 4. 投资组合构建建议

#### 最优ETF选择策略
1. **单ETF指数**: 直接选择（如有信号）
2. **多ETF指数**: 选择信号最多或质量最好的1个ETF
3. **无信号指数**: 暂时排除，持续监控

#### 风险分散策略
1. **避免同指数重复**: 不在同一指数下配置多个ETF
2. **关注信号质量**: 优先选择信号密度适中的ETF
3. **定期重评**: 根据信号变化调整ETF选择

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存报告
    report_path = 'ETF信号完整分析报告.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    # 保存Excel结果
    excel_path = 'ETF信号完整分析结果.xlsx'
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        
        # 有信号ETF
        pd.DataFrame(signal_analysis).to_excel(writer, sheet_name='有信号ETF', index=False)
        
        # 无信号ETF
        pd.DataFrame(no_signal_analysis).to_excel(writer, sheet_name='无信号ETF', index=False)
        
        # 单ETF指数
        pd.DataFrame(single_etf_indices).to_excel(writer, sheet_name='单ETF指数', index=False)
        
        # 多ETF指数
        pd.DataFrame(multi_etf_indices).to_excel(writer, sheet_name='多ETF指数', index=False)
        
        # 相关性分析
        if correlation_results:
            pd.DataFrame(correlation_results).to_excel(writer, sheet_name='相关性分析', index=False)
        
        # 完整ETF列表
        all_etf_df.to_excel(writer, sheet_name='完整ETF列表', index=False)
    
    print(f"完整分析报告已保存: {report_path}")
    print(f"完整分析结果已保存: {excel_path}")
    
    return report_path, excel_path

def main():
    """主函数"""
    print("=== ETF信号完整分析 ===")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 加载数据
    etf_mapping, daily_signals = load_complete_data()
    
    # 分析ETF覆盖情况
    signal_analysis, no_signal_analysis = analyze_complete_etf_coverage(etf_mapping, daily_signals)
    
    # 分析指数分组
    single_etf_indices, multi_etf_indices, all_etf_df = analyze_index_groups(etf_mapping, signal_analysis, no_signal_analysis)
    
    # 计算多ETF指数相关性
    correlation_results = calculate_correlation_for_multi_etf(multi_etf_indices, daily_signals)
    
    # 生成完整报告
    report_path, excel_path = generate_complete_report(
        single_etf_indices, multi_etf_indices, all_etf_df, 
        correlation_results, signal_analysis, no_signal_analysis
    )
    
    # 显示关键统计
    print(f"\n=== 完整分析结果摘要 ===")
    print(f"总ETF数量: {len(all_etf_df)}")
    print(f"有信号ETF: {len(signal_analysis)} 个")
    print(f"无信号ETF: {len(no_signal_analysis)} 个")
    print(f"单ETF指数: {len(single_etf_indices)} 个")
    print(f"多ETF指数: {len(multi_etf_indices)} 个")
    if correlation_results:
        correlation_df = pd.DataFrame(correlation_results)
        print(f"可分析相关性ETF对: {len(correlation_df)} 对")
        print(f"平均相关性: {correlation_df['Jaccard相似度'].mean():.4f}")
    
    print(f"\n报告文件: {report_path}")
    print(f"数据文件: {excel_path}")

if __name__ == "__main__":
    main()
