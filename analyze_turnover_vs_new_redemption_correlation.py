#!/usr/bin/env python3
"""
分析90分位成交额信号与新申赎买入信号的相关性
数据源：
1. 回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv (pe_filter_passed=True且threshold=90)
2. 分析结果_PE筛选_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv
"""

import pandas as pd
import numpy as np
from datetime import datetime
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

def standardize_etf_code(code):
    """标准化ETF代码，去除后缀"""
    if pd.isna(code):
        return None
    
    code_str = str(code)
    # 去除常见的后缀
    suffixes = ['.SH', '.SZ', '.XSHE', '.XSHG']
    for suffix in suffixes:
        if code_str.endswith(suffix):
            code_str = code_str[:-len(suffix)]
    
    # 确保是6位数字格式
    try:
        return int(code_str)
    except:
        return None

def load_turnover_signals():
    """加载90分位成交额信号数据"""
    print("=== 加载90分位成交额信号数据 ===")
    
    try:
        # 读取5分钟成交额信号数据
        signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
        
        print(f"原始数据行数: {len(signals_df)}")
        print(f"列名: {signals_df.columns.tolist()}")
        
        # 转换时间格式
        signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
        signals_df['date'] = signals_df['datetime'].dt.date
        
        # 筛选条件：pe_filter_passed=True且threshold=90
        filtered_signals = signals_df[
            (signals_df['pe_filter_passed'] == True) &
            (signals_df['threshold'] == 90)
        ].copy()
        
        print(f"PE筛选且90分位信号数: {len(filtered_signals)}")
        
        # 标准化ETF代码
        filtered_signals['etf_code_std'] = filtered_signals['etf_code'].apply(standardize_etf_code)
        filtered_signals = filtered_signals.dropna(subset=['etf_code_std'])
        
        # 每日每个ETF只保留一个信号
        daily_signals = filtered_signals.groupby(['etf_code_std', 'date']).first().reset_index()
        daily_signals = daily_signals.sort_values(['etf_code_std', 'date'])
        
        print(f"每日合并后信号数: {len(daily_signals)}")
        print(f"涉及ETF数量: {daily_signals['etf_code_std'].nunique()}")
        
        # 显示ETF代码分布
        etf_counts = daily_signals['etf_code_std'].value_counts().sort_index()
        print(f"各ETF信号数量:")
        for etf_code, count in etf_counts.head(10).items():
            print(f"  ETF {etf_code}: {count}个")
        
        return daily_signals[['etf_code_std', 'date', 'datetime']]
        
    except Exception as e:
        print(f"加载90分位成交额信号数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def load_new_redemption_signals():
    """加载新申赎买入信号数据"""
    print(f"\n=== 加载新申赎买入信号数据 ===")
    
    try:
        # 读取新申赎信号数据
        signals_df = pd.read_csv('分析结果_PE筛选_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv')
        
        print(f"原始数据行数: {len(signals_df)}")
        print(f"列名: {signals_df.columns.tolist()}")
        
        # 转换时间格式
        if 'signal_date' in signals_df.columns:
            signals_df['signal_date'] = pd.to_datetime(signals_df['signal_date'])
            signals_df['date'] = signals_df['signal_date'].dt.date
        else:
            print("未找到signal_date列")
            return pd.DataFrame()
        
        # 标准化ETF代码
        if 'etf_code' in signals_df.columns:
            signals_df['etf_code_std'] = signals_df['etf_code'].apply(standardize_etf_code)
        else:
            print("未找到etf_code列")
            return pd.DataFrame()
        
        signals_df = signals_df.dropna(subset=['etf_code_std'])
        
        # 每日每个ETF只保留一个信号
        daily_signals = signals_df.groupby(['etf_code_std', 'date']).first().reset_index()
        daily_signals = daily_signals.sort_values(['etf_code_std', 'date'])
        
        print(f"每日合并后信号数: {len(daily_signals)}")
        print(f"涉及ETF数量: {daily_signals['etf_code_std'].nunique()}")
        
        # 显示ETF代码分布
        etf_counts = daily_signals['etf_code_std'].value_counts().sort_index()
        print(f"各ETF信号数量:")
        for etf_code, count in etf_counts.head(10).items():
            print(f"  ETF {etf_code}: {count}个")
        
        return daily_signals[['etf_code_std', 'date']]
        
    except Exception as e:
        print(f"加载新申赎信号数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def analyze_signal_correlation(turnover_signals, redemption_signals):
    """分析两类信号的相关性"""
    print(f"\n=== 分析信号相关性 ===")
    
    if turnover_signals.empty or redemption_signals.empty:
        print("数据为空，无法进行相关性分析")
        return
    
    # 找到共同的ETF
    turnover_etfs = set(turnover_signals['etf_code_std'].unique())
    redemption_etfs = set(redemption_signals['etf_code_std'].unique())
    common_etfs = turnover_etfs.intersection(redemption_etfs)
    
    print(f"90分位信号ETF数量: {len(turnover_etfs)}")
    print(f"新申赎信号ETF数量: {len(redemption_etfs)}")
    print(f"共同ETF数量: {len(common_etfs)}")
    print(f"共同ETF: {sorted(common_etfs)}")
    
    if not common_etfs:
        print("没有共同的ETF，无法进行相关性分析")
        return
    
    # 分析结果
    correlation_results = []
    detailed_results = []
    
    for etf_code in sorted(common_etfs):
        print(f"\n--- 分析ETF {etf_code} ---")
        
        # 获取该ETF的两类信号
        turnover_dates = set(turnover_signals[turnover_signals['etf_code_std'] == etf_code]['date'])
        redemption_dates = set(redemption_signals[redemption_signals['etf_code_std'] == etf_code]['date'])
        
        print(f"90分位信号日期数: {len(turnover_dates)}")
        print(f"新申赎信号日期数: {len(redemption_dates)}")
        
        # 计算重叠情况
        overlap_dates = turnover_dates.intersection(redemption_dates)
        union_dates = turnover_dates.union(redemption_dates)
        
        print(f"重叠日期数: {len(overlap_dates)}")
        print(f"总信号日期数: {len(union_dates)}")
        
        # 计算相关性指标
        if len(union_dates) > 0:
            jaccard_similarity = len(overlap_dates) / len(union_dates)
            
            # 计算各自的覆盖率
            turnover_coverage = len(overlap_dates) / len(turnover_dates) if len(turnover_dates) > 0 else 0
            redemption_coverage = len(overlap_dates) / len(redemption_dates) if len(redemption_dates) > 0 else 0
            
            print(f"Jaccard相似度: {jaccard_similarity:.4f}")
            print(f"90分位信号覆盖率: {turnover_coverage:.4f}")
            print(f"新申赎信号覆盖率: {redemption_coverage:.4f}")
            
            correlation_results.append({
                'ETF代码': etf_code,
                '90分位信号数': len(turnover_dates),
                '新申赎信号数': len(redemption_dates),
                '重叠信号数': len(overlap_dates),
                'Jaccard相似度': jaccard_similarity,
                '90分位覆盖率': turnover_coverage,
                '新申赎覆盖率': redemption_coverage
            })
            
            # 详细的重叠日期
            if overlap_dates:
                overlap_list = sorted(overlap_dates)
                print(f"重叠日期: {[date.strftime('%Y-%m-%d') for date in overlap_list[:5]]}{'...' if len(overlap_list) > 5 else ''}")
                
                for date in overlap_list:
                    detailed_results.append({
                        'ETF代码': etf_code,
                        '重叠日期': date.strftime('%Y-%m-%d'),
                        '90分位信号': '是',
                        '新申赎信号': '是'
                    })
    
    # 生成汇总统计
    if correlation_results:
        results_df = pd.DataFrame(correlation_results)
        
        print(f"\n=== 整体相关性分析结果 ===")
        print(f"平均Jaccard相似度: {results_df['Jaccard相似度'].mean():.4f}")
        print(f"平均90分位覆盖率: {results_df['90分位覆盖率'].mean():.4f}")
        print(f"平均新申赎覆盖率: {results_df['新申赎覆盖率'].mean():.4f}")
        
        # 相关性强度分类
        high_correlation = results_df[results_df['Jaccard相似度'] >= 0.3]
        medium_correlation = results_df[(results_df['Jaccard相似度'] >= 0.1) & (results_df['Jaccard相似度'] < 0.3)]
        low_correlation = results_df[results_df['Jaccard相似度'] < 0.1]
        
        print(f"\n相关性分类:")
        print(f"强相关(Jaccard≥0.3): {len(high_correlation)}个ETF")
        print(f"中等相关(0.1≤Jaccard<0.3): {len(medium_correlation)}个ETF")
        print(f"弱相关(Jaccard<0.1): {len(low_correlation)}个ETF")
        
        # 保存结果
        save_correlation_results(results_df, detailed_results, turnover_signals, redemption_signals)
        
        return results_df
    
    return pd.DataFrame()

def save_correlation_results(results_df, detailed_results, turnover_signals, redemption_signals):
    """保存相关性分析结果"""
    
    excel_path = '90分位成交额与新申赎信号相关性分析结果.xlsx'
    
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        
        # 相关性汇总结果
        results_df.to_excel(writer, sheet_name='相关性汇总', index=False)
        
        # 详细重叠日期
        if detailed_results:
            detailed_df = pd.DataFrame(detailed_results)
            detailed_df.to_excel(writer, sheet_name='重叠日期明细', index=False)
        
        # 90分位信号明细
        turnover_summary = turnover_signals.groupby('etf_code_std').agg({
            'date': 'count'
        }).reset_index()
        turnover_summary.columns = ['ETF代码', '信号数量']
        turnover_summary.to_excel(writer, sheet_name='90分位信号统计', index=False)
        
        # 新申赎信号明细
        redemption_summary = redemption_signals.groupby('etf_code_std').agg({
            'date': 'count'
        }).reset_index()
        redemption_summary.columns = ['ETF代码', '信号数量']
        redemption_summary.to_excel(writer, sheet_name='新申赎信号统计', index=False)
        
        # 统计摘要
        stats_data = [
            ['分析时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['90分位信号ETF数量', turnover_signals['etf_code_std'].nunique()],
            ['新申赎信号ETF数量', redemption_signals['etf_code_std'].nunique()],
            ['共同ETF数量', len(results_df)],
            ['平均Jaccard相似度', results_df['Jaccard相似度'].mean()],
            ['平均90分位覆盖率', results_df['90分位覆盖率'].mean()],
            ['平均新申赎覆盖率', results_df['新申赎覆盖率'].mean()],
            ['强相关ETF数量(Jaccard≥0.3)', len(results_df[results_df['Jaccard相似度'] >= 0.3])],
            ['中等相关ETF数量(0.1≤Jaccard<0.3)', len(results_df[(results_df['Jaccard相似度'] >= 0.1) & (results_df['Jaccard相似度'] < 0.3)])],
            ['弱相关ETF数量(Jaccard<0.1)', len(results_df[results_df['Jaccard相似度'] < 0.1])]
        ]
        stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
        stats_df.to_excel(writer, sheet_name='统计摘要', index=False)
    
    print(f"\n相关性分析结果已保存: {excel_path}")

def generate_detailed_report(correlation_results, detailed_results):
    """生成详细分析报告"""
    
    if correlation_results.empty:
        print("没有相关性分析结果")
        return
    
    report_content = f"""# 90分位成交额信号与新申赎信号相关性分析报告

## 报告概述

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**分析目的**: 分析90分位成交额信号与新申赎买入信号发出日期的相关性  
**分析方法**: Jaccard相似度系数

## 数据源说明

### 信号1：90分位成交额信号（PE筛选）
- **数据源**: `回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv`
- **筛选条件**: pe_filter_passed=True 且 threshold=90
- **处理方式**: 每日每个ETF合并为1个信号

### 信号2：新申赎买入信号（PE筛选）
- **数据源**: `分析结果_PE筛选_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv`
- **处理方式**: 每日每个ETF合并为1个信号

## 整体分析结果

### 基础统计
- **90分位信号ETF数量**: {correlation_results['90分位信号数'].sum()}个信号
- **新申赎信号ETF数量**: {correlation_results['新申赎信号数'].sum()}个信号
- **共同ETF数量**: {len(correlation_results)}个
- **平均Jaccard相似度**: {correlation_results['Jaccard相似度'].mean():.4f}
- **平均90分位覆盖率**: {correlation_results['90分位覆盖率'].mean():.4f}
- **平均新申赎覆盖率**: {correlation_results['新申赎覆盖率'].mean():.4f}

### 相关性强度分类
- **强相关 (Jaccard≥0.3)**: {len(correlation_results[correlation_results['Jaccard相似度'] >= 0.3])}个ETF ({len(correlation_results[correlation_results['Jaccard相似度'] >= 0.3])/len(correlation_results)*100:.1f}%)
- **中等相关 (0.1≤Jaccard<0.3)**: {len(correlation_results[(correlation_results['Jaccard相似度'] >= 0.1) & (correlation_results['Jaccard相似度'] < 0.3)])}个ETF ({len(correlation_results[(correlation_results['Jaccard相似度'] >= 0.1) & (correlation_results['Jaccard相似度'] < 0.3)])/len(correlation_results)*100:.1f}%)
- **弱相关 (Jaccard<0.1)**: {len(correlation_results[correlation_results['Jaccard相似度'] < 0.1])}个ETF ({len(correlation_results[correlation_results['Jaccard相似度'] < 0.1])/len(correlation_results)*100:.1f}%)

## 详细分析结果

### 各ETF相关性排名

| 排名 | ETF代码 | 90分位信号数 | 新申赎信号数 | 重叠信号数 | Jaccard相似度 | 90分位覆盖率 | 新申赎覆盖率 |
|------|---------|-------------|-------------|-----------|-------------|-------------|-------------|
"""
    
    # 按Jaccard相似度排序
    correlation_sorted = correlation_results.sort_values('Jaccard相似度', ascending=False)
    
    for i, (_, row) in enumerate(correlation_sorted.iterrows(), 1):
        report_content += f"| {i} | {int(row['ETF代码'])} | {row['90分位信号数']} | {row['新申赎信号数']} | {row['重叠信号数']} | {row['Jaccard相似度']:.4f} | {row['90分位覆盖率']:.4f} | {row['新申赎覆盖率']:.4f} |\n"
    
    # 添加重叠日期分析
    if detailed_results:
        overlap_df = pd.DataFrame(detailed_results)
        overlap_date_counts = overlap_df['重叠日期'].value_counts().head(10)
        
        report_content += f"""

### 重叠信号日期分析

#### 重叠信号最多的日期

| 日期 | 重叠ETF数量 |
|------|------------|
"""
        
        for date, count in overlap_date_counts.items():
            report_content += f"| {date} | {count} |\n"
        
        # 添加月度分析
        overlap_df['月份'] = pd.to_datetime(overlap_df['重叠日期']).dt.to_period('M')
        monthly_overlap = overlap_df.groupby('月份').size()
        
        report_content += f"""

#### 月度重叠信号分布

| 月份 | 重叠信号次数 |
|------|-------------|
"""
        
        for month, count in monthly_overlap.items():
            report_content += f"| {month} | {count} |\n"
    
    # 添加相关性分析结论
    high_corr_etfs = correlation_sorted[correlation_sorted['Jaccard相似度'] >= 0.1]
    
    report_content += f"""

## 分析结论

### 1. 整体相关性评估

**结论**: 两类信号的相关性总体{'较强' if correlation_results['Jaccard相似度'].mean() >= 0.3 else '中等' if correlation_results['Jaccard相似度'].mean() >= 0.1 else '较弱'}

- **平均Jaccard相似度**: {correlation_results['Jaccard相似度'].mean():.4f}
- **相关性分布**: {len(correlation_results[correlation_results['Jaccard相似度'] < 0.1])/len(correlation_results)*100:.1f}%弱相关，{len(correlation_results[(correlation_results['Jaccard相似度'] >= 0.1) & (correlation_results['Jaccard相似度'] < 0.3)])/len(correlation_results)*100:.1f}%中等相关，{len(correlation_results[correlation_results['Jaccard相似度'] >= 0.3])/len(correlation_results)*100:.1f}%强相关
- **信号重叠率**: 平均有{correlation_results['90分位覆盖率'].mean()*100:.1f}%的90分位信号与新申赎信号重叠

### 2. 相关性较强的ETF (Jaccard≥0.1)

"""
    
    for _, row in high_corr_etfs.iterrows():
        etf_code = int(row['ETF代码'])
        jaccard = row['Jaccard相似度']
        overlap_count = row['重叠信号数']
        
        report_content += f"""
#### ETF {etf_code}
- **Jaccard相似度**: {jaccard:.4f}
- **重叠信号数**: {overlap_count}个
- **90分位覆盖率**: {row['90分位覆盖率']:.4f}
- **新申赎覆盖率**: {row['新申赎覆盖率']:.4f}
"""
    
    report_content += f"""

### 3. 投资策略建议

#### 基于相关性的策略应用
1. **高相关ETF**: 可以优先使用其中一类信号，避免重复
2. **中等相关ETF**: 两类信号可以互补使用
3. **低相关ETF**: 两类信号独立性强，可以分别使用

#### 信号确认策略
1. **强确认**: 当两类信号同时出现时，作为强确认信号
2. **独立验证**: 利用信号差异进行独立验证
3. **风险控制**: 根据相关性调整仓位和风险管理

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存报告
    report_path = '90分位成交额与新申赎信号相关性分析详细报告.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"详细报告已保存: {report_path}")

def main():
    """主函数"""
    print("=== 90分位成交额信号与新申赎信号相关性分析 ===")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 加载两类信号数据
    turnover_signals = load_turnover_signals()
    redemption_signals = load_new_redemption_signals()
    
    # 分析相关性
    if not turnover_signals.empty and not redemption_signals.empty:
        correlation_results = analyze_signal_correlation(turnover_signals, redemption_signals)
        
        if not correlation_results.empty:
            print(f"\n=== 分析完成 ===")
            print(f"共分析了 {len(correlation_results)} 个ETF的信号相关性")
            
            # 显示最相关的ETF
            top_correlated = correlation_results.nlargest(5, 'Jaccard相似度')
            print(f"\n最相关的5个ETF:")
            for _, row in top_correlated.iterrows():
                print(f"  ETF {int(row['ETF代码'])}: Jaccard相似度 {row['Jaccard相似度']:.4f}")
            
            # 生成详细报告
            detailed_results = []  # 这里需要从analyze_signal_correlation函数返回
            generate_detailed_report(correlation_results, detailed_results)
        else:
            print("未能生成相关性分析结果")
    else:
        print("数据加载失败，无法进行分析")

if __name__ == "__main__":
    main()
