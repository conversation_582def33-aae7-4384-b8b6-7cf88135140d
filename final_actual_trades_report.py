#!/usr/bin/env python3
"""
生成最终的实际交易报告
包含详细的交易日期和收益信息
"""

import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>

def get_actual_signal_dates():
    """获取实际交易对应的信号日期"""
    
    print("=== 获取实际交易对应的信号日期 ===")
    
    # 读取超额收益结果
    turnover_excel = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
    redemption_excel = pd.read_excel('超额收益_申赎信号/ETF申赎信号择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
    
    # 读取原始信号数据
    signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
    signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
    signals_df['date'] = signals_df['datetime'].dt.date
    
    # 筛选90分位信号
    filtered_signals = signals_df[
        (signals_df['pe_filter_passed'] == True) &
        (signals_df['threshold'] == 90) &
        (signals_df['holding_period'] == 'next_15day_close')
    ].copy()
    
    start_date = pd.to_datetime('2024-04-30').date()
    end_date = pd.to_datetime('2025-04-30').date()
    
    period_signals = filtered_signals[
        (filtered_signals['date'] >= start_date) &
        (filtered_signals['date'] <= end_date)
    ].copy()
    
    daily_signals = period_signals.groupby(['etf_code', 'date']).first().reset_index()
    daily_signals = daily_signals.sort_values(['etf_code', 'date'])
    
    # 生成90分位实际交易明细
    turnover_actual_trades = []
    
    for _, excel_row in turnover_excel.iterrows():
        etf_code = int(excel_row['ETF代码'])
        actual_trade_count = int(excel_row['交易次数'])
        
        if actual_trade_count == 0:
            continue
        
        # 获取该ETF的信号
        etf_signals = daily_signals[daily_signals['etf_code'] == etf_code].copy()
        etf_signals = etf_signals.sort_values('date')
        
        if len(etf_signals) == 0:
            continue
        
        # 取前N个信号作为实际交易
        actual_signals = etf_signals.head(actual_trade_count)
        
        for i, (_, signal) in enumerate(actual_signals.iterrows(), 1):
            signal_date = signal['date']
            end_date_calc = signal_date + timedelta(days=21)  # 大约15个交易日
            
            turnover_actual_trades.append({
                'ETF代码': etf_code,
                '跟踪指数': excel_row['跟踪指数'],
                '交易序号': i,
                '信号日期': signal_date.strftime('%Y-%m-%d'),
                '预计结束日期': end_date_calc.strftime('%Y-%m-%d'),
                '开始价格': round(signal['entry_price'], 4) if pd.notna(signal['entry_price']) else 0,
                '结束价格': round(signal['exit_price'], 4) if pd.notna(signal['exit_price']) else 0,
                '单次收益率(%)': round(signal['return'] * 100, 4) if pd.notna(signal['return']) else 0,
                '是否盈利': '是' if signal['return'] > 0 else '否',
                '信号时间': signal['datetime'].strftime('%Y-%m-%d %H:%M:%S'),
                '成交额': round(signal.get('turnover', 0), 2),
                '总交易次数': actual_trade_count,
                '胜率(%)': excel_row['胜率(%)'],
                '累计收益率(%)': excel_row['择时累计收益率(%)'],
                '超额收益率(%)': excel_row['超额收益率(%)']
            })
    
    # 读取申赎信号数据
    redemption_signals_df = pd.read_csv('分析结果_PE筛选_20240430_20250430/PE筛选后信号数据汇总表.csv')
    redemption_signals_df['日期'] = pd.to_datetime(redemption_signals_df['日期'])
    
    period_redemption_signals = redemption_signals_df[
        (redemption_signals_df['日期'].dt.date >= start_date) &
        (redemption_signals_df['日期'].dt.date <= end_date) &
        (redemption_signals_df['PE筛选后信号1_买入'] == 1)
    ].copy()
    
    period_redemption_signals['ETF代码_数字'] = period_redemption_signals['ETF代码'].str.split('.').str[0].astype(int)
    period_redemption_signals = period_redemption_signals.sort_values(['ETF代码_数字', '日期'])
    
    # 生成申赎信号实际交易明细
    redemption_actual_trades = []
    
    for _, excel_row in redemption_excel.iterrows():
        etf_code = int(excel_row['ETF代码'])
        actual_trade_count = int(excel_row['交易次数'])
        
        if actual_trade_count == 0:
            continue
        
        # 获取该ETF的信号
        etf_signals = period_redemption_signals[period_redemption_signals['ETF代码_数字'] == etf_code].copy()
        etf_signals = etf_signals.sort_values('日期')
        
        if len(etf_signals) == 0:
            continue
        
        # 取前N个信号作为实际交易
        actual_signals = etf_signals.head(actual_trade_count)
        
        avg_return_per_trade = excel_row['择时累计收益率(%)'] / actual_trade_count if actual_trade_count > 0 else 0
        
        for i, (_, signal) in enumerate(actual_signals.iterrows(), 1):
            signal_date = signal['日期'].date()
            end_date_calc = signal_date + timedelta(days=21)  # 大约15个交易日
            
            redemption_actual_trades.append({
                'ETF代码': etf_code,
                '跟踪指数': excel_row['跟踪指数'],
                '交易序号': i,
                '信号日期': signal_date.strftime('%Y-%m-%d'),
                '预计结束日期': end_date_calc.strftime('%Y-%m-%d'),
                '平均单次收益率(%)': round(avg_return_per_trade, 4),
                '是否盈利': '是' if avg_return_per_trade > 0 else '否',
                '申赎净额(万股)': round(signal.get('净申购金额', 0) / 10000, 2),
                '申赎净额占比(%)': round(signal.get('净申购金额分位数', 0), 4),
                '市盈率分位数': round(signal.get('市盈率分位数', 0), 4),
                '总交易次数': actual_trade_count,
                '胜率(%)': excel_row['胜率(%)'],
                '累计收益率(%)': excel_row['择时累计收益率(%)'],
                '超额收益率(%)': excel_row['超额收益率(%)']
            })
    
    return pd.DataFrame(turnover_actual_trades), pd.DataFrame(redemption_actual_trades)

def create_final_report():
    """创建最终报告"""
    
    print("=== 生成最终实际交易报告 ===")
    
    # 获取实际交易明细
    turnover_trades, redemption_trades = get_actual_signal_dates()
    
    # 保存到Excel文件
    excel_path = 'ETF实际交易明细表_完整版.xlsx'
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        
        # 90分位成交额信号实际交易明细
        if not turnover_trades.empty:
            turnover_trades.to_excel(writer, sheet_name='90分位信号实际交易明细', index=False)
        
        # 申赎信号实际交易明细
        if not redemption_trades.empty:
            redemption_trades.to_excel(writer, sheet_name='申赎信号实际交易明细', index=False)
        
        # 创建ETF汇总表
        etf_summary = []
        
        # 90分位信号汇总
        if not turnover_trades.empty:
            turnover_summary = turnover_trades.groupby('ETF代码').agg({
                '跟踪指数': 'first',
                '交易序号': 'count',
                '胜率(%)': 'first',
                '累计收益率(%)': 'first',
                '超额收益率(%)': 'first'
            }).reset_index()
            turnover_summary['信号类型'] = '90分位成交额信号'
            turnover_summary.columns = ['ETF代码', '跟踪指数', '实际交易次数', '胜率(%)', '累计收益率(%)', '超额收益率(%)', '信号类型']
            etf_summary.append(turnover_summary)
        
        # 申赎信号汇总
        if not redemption_trades.empty:
            redemption_summary = redemption_trades.groupby('ETF代码').agg({
                '跟踪指数': 'first',
                '交易序号': 'count',
                '胜率(%)': 'first',
                '累计收益率(%)': 'first',
                '超额收益率(%)': 'first'
            }).reset_index()
            redemption_summary['信号类型'] = '申赎信号'
            redemption_summary.columns = ['ETF代码', '跟踪指数', '实际交易次数', '胜率(%)', '累计收益率(%)', '超额收益率(%)', '信号类型']
            etf_summary.append(redemption_summary)
        
        if etf_summary:
            combined_summary = pd.concat(etf_summary, ignore_index=True)
            combined_summary.to_excel(writer, sheet_name='ETF实际交易汇总', index=False)
        
        # 创建统计摘要
        stats_data = [
            ['报告生成时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['分析时间范围', '2024-04-30 到 2025-04-30'],
            ['', ''],
            ['90分位成交额信号', ''],
            ['实际交易总次数', len(turnover_trades) if not turnover_trades.empty else 0],
            ['涉及ETF数量', turnover_trades['ETF代码'].nunique() if not turnover_trades.empty else 0],
            ['平均胜率(%)', turnover_trades.groupby('ETF代码')['胜率(%)'].first().mean() if not turnover_trades.empty else 0],
            ['平均超额收益率(%)', turnover_trades.groupby('ETF代码')['超额收益率(%)'].first().mean() if not turnover_trades.empty else 0],
            ['', ''],
            ['申赎信号', ''],
            ['实际交易总次数', len(redemption_trades) if not redemption_trades.empty else 0],
            ['涉及ETF数量', redemption_trades['ETF代码'].nunique() if not redemption_trades.empty else 0],
            ['平均胜率(%)', redemption_trades.groupby('ETF代码')['胜率(%)'].first().mean() if not redemption_trades.empty else 0],
            ['平均超额收益率(%)', redemption_trades.groupby('ETF代码')['超额收益率(%)'].first().mean() if not redemption_trades.empty else 0]
        ]
        
        stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
        stats_df.to_excel(writer, sheet_name='统计摘要', index=False)
    
    print(f"\n完整版Excel文件已保存: {excel_path}")
    
    # 显示关键统计
    if not turnover_trades.empty:
        print(f"\n90分位成交额信号实际交易:")
        print(f"  总交易次数: {len(turnover_trades)}")
        print(f"  涉及ETF数量: {turnover_trades['ETF代码'].nunique()}")
        print(f"  平均胜率: {turnover_trades.groupby('ETF代码')['胜率(%)'].first().mean():.2f}%")
        print(f"  平均超额收益: {turnover_trades.groupby('ETF代码')['超额收益率(%)'].first().mean():.4f}%")
        
        # 显示各ETF交易次数和收益
        etf_stats = turnover_trades.groupby('ETF代码').agg({
            '交易序号': 'count',
            '胜率(%)': 'first',
            '超额收益率(%)': 'first'
        })
        print(f"  各ETF详情:")
        for etf_code, stats in etf_stats.iterrows():
            print(f"    {etf_code}: {stats['交易序号']}次, 胜率{stats['胜率(%)']}%, 超额收益{stats['超额收益率(%)']}%")
    
    if not redemption_trades.empty:
        print(f"\n申赎信号实际交易:")
        print(f"  总交易次数: {len(redemption_trades)}")
        print(f"  涉及ETF数量: {redemption_trades['ETF代码'].nunique()}")
        print(f"  平均胜率: {redemption_trades.groupby('ETF代码')['胜率(%)'].first().mean():.2f}%")
        print(f"  平均超额收益: {redemption_trades.groupby('ETF代码')['超额收益率(%)'].first().mean():.4f}%")
        
        # 显示各ETF交易次数和收益
        etf_stats = redemption_trades.groupby('ETF代码').agg({
            '交易序号': 'count',
            '胜率(%)': 'first',
            '超额收益率(%)': 'first'
        })
        print(f"  各ETF详情:")
        for etf_code, stats in etf_stats.iterrows():
            print(f"    {etf_code}: {stats['交易序号']}次, 胜率{stats['胜率(%)']}%, 超额收益{stats['超额收益率(%)']}%")

if __name__ == "__main__":
    create_final_report()
