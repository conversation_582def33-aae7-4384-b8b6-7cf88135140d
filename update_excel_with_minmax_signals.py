#!/usr/bin/env python3
"""
更新Excel文件，在"指数汇总统计"sheet中新增"最大单日信号数"和"最小单日信号数"列
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def standardize_etf_code(code):
    """标准化ETF代码，去除后缀"""
    if pd.isna(code):
        return None
    
    code_str = str(code)
    # 去除常见的后缀
    suffixes = ['.SH', '.SZ', '.XSHE', '.XSHG']
    for suffix in suffixes:
        if code_str.endswith(suffix):
            code_str = code_str[:-len(suffix)]
    
    # 确保是6位数字格式
    try:
        return int(code_str)
    except:
        return None

def load_and_calculate_minmax():
    """加载数据并计算最大最小单日信号数"""
    print("=== 加载数据并计算最大最小单日信号数 ===")
    
    # 1. 加载ETF跟踪指数映射关系
    etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
    
    # 2. 加载申赎买入信号数据
    signals_df = pd.read_csv('分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv')
    
    # 转换时间格式
    signals_df['signal_date'] = pd.to_datetime(signals_df['signal_date'])
    signals_df['date'] = signals_df['signal_date'].dt.date
    
    # 标准化ETF代码
    signals_df['etf_code_std'] = signals_df['etf_code'].apply(standardize_etf_code)
    signals_df = signals_df.dropna(subset=['etf_code_std'])
    
    # 创建ETF到指数的映射字典
    etf_to_index = {}
    for _, row in etf_mapping.iterrows():
        etf_code = row['ETF代码']
        index_code = row['跟踪指数代码']
        etf_to_index[etf_code] = index_code
    
    # 为信号数据添加指数代码
    signals_df['index_code'] = signals_df['etf_code_std'].map(etf_to_index)
    mapped_signals = signals_df.dropna(subset=['index_code'])
    
    # 按指数和日期汇总信号
    index_daily_signals = mapped_signals.groupby(['index_code', 'date']).agg({
        'etf_code_std': 'count'  # 每日信号数量
    }).reset_index()
    
    index_daily_signals.columns = ['index_code', 'date', 'daily_signal_count']
    
    # 计算各指数的最大最小单日信号数
    minmax_stats = []
    
    for index_code in sorted(mapped_signals['index_code'].unique()):
        index_signals = index_daily_signals[index_daily_signals['index_code'] == index_code]
        
        max_daily_signals = index_signals['daily_signal_count'].max()
        min_daily_signals = index_signals['daily_signal_count'].min()
        
        minmax_stats.append({
            '指数代码': index_code,
            '最大单日信号数': max_daily_signals,
            '最小单日信号数': min_daily_signals
        })
        
        print(f"指数 {index_code}: 最大单日信号数 {max_daily_signals}, 最小单日信号数 {min_daily_signals}")
    
    return pd.DataFrame(minmax_stats)

def update_excel_file(minmax_df):
    """更新Excel文件"""
    print(f"\n=== 更新Excel文件 ===")
    
    excel_file = '指数信号分布分析结果_申赎买入95分位.xlsx'
    
    # 读取现有的指数汇总统计数据
    existing_df = pd.read_excel(excel_file, sheet_name='指数汇总统计')
    
    print(f"原始数据列: {existing_df.columns.tolist()}")
    print(f"原始数据行数: {len(existing_df)}")
    
    # 合并数据
    updated_df = existing_df.merge(minmax_df, on='指数代码', how='left')
    
    # 重新排列列的顺序，将新增列放在"平均每日信号数"之后
    columns_order = []
    for col in updated_df.columns:
        columns_order.append(col)
        if col == '平均每日信号数':
            columns_order.extend(['最大单日信号数', '最小单日信号数'])
    
    # 去除重复的列名
    columns_order = [col for i, col in enumerate(columns_order) if col not in columns_order[:i]]
    updated_df = updated_df[columns_order]
    
    print(f"更新后数据列: {updated_df.columns.tolist()}")
    print(f"更新后数据行数: {len(updated_df)}")
    
    # 读取所有其他sheet的数据
    all_sheets = pd.read_excel(excel_file, sheet_name=None)
    
    # 更新指数汇总统计sheet
    all_sheets['指数汇总统计'] = updated_df
    
    # 重新保存Excel文件
    with pd.ExcelWriter(excel_file, engine='xlsxwriter') as writer:
        for sheet_name, sheet_data in all_sheets.items():
            sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 如果是指数汇总统计sheet，设置列宽
            if sheet_name == '指数汇总统计':
                worksheet = writer.sheets[sheet_name]
                
                # 设置列宽
                worksheet.set_column('A:A', 12)  # 指数代码
                worksheet.set_column('B:B', 15)  # 有信号交易日数
                worksheet.set_column('C:C', 12)  # 总信号数量
                worksheet.set_column('D:D', 15)  # 平均每日信号数
                worksheet.set_column('E:E', 15)  # 最大单日信号数
                worksheet.set_column('F:F', 15)  # 最小单日信号数
                worksheet.set_column('G:G', 12)  # 信号密度等级
                worksheet.set_column('H:H', 15)  # 信号开始日期
                worksheet.set_column('I:I', 15)  # 信号结束日期
    
    print(f"Excel文件已更新: {excel_file}")
    
    # 显示更新后的数据预览
    print(f"\n=== 更新后的指数汇总统计预览 ===")
    print(updated_df.to_string(index=False))
    
    return updated_df

def generate_update_report(updated_df):
    """生成更新报告"""
    print(f"\n=== 生成更新报告 ===")
    
    report_content = f"""# Excel文件更新报告

## 更新概述

**更新时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**更新文件**: 指数信号分布分析结果_申赎买入95分位.xlsx  
**更新内容**: 在"指数汇总统计"sheet中新增"最大单日信号数"和"最小单日信号数"列

## 更新后的指数汇总统计

| 指数代码 | 有信号交易日数 | 总信号数量 | 平均每日信号数 | 最大单日信号数 | 最小单日信号数 | 信号密度等级 |
|---------|---------------|-----------|---------------|---------------|---------------|-------------|
"""
    
    for _, row in updated_df.iterrows():
        report_content += f"| {row['指数代码']} | {row['有信号交易日数']} | {row['总信号数量']} | {row['平均每日信号数']} | {row['最大单日信号数']} | {row['最小单日信号数']} | {row['信号密度等级']} |\n"
    
    report_content += f"""

## 新增列说明

### 最大单日信号数
- **定义**: 该指数在单个交易日内出现的最大信号数量
- **用途**: 评估指数信号的峰值活跃度
- **分析价值**: 帮助识别信号爆发期，制定应对策略

### 最小单日信号数
- **定义**: 该指数在有信号的交易日内出现的最小信号数量
- **用途**: 评估指数信号的基础活跃度
- **分析价值**: 了解信号的稳定性和一致性

## 数据分析

### 信号波动性分析

"""
    
    # 计算信号波动性
    updated_df['信号波动幅度'] = updated_df['最大单日信号数'] - updated_df['最小单日信号数']
    updated_df['信号波动率'] = updated_df['信号波动幅度'] / updated_df['平均每日信号数']
    
    # 按波动性排序
    volatility_sorted = updated_df.sort_values('信号波动幅度', ascending=False)
    
    report_content += f"#### 信号波动性排名（按波动幅度）\n\n"
    
    for i, (_, row) in enumerate(volatility_sorted.iterrows(), 1):
        波动幅度 = row['信号波动幅度']
        波动率 = row['信号波动率']
        
        if 波动幅度 >= 3:
            波动等级 = "高波动"
        elif 波动幅度 >= 2:
            波动等级 = "中波动"
        else:
            波动等级 = "低波动"
        
        report_content += f"{i}. **指数{row['指数代码']}**: 波动幅度{波动幅度} ({row['最小单日信号数']}-{row['最大单日信号数']}), 波动率{波动率:.2f}, {波动等级}\n"
    
    report_content += f"""

### 信号稳定性分析

#### 高稳定性指数（波动幅度≤1）
"""
    
    stable_indices = updated_df[updated_df['信号波动幅度'] <= 1]
    if len(stable_indices) > 0:
        for _, row in stable_indices.iterrows():
            report_content += f"- **指数{row['指数代码']}**: 信号稳定，每日{row['最小单日信号数']}-{row['最大单日信号数']}个信号\n"
    else:
        report_content += "- 无高稳定性指数\n"
    
    report_content += f"""

#### 高波动性指数（波动幅度≥3）
"""
    
    volatile_indices = updated_df[updated_df['信号波动幅度'] >= 3]
    if len(volatile_indices) > 0:
        for _, row in volatile_indices.iterrows():
            report_content += f"- **指数{row['指数代码']}**: 信号波动大，每日{row['最小单日信号数']}-{row['最大单日信号数']}个信号\n"
    else:
        report_content += "- 无高波动性指数\n"
    
    report_content += f"""

## 投资策略建议

### 基于信号波动性的策略调整

#### 1. 低波动指数策略
- **目标指数**: {', '.join(map(str, stable_indices['指数代码'].tolist())) if len(stable_indices) > 0 else '无'}
- **策略特点**: 信号稳定，便于预测和执行
- **适用场景**: 稳健投资，长期配置
- **执行建议**: 可以设置固定的仓位和交易频率

#### 2. 高波动指数策略
- **目标指数**: {', '.join(map(str, volatile_indices['指数代码'].tolist())) if len(volatile_indices) > 0 else '无'}
- **策略特点**: 信号变化大，需要灵活应对
- **适用场景**: 积极投资，短期交易
- **执行建议**: 需要动态调整仓位，准备应对信号爆发

### 风险管理建议

1. **仓位控制**: 根据最大单日信号数设置最大仓位限制
2. **资金准备**: 为高波动指数准备充足的备用资金
3. **执行能力**: 确保有足够的执行能力应对信号峰值期

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存报告
    report_path = 'Excel更新报告_最大最小单日信号数.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"更新报告已保存: {report_path}")
    
    return report_path

def main():
    """主函数"""
    print("=== 更新Excel文件：新增最大最小单日信号数列 ===")
    print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 计算最大最小单日信号数
    minmax_df = load_and_calculate_minmax()
    
    # 更新Excel文件
    updated_df = update_excel_file(minmax_df)
    
    # 生成更新报告
    report_path = generate_update_report(updated_df)
    
    # 显示关键统计
    print(f"\n=== 更新完成摘要 ===")
    print(f"更新的指数数量: {len(updated_df)}")
    print(f"新增列数: 2 (最大单日信号数, 最小单日信号数)")
    print(f"最大的单日信号数: {updated_df['最大单日信号数'].max()}")
    print(f"最小的单日信号数: {updated_df['最小单日信号数'].min()}")
    print(f"平均波动幅度: {(updated_df['最大单日信号数'] - updated_df['最小单日信号数']).mean():.2f}")
    
    print(f"\n更新文件: 指数信号分布分析结果_申赎买入95分位.xlsx")
    print(f"更新报告: {report_path}")

if __name__ == "__main__":
    main()
