# -*- coding: utf-8 -*-
"""
基于现有信号明细数据计算median_return并更新Excel文件
"""

import pandas as pd
import numpy as np
import os
from pathlib import Path

def calculate_median_return_from_csv():
    """
    从CSV文件读取信号明细数据，计算各分位、持有周期下的收益率中位数
    """

    # 查找CSV文件
    csv_path = None
    possible_paths = [
        "所有信号明细_5min_PE筛选.csv",
        "回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv"
    ]

    for path in possible_paths:
        if os.path.exists(path):
            csv_path = path
            break

    if csv_path is None:
        print("未找到信号明细CSV文件")
        return None

    print(f"读取信号明细数据: {csv_path}")

    # 读取CSV数据
    signals_df = pd.read_csv(csv_path)

    # 转换datetime列为正确的数据类型
    signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])

    print(f"数据概览:")
    print(f"  总信号数: {len(signals_df)}")
    print(f"  数据列: {list(signals_df.columns)}")
    print(f"  时间范围: {signals_df['datetime'].min()} 到 {signals_df['datetime'].max()}")

    # 筛选有效信号（通过PE筛选且有收益数据）
    valid_signals = signals_df[
        (signals_df['pe_filter_passed'] == True) &
        (signals_df['return'].notna())
    ].copy()

    print(f"  有效信号数: {len(valid_signals)}")

    # 按阈值和持有周期分组计算统计指标
    summary_stats = []

    for (threshold, period), group in valid_signals.groupby(['threshold', 'holding_period']):
        if len(group) > 0:
            returns = group['return'].values

            # 计算各种统计指标
            total_signals = len(returns)
            win_rate = np.sum(returns > 0) / len(returns) * 100
            avg_return = np.mean(returns) * 100
            median_return = np.median(returns) * 100  # 新增中位数收益率

            # 计算年化收益率（使用平均收益）
            annualized_return = np.nan
            unique_signal_days = group['datetime'].dt.date.nunique()
            total_days_in_data = signals_df['datetime'].dt.date.nunique()

            if unique_signal_days > 0 and total_days_in_data > 0:
                triggers_per_year_estimate = (unique_signal_days / total_days_in_data) * 252
                if (1 + avg_return/100) > 0:
                    annualized_return = ((1 + avg_return/100) ** triggers_per_year_estimate - 1) * 100
                else:
                    annualized_return = -100.0

            summary_stats.append({
                'threshold': f'大于{threshold}分位',
                'period': period,
                'total_signals': total_signals,
                'avg_win_rate': win_rate,
                'avg_return': avg_return,
                'median_return': median_return,  # 新增列
                'annualized_return': annualized_return
            })

    if summary_stats:
        summary_df = pd.DataFrame(summary_stats)
        summary_df = summary_df.sort_values(by=['threshold', 'period'])
        return summary_df
    else:
        print("没有有效的统计数据")
        return None

def update_excel_with_median_return():
    """
    更新Excel文件，在汇总统计结果中增加median_return列
    """

    # 计算median_return数据
    summary_df = calculate_median_return_from_csv()
    if summary_df is None:
        return

    print("\n计算得到的汇总统计结果（包含median_return）:")
    print(summary_df.head(10))

    # 查找现有Excel文件
    excel_path = None
    possible_excel_paths = [
        "回测结果_5min_PE筛选.xlsx",
        "回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/回测结果_5min_PE筛选.xlsx"
    ]

    for path in possible_excel_paths:
        if os.path.exists(path):
            excel_path = path
            break

    if excel_path is None:
        print("未找到现有Excel文件，将创建新文件")
        excel_path = "回测结果_5min_PE筛选_含median_return.xlsx"
    else:
        print(f"找到现有Excel文件: {excel_path}")

    # 创建新的Excel文件
    output_excel_path = "回测结果_5min_PE筛选_含median_return.xlsx"

    try:
        # 如果存在原Excel文件，先读取其他sheet的数据
        existing_sheets = {}
        if os.path.exists(excel_path):
            try:
                with pd.ExcelFile(excel_path) as xls:
                    for sheet_name in xls.sheet_names:
                        if sheet_name != '汇总统计结果':
                            existing_sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)
                            print(f"读取现有sheet: {sheet_name}")
            except Exception as e:
                print(f"读取现有Excel文件时出错: {e}")

        # 写入新的Excel文件
        with pd.ExcelWriter(output_excel_path, engine='xlsxwriter') as writer:
            # 写入更新后的汇总统计结果
            summary_df.to_excel(writer, sheet_name='汇总统计结果', index=False)
            print("已写入更新后的汇总统计结果")

            # 写入其他现有的sheets
            for sheet_name, sheet_data in existing_sheets.items():
                sheet_data.to_excel(writer, sheet_name=sheet_name, index=False)
                print(f"已写入sheet: {sheet_name}")

        print(f"\n新的Excel文件已保存: {output_excel_path}")

        # 显示新增列的统计信息
        print(f"\nmedian_return统计信息:")
        print(f"  最小值: {summary_df['median_return'].min():.4f}%")
        print(f"  最大值: {summary_df['median_return'].max():.4f}%")
        print(f"  平均值: {summary_df['median_return'].mean():.4f}%")
        print(f"  中位数: {summary_df['median_return'].median():.4f}%")

        return output_excel_path

    except Exception as e:
        print(f"更新Excel文件时出错: {e}")
        return None

def compare_avg_vs_median_returns():
    """
    比较平均收益率和中位数收益率的差异
    """
    summary_df = calculate_median_return_from_csv()
    if summary_df is None:
        return

    # 计算差异
    summary_df['return_diff'] = summary_df['avg_return'] - summary_df['median_return']
    summary_df['return_diff_pct'] = (summary_df['return_diff'] / summary_df['median_return'] * 100).round(2)

    print("\n=== 平均收益率 vs 中位数收益率对比分析 ===")
    print("(显示主要持有周期)")

    main_periods = ['next_day_close', 'next_5day_close', 'next_10day_close', 'next_30day_close']
    comparison_data = summary_df[summary_df['period'].isin(main_periods)].copy()

    print(f"{'阈值':<12} {'持有周期':<18} {'平均收益率':<10} {'中位数收益率':<12} {'差异':<8} {'差异%':<8}")
    print("-" * 80)

    for _, row in comparison_data.iterrows():
        print(f"{row['threshold']:<12} {row['period']:<18} {row['avg_return']:<10.4f} {row['median_return']:<12.4f} {row['return_diff']:<8.4f} {row['return_diff_pct']:<8.2f}")

    # 分析差异的统计特征
    print(f"\n差异统计:")
    print(f"  平均差异: {comparison_data['return_diff'].mean():.4f}%")
    print(f"  差异标准差: {comparison_data['return_diff'].std():.4f}%")
    print(f"  最大正差异: {comparison_data['return_diff'].max():.4f}% (平均>中位数)")
    print(f"  最大负差异: {comparison_data['return_diff'].min():.4f}% (平均<中位数)")

    # 保存对比结果
    comparison_data.to_csv('收益率对比分析.csv', index=False, encoding='utf-8-sig')
    print(f"\n对比分析结果已保存: 收益率对比分析.csv")

if __name__ == "__main__":
    print("=== 计算median_return并更新Excel文件 ===")

    # 更新Excel文件
    output_path = update_excel_with_median_return()

    if output_path:
        print(f"\n✅ 成功更新Excel文件: {output_path}")

        # 进行对比分析
        compare_avg_vs_median_returns()

        print("\n📊 主要改进:")
        print("1. 在汇总统计结果中新增了 'median_return' 列")
        print("2. 提供了平均收益率与中位数收益率的对比分析")
        print("3. 保留了原有的所有数据和分析结果")

    else:
        print("\n❌ 更新Excel文件失败")
