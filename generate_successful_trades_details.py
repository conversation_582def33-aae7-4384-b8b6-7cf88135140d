#!/usr/bin/env python3
"""
生成各ETF实际成功交易的详细明细数据
包含信号日期、结束日期、区间收益率和超额收益率
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """加载所有必要的数据"""
    print("=== 加载数据 ===")
    
    # 1. 加载ETF跟踪指数映射表
    etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
    
    # 2. 加载指数行情数据
    index_data = pd.read_excel('指数行情序列.xlsx')
    index_data['时间'] = pd.to_datetime(index_data['时间'])
    # 处理指数代码格式
    if '代码' in index_data.columns:
        index_data['代码'] = index_data['代码'].astype(str).str.replace('.SH', '').str.replace('.SZ', '').str.zfill(6)
    
    # 3. 加载原始信号数据
    signals_path = '回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv'
    signals_df = pd.read_csv(signals_path)
    signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
    signals_df['date'] = signals_df['datetime'].dt.date
    
    print(f"ETF映射关系: {len(etf_mapping)} 条")
    print(f"指数行情数据: {len(index_data)} 条")
    print(f"原始信号数据: {len(signals_df)} 条")
    
    return etf_mapping, index_data, signals_df

def get_trading_days(index_data, start_date, end_date):
    """获取交易日列表"""
    trading_days = index_data[
        (index_data['时间'].dt.date >= start_date) & 
        (index_data['时间'].dt.date <= end_date)
    ]['时间'].dt.date.unique()
    return sorted(trading_days)

def generate_buy_signals(signals_df, start_date, end_date):
    """生成买入信号（严格按照90分位PE筛选）"""
    
    # 筛选条件：严格按照用户要求
    valid_signals = signals_df[
        (signals_df['pe_filter_passed'] == True) &
        (signals_df['threshold'] == 90) &
        (signals_df['date'] >= start_date) &
        (signals_df['date'] <= end_date)
    ].copy()
    
    # 每日每个ETF只保留一个信号（取最早的信号）
    daily_signals = valid_signals.groupby(['etf_code', 'date']).first().reset_index()
    daily_signals = daily_signals.sort_values(['etf_code', 'date'])
    
    print(f"有效信号数: {len(valid_signals)}")
    print(f"每日合并后信号数: {len(daily_signals)}")
    
    return daily_signals

def calculate_successful_trades_details(etf_code, signals, index_data, etf_mapping, trading_days, start_date, end_date):
    """
    计算单个ETF的成功交易详细信息
    """
    # 获取ETF对应的指数代码
    etf_row = etf_mapping[etf_mapping['ETF代码'] == etf_code]
    if etf_row.empty:
        return []
    
    index_code = str(etf_row.iloc[0]['跟踪指数代码']).zfill(6)
    
    # 获取指数行情数据
    index_prices = index_data[index_data['代码'] == index_code].copy()
    
    # 特殊处理某些指数格式问题
    if index_prices.empty and index_code == '746059':
        index_prices = index_data[index_data['代码'] == '746059.MSI'].copy()
    
    if index_prices.empty:
        return []
    
    index_prices = index_prices.sort_values('时间')
    index_prices['date'] = index_prices['时间'].dt.date
    
    # 计算指数基准收益率
    start_data = index_prices[index_prices['时间'].dt.date >= start_date]
    end_data = index_prices[index_prices['时间'].dt.date <= end_date]
    
    if start_data.empty or end_data.empty:
        benchmark_return = 0.0
    else:
        start_price = start_data.iloc[0]['开盘价(元)']
        end_price = end_data.iloc[-1]['收盘价(元)']
        benchmark_return = (end_price - start_price) / start_price if start_price > 0 else 0.0
    
    # 获取该ETF的信号
    etf_signals = signals[signals['etf_code'] == etf_code].copy()
    etf_signals = etf_signals.sort_values('date')
    
    if etf_signals.empty:
        return []
    
    successful_trades = []
    holding_end_date = None
    
    for _, signal in etf_signals.iterrows():
        signal_date = signal['date']
        
        # 如果还在持有期内，跳过新信号
        if holding_end_date and signal_date <= holding_end_date:
            continue
        
        # 找到信号日期在交易日列表中的位置
        try:
            signal_date_idx = trading_days.index(signal_date)
        except ValueError:
            continue
        
        # 下一个交易日作为买入日
        if signal_date_idx + 1 >= len(trading_days):
            continue
        
        buy_date = trading_days[signal_date_idx + 1]
        
        # 15个交易日后作为卖出日
        if signal_date_idx + 16 >= len(trading_days):
            continue
        
        sell_date = trading_days[signal_date_idx + 16]
        
        # 获取买入日开盘价和卖出日收盘价
        buy_price_data = index_prices[index_prices['date'] == buy_date]
        sell_price_data = index_prices[index_prices['date'] == sell_date]
        
        if buy_price_data.empty or sell_price_data.empty:
            continue
        
        buy_price = buy_price_data.iloc[0]['开盘价(元)']
        sell_price = sell_price_data.iloc[0]['收盘价(元)']
        
        if pd.isna(buy_price) or pd.isna(sell_price) or buy_price <= 0:
            continue
        
        # 计算区间收益率
        period_return = (sell_price - buy_price) / buy_price
        
        # 只保留成功交易（收益率 > 0）
        if period_return > 0:
            # 计算同期指数基准收益率（买入日到卖出日）
            buy_index_price = buy_price
            sell_index_price = sell_price
            period_benchmark_return = (sell_index_price - buy_index_price) / buy_index_price
            
            # 计算区间超额收益率
            period_excess_return = period_return - period_benchmark_return
            
            successful_trades.append({
                'ETF代码': etf_code,
                '跟踪指数': index_code,
                '信号日期': signal_date.strftime('%Y-%m-%d'),
                '买入日期': buy_date.strftime('%Y-%m-%d'),
                '卖出日期': sell_date.strftime('%Y-%m-%d'),
                '买入价格': round(buy_price, 4),
                '卖出价格': round(sell_price, 4),
                '区间收益率(%)': round(period_return * 100, 4),
                '区间基准收益率(%)': round(period_benchmark_return * 100, 4),
                '区间超额收益率(%)': round(period_excess_return * 100, 4),
                '持有天数': 15,
                '信号时间': signal['datetime'].strftime('%Y-%m-%d %H:%M:%S')
            })
        
        # 更新持有结束日期
        holding_end_date = sell_date
    
    return successful_trades

def main():
    """主函数"""
    print("=== 生成ETF成功交易详细明细数据 ===")
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 分析时间范围
    start_date = pd.to_datetime('2024-04-30').date()
    end_date = pd.to_datetime('2025-04-30').date()
    
    # 加载数据
    etf_mapping, index_data, signals_df = load_data()
    
    # 获取交易日
    trading_days = get_trading_days(index_data, start_date, end_date)
    print(f"交易日数量: {len(trading_days)}")
    
    # 生成买入信号
    buy_signals = generate_buy_signals(signals_df, start_date, end_date)
    
    # 计算每个ETF的成功交易详情
    all_successful_trades = []
    etf_codes = sorted(buy_signals['etf_code'].unique())
    
    print(f"\n=== 开始计算 {len(etf_codes)} 个ETF的成功交易详情 ===")
    
    for etf_code in etf_codes:
        print(f"正在处理 ETF {etf_code}...")
        
        successful_trades = calculate_successful_trades_details(
            etf_code, buy_signals, index_data, etf_mapping, trading_days, start_date, end_date
        )
        
        if successful_trades:
            all_successful_trades.extend(successful_trades)
            print(f"  ETF {etf_code}: {len(successful_trades)} 笔成功交易")
        else:
            print(f"  ETF {etf_code}: 无成功交易")
    
    # 转换为DataFrame
    if all_successful_trades:
        trades_df = pd.DataFrame(all_successful_trades)
        trades_df = trades_df.sort_values(['ETF代码', '信号日期'])
        
        # 保存到Excel文件
        excel_path = '超额收益_分钟级成交额_90分位信号/ETF成功交易详细明细.xlsx'
        with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
            
            # 所有成功交易明细
            trades_df.to_excel(writer, sheet_name='成功交易明细', index=False)
            
            # 按ETF分组的汇总
            etf_summary = trades_df.groupby('ETF代码').agg({
                '跟踪指数': 'first',
                '区间收益率(%)': ['count', 'mean', 'sum'],
                '区间超额收益率(%)': ['mean', 'sum']
            }).round(4)
            
            etf_summary.columns = ['跟踪指数', '成功交易次数', '平均区间收益率(%)', '累计区间收益率(%)', '平均区间超额收益率(%)', '累计区间超额收益率(%)']
            etf_summary = etf_summary.reset_index()
            etf_summary.to_excel(writer, sheet_name='ETF成功交易汇总', index=False)
            
            # 统计摘要
            stats_data = [
                ['分析时间范围', f'{start_date} 到 {end_date}'],
                ['参与ETF数量', trades_df['ETF代码'].nunique()],
                ['成功交易总次数', len(trades_df)],
                ['平均区间收益率(%)', trades_df['区间收益率(%)'].mean()],
                ['平均区间超额收益率(%)', trades_df['区间超额收益率(%)'].mean()],
                ['最高单次收益率(%)', trades_df['区间收益率(%)'].max()],
                ['最高单次超额收益率(%)', trades_df['区间超额收益率(%)'].max()]
            ]
            stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
            stats_df.to_excel(writer, sheet_name='统计摘要', index=False)
        
        print(f"\n=== 生成完成 ===")
        print(f"Excel文件已保存: {excel_path}")
        print(f"成功交易总次数: {len(trades_df)}")
        print(f"涉及ETF数量: {trades_df['ETF代码'].nunique()}")
        print(f"平均区间收益率: {trades_df['区间收益率(%)'].mean():.4f}%")
        print(f"平均区间超额收益率: {trades_df['区间超额收益率(%)'].mean():.4f}%")
        
        # 显示各ETF成功交易次数
        print(f"\n各ETF成功交易次数:")
        etf_counts = trades_df['ETF代码'].value_counts().sort_index()
        for etf_code, count in etf_counts.items():
            avg_return = trades_df[trades_df['ETF代码'] == etf_code]['区间收益率(%)'].mean()
            avg_excess = trades_df[trades_df['ETF代码'] == etf_code]['区间超额收益率(%)'].mean()
            print(f"  ETF {etf_code}: {count}次, 平均收益{avg_return:.4f}%, 平均超额收益{avg_excess:.4f}%")
        
        # 显示前10笔最佳交易
        print(f"\n前10笔最佳成功交易:")
        top_trades = trades_df.nlargest(10, '区间收益率(%)')
        for _, trade in top_trades.iterrows():
            print(f"  ETF {trade['ETF代码']} {trade['信号日期']}: 收益{trade['区间收益率(%)']}%, 超额收益{trade['区间超额收益率(%)']}%")
        
    else:
        print("未找到成功交易数据")

if __name__ == "__main__":
    main()
