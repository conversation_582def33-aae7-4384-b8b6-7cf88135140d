#!/usr/bin/env python3
"""
分析跟踪相同指数的ETF发出信号日期的相关性
"""

import pandas as pd
import numpy as np
from datetime import datetime
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

def load_etf_index_mapping():
    """加载ETF跟踪指数映射关系"""
    print("=== 加载ETF跟踪指数映射关系 ===")
    
    try:
        etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
        print(f"ETF映射数据行数: {len(etf_mapping)}")
        print(f"列名: {etf_mapping.columns.tolist()}")
        
        # 显示前几行数据
        print(f"前5行数据:")
        print(etf_mapping.head())
        
        # 统计跟踪相同指数的ETF
        index_etf_groups = etf_mapping.groupby('跟踪指数代码')['ETF代码'].apply(list).to_dict()
        
        # 筛选出有多个ETF跟踪的指数
        multi_etf_indices = {index_code: etfs for index_code, etfs in index_etf_groups.items() if len(etfs) > 1}
        
        print(f"\n跟踪相同指数的ETF组合:")
        for index_code, etfs in multi_etf_indices.items():
            index_name = etf_mapping[etf_mapping['跟踪指数代码'] == index_code]['跟踪指数名称'].iloc[0] if '跟踪指数名称' in etf_mapping.columns else f"指数_{index_code}"
            print(f"  指数 {index_code} ({index_name}): {etfs}")
        
        return etf_mapping, multi_etf_indices
        
    except Exception as e:
        print(f"加载ETF映射关系时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame(), {}

def load_signal_data():
    """加载90分位成交额信号数据"""
    print(f"\n=== 加载90分位成交额信号数据 ===")
    
    try:
        # 读取信号数据
        signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
        
        print(f"原始信号数据行数: {len(signals_df)}")
        
        # 转换时间格式
        signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
        signals_df['date'] = signals_df['datetime'].dt.date
        
        # 筛选条件：pe_filter_passed=True且threshold=90
        filtered_signals = signals_df[
            (signals_df['pe_filter_passed'] == True) &
            (signals_df['threshold'] == 90)
        ].copy()
        
        print(f"PE筛选且90分位信号数: {len(filtered_signals)}")
        
        # 每日每个ETF只保留一个信号
        daily_signals = filtered_signals.groupby(['etf_code', 'date']).first().reset_index()
        daily_signals = daily_signals.sort_values(['etf_code', 'date'])
        
        print(f"每日合并后信号数: {len(daily_signals)}")
        print(f"涉及ETF数量: {daily_signals['etf_code'].nunique()}")
        
        # 显示各ETF信号数量
        etf_signal_counts = daily_signals['etf_code'].value_counts().sort_index()
        print(f"\n各ETF信号数量:")
        for etf_code, count in etf_signal_counts.head(10).items():
            print(f"  ETF {etf_code}: {count}个")
        
        return daily_signals
        
    except Exception as e:
        print(f"加载信号数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def calculate_etf_pair_correlation(etf1_signals, etf2_signals, etf1_code, etf2_code):
    """计算两个ETF信号的相关性"""
    
    # 获取信号日期集合
    etf1_dates = set(etf1_signals['date'])
    etf2_dates = set(etf2_signals['date'])
    
    # 计算重叠和并集
    overlap_dates = etf1_dates.intersection(etf2_dates)
    union_dates = etf1_dates.union(etf2_dates)
    
    # 计算Jaccard相似度
    jaccard_similarity = len(overlap_dates) / len(union_dates) if len(union_dates) > 0 else 0
    
    # 计算各自的覆盖率
    etf1_coverage = len(overlap_dates) / len(etf1_dates) if len(etf1_dates) > 0 else 0
    etf2_coverage = len(overlap_dates) / len(etf2_dates) if len(etf2_dates) > 0 else 0
    
    return {
        'ETF1代码': etf1_code,
        'ETF2代码': etf2_code,
        'ETF1信号数': len(etf1_dates),
        'ETF2信号数': len(etf2_dates),
        '重叠信号数': len(overlap_dates),
        'Jaccard相似度': jaccard_similarity,
        'ETF1覆盖率': etf1_coverage,
        'ETF2覆盖率': etf2_coverage,
        '重叠日期': sorted([date.strftime('%Y-%m-%d') for date in overlap_dates])
    }

def analyze_same_index_correlation(etf_mapping, multi_etf_indices, signals_df):
    """分析跟踪相同指数的ETF信号相关性"""
    print(f"\n=== 分析跟踪相同指数的ETF信号相关性 ===")
    
    correlation_results = []
    detailed_overlaps = []
    
    for index_code, etf_list in multi_etf_indices.items():
        print(f"\n--- 分析指数 {index_code} ---")
        
        # 获取指数名称
        index_name = "未知指数"
        if '跟踪指数名称' in etf_mapping.columns:
            index_info = etf_mapping[etf_mapping['跟踪指数代码'] == index_code]
            if not index_info.empty:
                index_name = index_info['跟踪指数名称'].iloc[0]
        
        print(f"指数名称: {index_name}")
        print(f"跟踪该指数的ETF: {etf_list}")
        
        # 获取每个ETF的信号数据
        etf_signals = {}
        for etf_code in etf_list:
            etf_data = signals_df[signals_df['etf_code'] == etf_code]
            if not etf_data.empty:
                etf_signals[etf_code] = etf_data
                print(f"  ETF {etf_code}: {len(etf_data)} 个信号")
            else:
                print(f"  ETF {etf_code}: 无信号数据")
        
        # 计算ETF两两之间的相关性
        valid_etfs = list(etf_signals.keys())
        if len(valid_etfs) < 2:
            print(f"  该指数下有效ETF少于2个，跳过相关性分析")
            continue
        
        print(f"  开始计算 {len(valid_etfs)} 个ETF的两两相关性...")
        
        for etf1, etf2 in combinations(valid_etfs, 2):
            correlation = calculate_etf_pair_correlation(
                etf_signals[etf1], etf_signals[etf2], etf1, etf2
            )
            
            # 添加指数信息
            correlation['跟踪指数代码'] = index_code
            correlation['跟踪指数名称'] = index_name
            
            correlation_results.append(correlation)
            
            print(f"    ETF {etf1} vs ETF {etf2}: Jaccard相似度 {correlation['Jaccard相似度']:.4f}, 重叠 {correlation['重叠信号数']} 个")
            
            # 记录重叠日期详情
            for overlap_date in correlation['重叠日期']:
                detailed_overlaps.append({
                    '跟踪指数代码': index_code,
                    '跟踪指数名称': index_name,
                    'ETF1代码': etf1,
                    'ETF2代码': etf2,
                    '重叠日期': overlap_date
                })
    
    return correlation_results, detailed_overlaps

def generate_correlation_analysis_report(correlation_results, detailed_overlaps):
    """生成相关性分析报告"""
    
    if not correlation_results:
        print("没有相关性分析结果")
        return
    
    results_df = pd.DataFrame(correlation_results)
    
    print(f"\n=== 跟踪相同指数ETF信号相关性分析结果 ===")
    print(f"分析的ETF对数: {len(results_df)}")
    print(f"平均Jaccard相似度: {results_df['Jaccard相似度'].mean():.4f}")
    print(f"最高Jaccard相似度: {results_df['Jaccard相似度'].max():.4f}")
    print(f"最低Jaccard相似度: {results_df['Jaccard相似度'].min():.4f}")
    
    # 相关性强度分类
    high_correlation = results_df[results_df['Jaccard相似度'] >= 0.5]
    medium_correlation = results_df[(results_df['Jaccard相似度'] >= 0.3) & (results_df['Jaccard相似度'] < 0.5)]
    low_correlation = results_df[results_df['Jaccard相似度'] < 0.3]
    
    print(f"\n相关性分类:")
    print(f"强相关(Jaccard≥0.5): {len(high_correlation)}对 ({len(high_correlation)/len(results_df)*100:.1f}%)")
    print(f"中等相关(0.3≤Jaccard<0.5): {len(medium_correlation)}对 ({len(medium_correlation)/len(results_df)*100:.1f}%)")
    print(f"弱相关(Jaccard<0.3): {len(low_correlation)}对 ({len(low_correlation)/len(results_df)*100:.1f}%)")
    
    # 显示最相关的ETF对
    print(f"\n最相关的ETF对 (前10名):")
    top_correlations = results_df.nlargest(10, 'Jaccard相似度')
    for _, row in top_correlations.iterrows():
        print(f"  {row['跟踪指数名称']} - ETF {row['ETF1代码']} vs ETF {row['ETF2代码']}: {row['Jaccard相似度']:.4f}")
    
    # 按指数分组分析
    print(f"\n按指数分组的相关性分析:")
    index_stats = results_df.groupby('跟踪指数代码').agg({
        'Jaccard相似度': ['count', 'mean', 'max', 'min'],
        '跟踪指数名称': 'first'
    }).round(4)
    
    index_stats.columns = ['ETF对数', '平均相似度', '最高相似度', '最低相似度', '指数名称']
    index_stats = index_stats.reset_index()
    
    for _, row in index_stats.iterrows():
        print(f"  指数 {row['跟踪指数代码']} ({row['指数名称']}): {row['ETF对数']}对, 平均相似度 {row['平均相似度']:.4f}")
    
    # 保存结果
    save_same_index_correlation_results(results_df, detailed_overlaps, index_stats)

def save_same_index_correlation_results(results_df, detailed_overlaps, index_stats):
    """保存相关性分析结果"""
    
    excel_path = '跟踪相同指数ETF信号相关性分析结果.xlsx'
    
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        
        # ETF对相关性结果
        results_df.to_excel(writer, sheet_name='ETF对相关性分析', index=False)
        
        # 重叠日期明细
        if detailed_overlaps:
            detailed_df = pd.DataFrame(detailed_overlaps)
            detailed_df.to_excel(writer, sheet_name='重叠日期明细', index=False)
        
        # 按指数分组统计
        index_stats.to_excel(writer, sheet_name='按指数分组统计', index=False)
        
        # 统计摘要
        stats_data = [
            ['分析时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['分析的ETF对数', len(results_df)],
            ['平均Jaccard相似度', results_df['Jaccard相似度'].mean()],
            ['最高Jaccard相似度', results_df['Jaccard相似度'].max()],
            ['最低Jaccard相似度', results_df['Jaccard相似度'].min()],
            ['强相关ETF对数(Jaccard≥0.5)', len(results_df[results_df['Jaccard相似度'] >= 0.5])],
            ['中等相关ETF对数(0.3≤Jaccard<0.5)', len(results_df[(results_df['Jaccard相似度'] >= 0.3) & (results_df['Jaccard相似度'] < 0.5)])],
            ['弱相关ETF对数(Jaccard<0.3)', len(results_df[results_df['Jaccard相似度'] < 0.3])],
            ['涉及指数数量', results_df['跟踪指数代码'].nunique()]
        ]
        stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
        stats_df.to_excel(writer, sheet_name='统计摘要', index=False)
    
    print(f"\n相关性分析结果已保存: {excel_path}")

def main():
    """主函数"""
    print("=== 跟踪相同指数ETF信号相关性分析 ===")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 加载ETF跟踪指数映射关系
    etf_mapping, multi_etf_indices = load_etf_index_mapping()
    
    if not multi_etf_indices:
        print("未找到跟踪相同指数的ETF组合")
        return
    
    # 加载信号数据
    signals_df = load_signal_data()
    
    if signals_df.empty:
        print("信号数据为空")
        return
    
    # 分析相关性
    correlation_results, detailed_overlaps = analyze_same_index_correlation(
        etf_mapping, multi_etf_indices, signals_df
    )
    
    # 生成分析报告
    generate_correlation_analysis_report(correlation_results, detailed_overlaps)
    
    print(f"\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
