#!/usr/bin/env python3
"""
生成详细的ETF交易汇总表
基于现有的超额收益分析结果
"""

import pandas as pd
from datetime import datetime

def load_existing_results():
    """加载现有的超额收益分析结果"""
    
    # 90分位成交额信号结果
    try:
        turnover_excel = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        print(f"90分位成交额信号结果: {len(turnover_excel)} 个ETF")
    except:
        turnover_excel = pd.DataFrame()
        print("未找到90分位成交额信号结果文件")
    
    # 申赎信号结果
    try:
        redemption_excel = pd.read_excel('超额收益_申赎信号/ETF申赎信号择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        print(f"申赎信号结果: {len(redemption_excel)} 个ETF")
    except:
        redemption_excel = pd.DataFrame()
        print("未找到申赎信号结果文件")
    
    return turnover_excel, redemption_excel

def generate_detailed_signal_dates():
    """生成详细的信号日期信息"""
    
    # 90分位信号日期
    try:
        signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
        signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
        signals_df['date'] = signals_df['datetime'].dt.date
        
        filtered_signals = signals_df[
            (signals_df['pe_filter_passed'] == True) &
            (signals_df['threshold'] == 90) &
            (signals_df['holding_period'] == 'next_15day_close')
        ].copy()
        
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()
        
        period_signals = filtered_signals[
            (filtered_signals['date'] >= start_date) &
            (filtered_signals['date'] <= end_date)
        ].copy()
        
        daily_signals = period_signals.groupby(['etf_code', 'date']).first().reset_index()
        
        # 按ETF整理信号日期
        turnover_signal_dates = {}
        for etf_code, group in daily_signals.groupby('etf_code'):
            signal_dates = sorted(group['date'].tolist())
            turnover_signal_dates[etf_code] = [date.strftime('%Y-%m-%d') for date in signal_dates]
        
    except Exception as e:
        print(f"处理90分位信号日期时出错: {e}")
        turnover_signal_dates = {}
    
    # 申赎信号日期
    try:
        signals_df = pd.read_csv('分析结果_PE筛选_20240430_20250430/PE筛选后信号数据汇总表.csv')
        signals_df['日期'] = pd.to_datetime(signals_df['日期'])
        
        start_date = pd.to_datetime('2024-04-30').date()
        end_date = pd.to_datetime('2025-04-30').date()
        
        period_signals = signals_df[
            (signals_df['日期'].dt.date >= start_date) &
            (signals_df['日期'].dt.date <= end_date) &
            (signals_df['PE筛选后信号1_买入'] == 1)
        ].copy()
        
        period_signals['ETF代码_数字'] = period_signals['ETF代码'].str.split('.').str[0].astype(int)
        
        # 按ETF整理信号日期
        redemption_signal_dates = {}
        for etf_code, group in period_signals.groupby('ETF代码_数字'):
            signal_dates = sorted(group['日期'].dt.date.tolist())
            redemption_signal_dates[etf_code] = [date.strftime('%Y-%m-%d') for date in signal_dates]
        
    except Exception as e:
        print(f"处理申赎信号日期时出错: {e}")
        redemption_signal_dates = {}
    
    return turnover_signal_dates, redemption_signal_dates

def create_detailed_summary():
    """创建详细的交易汇总表"""
    
    print("=== 生成详细ETF交易汇总表 ===")
    
    # 加载现有结果
    turnover_excel, redemption_excel = load_existing_results()
    
    # 生成信号日期
    turnover_dates, redemption_dates = generate_detailed_signal_dates()
    
    # 创建90分位成交额信号详细表
    turnover_details = []
    if not turnover_excel.empty:
        for _, row in turnover_excel.iterrows():
            etf_code = row['ETF代码']
            signal_dates = turnover_dates.get(etf_code, [])
            
            turnover_details.append({
                'ETF代码': etf_code,
                '跟踪指数': row['跟踪指数'],
                '实际交易次数': row['交易次数'],
                '总信号次数': len(signal_dates),
                '成功交易': row['成功交易'],
                '胜率(%)': row['胜率(%)'],
                '择时累计收益率(%)': row['择时累计收益率(%)'],
                '指数基准收益率(%)': row['指数基准收益率(%)'],
                '超额收益率(%)': row['超额收益率(%)'],
                '信号日期列表': ', '.join(signal_dates[:10]) + ('...' if len(signal_dates) > 10 else ''),
                '信号日期总数': len(signal_dates)
            })
    
    turnover_df = pd.DataFrame(turnover_details)
    
    # 创建申赎信号详细表
    redemption_details = []
    if not redemption_excel.empty:
        for _, row in redemption_excel.iterrows():
            etf_code = row['ETF代码']
            signal_dates = redemption_dates.get(etf_code, [])
            
            redemption_details.append({
                'ETF代码': etf_code,
                '跟踪指数': row['跟踪指数'],
                '实际交易次数': row['交易次数'],
                '总信号次数': len(signal_dates),
                '成功交易': row['成功交易'],
                '胜率(%)': row['胜率(%)'],
                '择时累计收益率(%)': row['择时累计收益率(%)'],
                '指数基准收益率(%)': row['指数基准收益率(%)'],
                '超额收益率(%)': row['超额收益率(%)'],
                '信号日期列表': ', '.join(signal_dates[:10]) + ('...' if len(signal_dates) > 10 else ''),
                '信号日期总数': len(signal_dates)
            })
    
    redemption_df = pd.DataFrame(redemption_details)
    
    return turnover_df, redemption_df

def create_signal_date_sheets():
    """创建详细的信号日期工作表"""
    
    turnover_dates, redemption_dates = generate_detailed_signal_dates()
    
    # 90分位信号日期详细表
    turnover_date_details = []
    for etf_code, dates in turnover_dates.items():
        for i, date in enumerate(dates, 1):
            turnover_date_details.append({
                'ETF代码': etf_code,
                '信号序号': i,
                '信号日期': date,
                '预计结束日期': pd.to_datetime(date) + pd.Timedelta(days=21)  # 大约15个交易日
            })
    
    turnover_date_df = pd.DataFrame(turnover_date_details)
    
    # 申赎信号日期详细表
    redemption_date_details = []
    for etf_code, dates in redemption_dates.items():
        for i, date in enumerate(dates, 1):
            redemption_date_details.append({
                'ETF代码': etf_code,
                '信号序号': i,
                '信号日期': date,
                '预计结束日期': pd.to_datetime(date) + pd.Timedelta(days=21)  # 大约15个交易日
            })
    
    redemption_date_df = pd.DataFrame(redemption_date_details)
    
    return turnover_date_df, redemption_date_df

def main():
    """主函数"""
    print("=== ETF交易详细汇总表生成工具 ===")
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建详细汇总表
    turnover_summary, redemption_summary = create_detailed_summary()
    
    # 创建信号日期详细表
    turnover_dates, redemption_dates = create_signal_date_sheets()
    
    # 保存到Excel文件
    excel_path = 'ETF交易详细汇总表.xlsx'
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        
        # 90分位成交额信号汇总
        if not turnover_summary.empty:
            turnover_summary.to_excel(writer, sheet_name='90分位信号汇总', index=False)
        
        # 申赎信号汇总
        if not redemption_summary.empty:
            redemption_summary.to_excel(writer, sheet_name='申赎信号汇总', index=False)
        
        # 90分位信号日期明细
        if not turnover_dates.empty:
            turnover_dates.to_excel(writer, sheet_name='90分位信号日期明细', index=False)
        
        # 申赎信号日期明细
        if not redemption_dates.empty:
            redemption_dates.to_excel(writer, sheet_name='申赎信号日期明细', index=False)
    
    print(f"\nExcel文件已保存: {excel_path}")
    
    # 显示统计信息
    if not turnover_summary.empty:
        print(f"\n90分位成交额信号统计:")
        print(f"  参与ETF数量: {len(turnover_summary)}")
        print(f"  总实际交易次数: {turnover_summary['实际交易次数'].sum()}")
        print(f"  总信号次数: {turnover_summary['总信号次数'].sum()}")
        print(f"  平均胜率: {turnover_summary['胜率(%)'].mean():.2f}%")
        print(f"  平均超额收益率: {turnover_summary['超额收益率(%)'].mean():.4f}%")
    
    if not redemption_summary.empty:
        print(f"\n申赎信号统计:")
        print(f"  参与ETF数量: {len(redemption_summary)}")
        print(f"  总实际交易次数: {redemption_summary['实际交易次数'].sum()}")
        print(f"  总信号次数: {redemption_summary['总信号次数'].sum()}")
        print(f"  平均胜率: {redemption_summary['胜率(%)'].mean():.2f}%")
        print(f"  平均超额收益率: {redemption_summary['超额收益率(%)'].mean():.4f}%")

if __name__ == "__main__":
    main()
