# 跟踪相同指数ETF信号相关性分析报告

## 报告概述

**分析时间**: 2025-06-12 13:09:54  
**分析目的**: 分析跟踪相同指数的ETF发出90分位成交额信号日期的相关性  
**分析方法**: Jaccard相似度系数  
**信号条件**: pe_filter_passed=True 且 threshold=90

## 数据源说明

### ETF跟踪指数映射
- **数据源**: `市盈率信息/ETF跟踪指数.xlsx`
- **用途**: 确定哪些ETF跟踪相同指数

### 90分位成交额信号
- **数据源**: `回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv`
- **筛选条件**: pe_filter_passed=True 且 threshold=90
- **处理方式**: 每日每个ETF合并为1个信号

## 整体分析结果

### 基础统计
- **分析的ETF对数**: 19对
- **涉及指数数量**: 5个
- **平均Jaccard相似度**: 0.7480
- **最高Jaccard相似度**: 1.0000
- **最低Jaccard相似度**: 0.5691

### 相关性强度分类
- **强相关 (Jaccard≥0.5)**: 19对 (100.0%)
- **中等相关 (0.3≤Jaccard<0.5)**: 0对 (0.0%)
- **弱相关 (Jaccard<0.3)**: 0对 (0.0%)

## 详细分析结果

### 各指数下ETF信号相关性排名

#### 指数 16 (未知指数)

**统计信息**:
- ETF对数: 1
- 平均相似度: 0.6923
- 最高相似度: 0.6923
- 最低相似度: 0.6923

**ETF对详细相关性**:

| ETF1 | ETF2 | ETF1信号数 | ETF2信号数 | 重叠信号数 | Jaccard相似度 | ETF1覆盖率 | ETF2覆盖率 |
|------|------|-----------|-----------|-----------|-------------|-----------|-----------|
| 510050 | 510100 | 20 | 24 | 18 | 0.6923 | 0.9000 | 0.7500 |

#### 指数 300 (未知指数)

**统计信息**:
- ETF对数: 6
- 平均相似度: 0.7071
- 最高相似度: 0.7742
- 最低相似度: 0.6286

**ETF对详细相关性**:

| ETF1 | ETF2 | ETF1信号数 | ETF2信号数 | 重叠信号数 | Jaccard相似度 | ETF1覆盖率 | ETF2覆盖率 |
|------|------|-----------|-----------|-----------|-------------|-----------|-----------|
| 510310 | 510330 | 27 | 28 | 24 | 0.7742 | 0.8889 | 0.8571 |
| 510300 | 510330 | 24 | 28 | 22 | 0.7333 | 0.9167 | 0.7857 |
| 510300 | 159919 | 24 | 29 | 22 | 0.7097 | 0.9167 | 0.7586 |
| 510300 | 510310 | 24 | 27 | 21 | 0.7000 | 0.8750 | 0.7778 |
| 510310 | 159919 | 27 | 29 | 23 | 0.6970 | 0.8519 | 0.7931 |
| 510330 | 159919 | 28 | 29 | 22 | 0.6286 | 0.7857 | 0.7586 |

#### 指数 852 (未知指数)

**统计信息**:
- ETF对数: 6
- 平均相似度: 0.7805
- 最高相似度: 0.8372
- 最低相似度: 0.7000

**ETF对详细相关性**:

| ETF1 | ETF2 | ETF1信号数 | ETF2信号数 | 重叠信号数 | Jaccard相似度 | ETF1覆盖率 | ETF2覆盖率 |
|------|------|-----------|-----------|-----------|-------------|-----------|-----------|
| 512100 | 159845 | 42 | 37 | 36 | 0.8372 | 0.8571 | 0.9730 |
| 512100 | 560010 | 42 | 45 | 39 | 0.8125 | 0.9286 | 0.8667 |
| 512100 | 159629 | 42 | 48 | 40 | 0.8000 | 0.9524 | 0.8333 |
| 560010 | 159629 | 45 | 48 | 41 | 0.7885 | 0.9111 | 0.8542 |
| 560010 | 159845 | 45 | 37 | 35 | 0.7447 | 0.7778 | 0.9459 |
| 159629 | 159845 | 48 | 37 | 35 | 0.7000 | 0.7292 | 0.9459 |

#### 指数 905 (未知指数)

**统计信息**:
- ETF对数: 3
- 平均相似度: 0.9111
- 最高相似度: 1.0000
- 最低相似度: 0.8667

**ETF对详细相关性**:

| ETF1 | ETF2 | ETF1信号数 | ETF2信号数 | 重叠信号数 | Jaccard相似度 | ETF1覆盖率 | ETF2覆盖率 |
|------|------|-----------|-----------|-----------|-------------|-----------|-----------|
| 512500 | 159922 | 15 | 15 | 15 | 1.0000 | 1.0000 | 1.0000 |
| 510500 | 512500 | 13 | 15 | 13 | 0.8667 | 1.0000 | 0.8667 |
| 510500 | 159922 | 13 | 15 | 13 | 0.8667 | 1.0000 | 0.8667 |

#### 指数 399006 (未知指数)

**统计信息**:
- ETF对数: 3
- 平均相似度: 0.6200
- 最高相似度: 0.6863
- 最低相似度: 0.5691

**ETF对详细相关性**:

| ETF1 | ETF2 | ETF1信号数 | ETF2信号数 | 重叠信号数 | Jaccard相似度 | ETF1覆盖率 | ETF2覆盖率 |
|------|------|-----------|-----------|-----------|-------------|-----------|-----------|
| 159915 | 159977 | 79 | 93 | 70 | 0.6863 | 0.8861 | 0.7527 |
| 159952 | 159977 | 114 | 93 | 78 | 0.6047 | 0.6842 | 0.8387 |
| 159915 | 159952 | 79 | 114 | 70 | 0.5691 | 0.8861 | 0.6140 |

### 最佳相关性排行榜

| 排名 | 指数代码 | ETF1 | ETF2 | Jaccard相似度 | 重叠信号数 | 相关性评级 |
|------|---------|------|------|-------------|-----------|-----------|
| 1 | 905 | 512500 | 159922 | 1.0000 | 15 | 极强相关 |
| 2 | 905 | 510500 | 512500 | 0.8667 | 13 | 极强相关 |
| 3 | 905 | 510500 | 159922 | 0.8667 | 13 | 极强相关 |
| 4 | 852 | 512100 | 159845 | 0.8372 | 36 | 极强相关 |
| 5 | 852 | 512100 | 560010 | 0.8125 | 39 | 极强相关 |
| 6 | 852 | 512100 | 159629 | 0.8000 | 40 | 极强相关 |
| 7 | 852 | 560010 | 159629 | 0.7885 | 41 | 强相关 |
| 8 | 300 | 510310 | 510330 | 0.7742 | 24 | 强相关 |
| 9 | 852 | 560010 | 159845 | 0.7447 | 35 | 强相关 |
| 10 | 300 | 510300 | 510330 | 0.7333 | 22 | 强相关 |


## 分析结论

### 1. 整体相关性评估

**结论**: 跟踪相同指数的ETF信号相关性非常强

- **平均Jaccard相似度**: 0.7480 (强相关)
- **相关性分布**: 100%的ETF对呈现强相关，无中等或弱相关
- **完美相关**: 1对ETF完全同步发出信号
- **极强相关**: 6对ETF相似度≥0.8

### 2. 完美相关ETF对分析


#### ETF 512500 vs ETF 159922 (指数905)
- **Jaccard相似度**: 1.0000 (完美相关)
- **重叠信号数**: 15个
- **信号同步率**: 100%


### 3. 各指数相关性特征分析


#### 指数 16
- **相关性强度**: 中等相关 (平均0.6923)
- **ETF对数**: 1对
- **相似度范围**: 0.6923 - 0.6923

#### 指数 300
- **相关性强度**: 强相关 (平均0.7071)
- **ETF对数**: 6对
- **相似度范围**: 0.6286 - 0.7742

#### 指数 852
- **相关性强度**: 强相关 (平均0.7805)
- **ETF对数**: 6对
- **相似度范围**: 0.7000 - 0.8372

#### 指数 905
- **相关性强度**: 极强相关 (平均0.9111)
- **ETF对数**: 3对
- **相似度范围**: 0.8667 - 1.0000

#### 指数 399006
- **相关性强度**: 中等相关 (平均0.6200)
- **ETF对数**: 3对
- **相似度范围**: 0.5691 - 0.6863


### 4. 关键发现

1. **高度同步性**: 跟踪相同指数的ETF信号发出时间高度同步
2. **指数驱动**: 信号相关性主要由底层指数的市场表现驱动
3. **策略一致性**: 相同指数的ETF采用相似的量化策略逻辑
4. **风险集中**: 高相关性意味着系统性风险较高

### 5. 投资策略建议

#### 风险管理建议
1. **避免重复配置**: 不建议同时持有跟踪相同指数的多个ETF
2. **分散化投资**: 选择跟踪不同指数的ETF以降低相关性
3. **信号确认**: 可利用高相关性进行信号确认

#### 策略优化建议
1. **指数选择**: 优先选择每个指数下表现最佳的ETF
2. **流动性考虑**: 在相关性相似的情况下，选择流动性更好的ETF
3. **成本控制**: 考虑管理费用等成本因素

### 6. 技术分析总结

#### 相关性水平分布
- **完美相关 (Jaccard=1.0)**: 1对
- **极强相关 (Jaccard≥0.8)**: 6对
- **强相关 (Jaccard≥0.6)**: 18对
- **中等相关 (Jaccard≥0.4)**: 19对

#### 最具代表性的指数
1. **指数905**: 平均相似度0.9111，包含完美相关ETF对
2. **指数852**: 平均相似度0.7805，ETF对数最多
3. **指数300**: 平均相似度0.7071，覆盖4个ETF

---

## 技术说明

### Jaccard相似度计算公式
```
Jaccard相似度 = |A ∩ B| / |A ∪ B|
```
其中：
- A: ETF1的信号日期集合
- B: ETF2的信号日期集合
- |A ∩ B|: 重叠信号日期数量
- |A ∪ B|: 总信号日期数量

### 相关性强度分类标准
- **完美相关**: Jaccard = 1.0
- **极强相关**: Jaccard ≥ 0.8
- **强相关**: Jaccard ≥ 0.6
- **中等相关**: 0.4 ≤ Jaccard < 0.6
- **弱相关**: Jaccard < 0.4

---
*报告生成时间: 2025-06-12 13:09:54*
