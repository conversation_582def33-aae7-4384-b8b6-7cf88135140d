#!/usr/bin/env python3
"""
正确的ETF择时策略超额收益计算
严格按照用户要求的逻辑进行计算

计算逻辑：
1. 筛选各ETF"pe_filter_passed"=TRUE且"threshold"=90的买入信号
2. 以下一交易日开盘价买入，持有15个交易日后以收盘价卖出
3. 若ETF日内发出多个买入信号，则当日整体记为1个买信号
4. 在持有期间若出现新的买入信号，则忽略该买入信号
5. 超额收益=ETF择时累计收益率-跟踪指数累计涨幅
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """加载所有必要的数据"""
    print("=== 加载数据 ===")
    
    # 1. 加载ETF跟踪指数映射表
    etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
    print(f"ETF映射关系: {len(etf_mapping)} 条")
    print(f"ETF映射列名: {etf_mapping.columns.tolist()}")
    
    # 2. 加载指数行情数据
    index_data = pd.read_excel('指数行情序列.xlsx')
    index_data['时间'] = pd.to_datetime(index_data['时间'])
    # 处理指数代码格式
    if '代码' in index_data.columns:
        index_data['代码'] = index_data['代码'].astype(str).str.replace('.SH', '').str.replace('.SZ', '').str.zfill(6)
    print(f"指数行情数据: {len(index_data)} 条")
    print(f"指数行情列名: {index_data.columns.tolist()}")
    
    # 3. 加载原始信号数据
    signals_path = '回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv'
    signals_df = pd.read_csv(signals_path)
    signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
    signals_df['date'] = signals_df['datetime'].dt.date
    print(f"原始信号数据: {len(signals_df)} 条")
    print(f"信号数据列名: {signals_df.columns.tolist()}")
    
    return etf_mapping, index_data, signals_df

def validate_calculation_logic():
    """验证现有计算逻辑是否正确"""
    print("\n=== 验证计算逻辑 ===")
    
    # 检查现有脚本的问题
    existing_script_path = '超额收益_分钟级成交额_90分位信号/etf_excess_return_analysis.py'
    
    with open(existing_script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    issues = []
    
    # 检查信号筛选条件
    if 'threshold >= 80' in content:
        issues.append("❌ 信号筛选条件错误：使用了 threshold >= 80，应该是 threshold == 90")
    
    if 'threshold == 90' in content:
        issues.append("✓ 信号筛选条件正确：使用了 threshold == 90")
    
    # 检查PE筛选条件
    if 'pe_filter_passed == True' in content:
        issues.append("✓ PE筛选条件正确")
    
    if issues:
        print("发现的问题:")
        for issue in issues:
            print(f"  {issue}")
    
    return len([i for i in issues if i.startswith('❌')]) == 0

def generate_correct_buy_signals(signals_df, start_date, end_date):
    """
    生成正确的买入信号
    严格按照用户要求：pe_filter_passed=TRUE且threshold=90
    """
    print(f"\n=== 生成买入信号 ===")
    print(f"分析时间范围: {start_date} 到 {end_date}")
    
    # 筛选条件：严格按照用户要求
    valid_signals = signals_df[
        (signals_df['pe_filter_passed'] == True) &
        (signals_df['threshold'] == 90) &  # 注意：这里必须是90，不是>=80
        (signals_df['date'] >= start_date) &
        (signals_df['date'] <= end_date)
    ].copy()
    
    print(f"原始信号总数: {len(signals_df)}")
    print(f"PE筛选后信号数: {len(signals_df[signals_df['pe_filter_passed'] == True])}")
    print(f"90分位信号数: {len(signals_df[signals_df['threshold'] == 90])}")
    print(f"时间范围内有效信号数: {len(valid_signals)}")
    
    # 每日每个ETF只保留一个信号（取最早的信号）
    daily_signals = valid_signals.groupby(['etf_code', 'date']).first().reset_index()
    daily_signals = daily_signals.sort_values(['etf_code', 'date'])
    
    print(f"每日合并后信号数: {len(daily_signals)}")
    print(f"涉及ETF数量: {daily_signals['etf_code'].nunique()}")
    
    # 显示各ETF的信号数量
    etf_signal_counts = daily_signals['etf_code'].value_counts().sort_index()
    print(f"\n各ETF信号数量:")
    for etf_code, count in etf_signal_counts.items():
        print(f"  ETF {etf_code}: {count} 个信号")
    
    return daily_signals

def get_trading_days(index_data, start_date, end_date):
    """获取交易日列表"""
    trading_days = index_data[
        (index_data['时间'].dt.date >= start_date) & 
        (index_data['时间'].dt.date <= end_date)
    ]['时间'].dt.date.unique()
    return sorted(trading_days)

def calculate_etf_timing_returns_correct(etf_code, signals, index_data, etf_mapping, trading_days):
    """
    正确计算单个ETF的择时收益
    严格按照用户要求的逻辑
    """
    # 获取ETF对应的指数代码
    etf_row = etf_mapping[etf_mapping['ETF代码'] == etf_code]
    if etf_row.empty:
        print(f"警告: ETF {etf_code} 未找到对应的指数")
        return None
    
    index_code = str(etf_row.iloc[0]['跟踪指数代码']).zfill(6)
    
    # 获取指数行情数据
    index_prices = index_data[index_data['代码'] == index_code].copy()
    
    # 特殊处理某些指数格式问题
    if index_prices.empty and index_code == '746059':
        index_prices = index_data[index_data['代码'] == '746059.MSI'].copy()
        if not index_prices.empty:
            print(f"注意: 指数 {index_code} 使用 746059.MSI 数据")
    
    if index_prices.empty:
        print(f"警告: 指数 {index_code} 未找到行情数据")
        return None
    
    index_prices = index_prices.sort_values('时间')
    index_prices['date'] = index_prices['时间'].dt.date
    
    # 获取该ETF的信号
    etf_signals = signals[signals['etf_code'] == etf_code].copy()
    etf_signals = etf_signals.sort_values('date')
    
    if etf_signals.empty:
        return {
            'etf_code': etf_code,
            'index_code': index_code,
            'total_trades': 0,
            'successful_trades': 0,
            'total_return': 0.0,
            'trades': []
        }
    
    trades = []
    holding_end_date = None
    
    print(f"  处理ETF {etf_code} 的 {len(etf_signals)} 个信号...")
    
    for _, signal in etf_signals.iterrows():
        signal_date = signal['date']
        
        # 如果还在持有期内，跳过新信号
        if holding_end_date and signal_date <= holding_end_date:
            print(f"    跳过信号 {signal_date}（仍在持有期内，持有至 {holding_end_date}）")
            continue
        
        # 找到信号日期在交易日列表中的位置
        try:
            signal_date_idx = trading_days.index(signal_date)
        except ValueError:
            print(f"    跳过信号 {signal_date}（不是交易日）")
            continue
        
        # 下一个交易日作为买入日
        if signal_date_idx + 1 >= len(trading_days):
            print(f"    跳过信号 {signal_date}（无下一交易日）")
            continue
        
        buy_date = trading_days[signal_date_idx + 1]
        
        # 15个交易日后作为卖出日
        if signal_date_idx + 16 >= len(trading_days):
            print(f"    跳过信号 {signal_date}（持有期超出数据范围）")
            continue
        
        sell_date = trading_days[signal_date_idx + 16]
        
        # 获取买入日开盘价和卖出日收盘价
        buy_price_data = index_prices[index_prices['date'] == buy_date]
        sell_price_data = index_prices[index_prices['date'] == sell_date]
        
        if buy_price_data.empty or sell_price_data.empty:
            print(f"    跳过信号 {signal_date}（缺少价格数据）")
            continue
        
        buy_price = buy_price_data.iloc[0]['开盘价(元)']
        sell_price = sell_price_data.iloc[0]['收盘价(元)']
        
        if pd.isna(buy_price) or pd.isna(sell_price) or buy_price <= 0:
            print(f"    跳过信号 {signal_date}（价格数据无效）")
            continue
        
        # 计算收益率
        return_rate = (sell_price - buy_price) / buy_price
        
        trades.append({
            'signal_date': signal_date,
            'buy_date': buy_date,
            'sell_date': sell_date,
            'buy_price': buy_price,
            'sell_price': sell_price,
            'return': return_rate
        })
        
        print(f"    执行交易: {signal_date} -> {buy_date} 买入 {buy_price:.4f}, {sell_date} 卖出 {sell_price:.4f}, 收益率 {return_rate*100:.4f}%")
        
        # 更新持有结束日期
        holding_end_date = sell_date
    
    # 计算总收益率（复利）
    total_return = 1.0
    for trade in trades:
        total_return *= (1 + trade['return'])
    total_return -= 1
    
    successful_trades = len([t for t in trades if t['return'] > 0])
    
    return {
        'etf_code': etf_code,
        'index_code': index_code,
        'total_trades': len(trades),
        'successful_trades': successful_trades,
        'win_rate': successful_trades / len(trades) * 100 if trades else 0,
        'total_return': total_return,
        'trades': trades
    }

def calculate_index_benchmark_return(index_code, index_data, start_date, end_date):
    """
    计算指数基准收益率
    统计区间最后一个交易日收盘价-统计区间第一个交易日开盘价
    """
    index_prices = index_data[index_data['代码'] == index_code].copy()
    
    # 特殊处理某些指数格式问题
    if index_prices.empty and index_code == '746059':
        index_prices = index_data[index_data['代码'] == '746059.MSI'].copy()
    
    if index_prices.empty:
        return 0.0
    
    index_prices = index_prices.sort_values('时间')
    
    # 获取起始日期的开盘价和结束日期的收盘价
    start_data = index_prices[index_prices['时间'].dt.date >= start_date]
    end_data = index_prices[index_prices['时间'].dt.date <= end_date]
    
    if start_data.empty or end_data.empty:
        return 0.0
    
    start_price = start_data.iloc[0]['开盘价(元)']
    end_price = end_data.iloc[-1]['收盘价(元)']
    
    if pd.isna(start_price) or pd.isna(end_price) or start_price <= 0:
        return 0.0
    
    return (end_price - start_price) / start_price

def main():
    """主函数"""
    print("=== 正确的ETF择时策略超额收益计算 ===")
    
    # 分析时间范围
    start_date = pd.to_datetime('2024-04-30').date()
    end_date = pd.to_datetime('2025-04-30').date()
    
    # 验证现有计算逻辑
    is_logic_correct = validate_calculation_logic()
    if not is_logic_correct:
        print("\n⚠️  发现现有计算逻辑存在问题，将使用正确的逻辑重新计算")
    
    # 加载数据
    etf_mapping, index_data, signals_df = load_data()
    
    # 获取交易日
    trading_days = get_trading_days(index_data, start_date, end_date)
    print(f"\n交易日数量: {len(trading_days)}")
    
    # 生成正确的买入信号
    buy_signals = generate_correct_buy_signals(signals_df, start_date, end_date)
    
    # 计算每个ETF的择时收益
    results = []
    etf_codes = sorted(buy_signals['etf_code'].unique())
    
    print(f"\n=== 开始计算 {len(etf_codes)} 个ETF的择时收益 ===")
    
    for etf_code in etf_codes:
        print(f"\n正在处理 ETF {etf_code}...")
        
        result = calculate_etf_timing_returns_correct(
            etf_code, buy_signals, index_data, etf_mapping, trading_days
        )
        
        if result:
            # 计算对应指数的基准收益
            benchmark_return = calculate_index_benchmark_return(
                result['index_code'], index_data, start_date, end_date
            )
            
            result['benchmark_return'] = benchmark_return
            result['excess_return'] = result['total_return'] - benchmark_return
            
            print(f"  ETF {etf_code} 结果:")
            print(f"    交易次数: {result['total_trades']}")
            print(f"    成功交易: {result['successful_trades']}")
            print(f"    胜率: {result['win_rate']:.2f}%")
            print(f"    择时收益率: {result['total_return']*100:.4f}%")
            print(f"    基准收益率: {result['benchmark_return']*100:.4f}%")
            print(f"    超额收益率: {result['excess_return']*100:.4f}%")
            
            results.append(result)
    
    # 保存结果
    save_corrected_results(results, start_date, end_date)
    
    print(f"\n=== 计算完成 ===")
    print(f"结果已保存到 超额收益_分钟级成交额_90分位信号/ 文件夹中")

def save_corrected_results(results, start_date, end_date):
    """保存修正后的结果"""
    
    # 创建汇总表
    summary_data = []
    for result in results:
        summary_data.append({
            'ETF代码': result['etf_code'],
            '跟踪指数': result['index_code'],
            '交易次数': result['total_trades'],
            '成功交易': result['successful_trades'],
            '胜率(%)': round(result['win_rate'], 2),
            '择时累计收益率(%)': round(result['total_return'] * 100, 4),
            '指数基准收益率(%)': round(result['benchmark_return'] * 100, 4),
            '超额收益率(%)': round(result['excess_return'] * 100, 4)
        })
    
    summary_df = pd.DataFrame(summary_data)
    
    # 计算整体统计
    total_trades = summary_df['交易次数'].sum()
    total_successful = summary_df['成功交易'].sum()
    avg_win_rate = total_successful / total_trades * 100 if total_trades > 0 else 0
    
    # 简单平均收益率
    avg_timing_return = summary_df['择时累计收益率(%)'].mean()
    avg_benchmark_return = summary_df['指数基准收益率(%)'].mean()
    avg_excess_return = summary_df['超额收益率(%)'].mean()
    
    # 保存到原文件夹
    output_dir = '超额收益_分钟级成交额_90分位信号'
    excel_path = f'{output_dir}/ETF择时策略超额收益分析_修正版.xlsx'
    
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        summary_df.to_excel(writer, sheet_name='超额收益汇总', index=False)
        
        # 添加统计摘要
        stats_data = [
            ['分析时间范围', f'{start_date} 到 {end_date}'],
            ['参与ETF数量', len(results)],
            ['总交易次数', total_trades],
            ['总成功交易', total_successful],
            ['平均胜率(%)', round(avg_win_rate, 2)],
            ['平均择时收益率(%)', round(avg_timing_return, 4)],
            ['平均基准收益率(%)', round(avg_benchmark_return, 4)],
            ['平均超额收益率(%)', round(avg_excess_return, 4)]
        ]
        stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
        stats_df.to_excel(writer, sheet_name='统计摘要', index=False)
    
    print(f"\n修正版Excel报告已保存: {excel_path}")
    
    # 显示关键统计
    print(f"\n=== 修正后的关键统计 ===")
    print(f"参与ETF数量: {len(results)}")
    print(f"总交易次数: {total_trades}")
    print(f"平均胜率: {avg_win_rate:.2f}%")
    print(f"平均超额收益率: {avg_excess_return:.4f}%")

if __name__ == "__main__":
    main()
