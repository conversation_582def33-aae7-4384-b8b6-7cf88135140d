# 年化收益率修正报告

## 修正说明

### 原始问题
1. 同一天多个信号被重复计算
2. 年化收益率计算方法不合理，导致数值过高

### 修正方法
1. **信号去重**: 每日每个ETF每个策略组合最多保留一个信号
2. **年化计算优化**: 
   - 低频交易(≤20次/年): 使用复利公式
   - 高频交易(>20次/年): 基于持有期年化
   - 设置合理的上限，避免极端值

## 修正后的主要结果

| 阈值 | 持有周期 | 信号数 | 胜率(%) | 平均收益率(%) | 年化收益率(%) |
|------|----------|--------|---------|---------------|---------------|
| 大于80分位 | next_10day_close | 1065 | 72.11 | 5.8086 | 94.43 |
| 大于80分位 | next_30day_close | 897 | 61.87 | 8.9357 | 137.97 |
| 大于80分位 | next_5day_close | 1125 | 66.76 | 3.2443 | 47.57 |
| 大于80分位 | next_day_close | 1163 | 57.09 | 0.9537 | 12.62 |
| 大于90分位 | next_10day_close | 845 | 74.79 | 6.5893 | 108.70 |
| 大于90分位 | next_30day_close | 705 | 64.54 | 10.4436 | 166.89 |
| 大于90分位 | next_5day_close | 888 | 68.24 | 3.9838 | 59.44 |
| 大于90分位 | next_day_close | 910 | 58.13 | 1.1806 | 15.49 |
| 大于95分位 | next_10day_close | 607 | 75.78 | 7.1255 | 107.76 |
| 大于95分位 | next_30day_close | 502 | 65.14 | 11.5708 | 172.06 |
| 大于95分位 | next_5day_close | 634 | 68.45 | 4.8106 | 67.95 |
| 大于95分位 | next_day_close | 647 | 60.28 | 1.4731 | 18.08 |


## 关键改进

1. **信号数量**: 从303,396个压缩到36,552个，避免重复计算
2. **年化收益率**: 使用更合理的计算方法，结果更贴近实际
3. **数据质量**: 提高了策略评估的准确性

## 计算逻辑

```python
# 交易频率计算
trades_per_etf_per_year = (unique_signal_days / etf_count) * (252 / total_days)

# 年化收益率计算
if trades_per_etf_per_year <= 20:
    # 低频交易：复利计算
    annual_return = ((1 + avg_return/100) ** trades_per_etf_per_year) - 1
else:
    # 高频交易：基于持有期年化
    periods_per_year = 252 / holding_days
    annual_return = ((1 + avg_return/100) ** periods_per_year) - 1
```

---
*报告生成时间: 2025-06-09 19:53:33*
