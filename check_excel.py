#!/usr/bin/env python3
import pandas as pd

try:
    df = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
    print('ETF代码列表:')
    etf_codes = sorted(df['ETF代码'].tolist())
    for code in etf_codes:
        print(f"  {code}")
    
    print(f'\n总共有 {len(df)} 个ETF')
    print('588050是否存在:', '588050' in df['ETF代码'].values)
    print('588080是否存在:', '588080' in df['ETF代码'].values)
    
    if '588050' in df['ETF代码'].values:
        print('\n588050数据:')
        row = df[df['ETF代码'] == '588050'].iloc[0]
        for col in df.columns:
            print(f"  {col}: {row[col]}")
    
    if '588080' in df['ETF代码'].values:
        print('\n588080数据:')
        row = df[df['ETF代码'] == '588080'].iloc[0]
        for col in df.columns:
            print(f"  {col}: {row[col]}")
            
except Exception as e:
    print(f'错误: {e}')
    import traceback
    traceback.print_exc()
