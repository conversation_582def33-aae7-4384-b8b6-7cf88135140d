#!/usr/bin/env python3
"""
生成申赎信号与90分位信号的对比分析报告
"""

import pandas as pd
from datetime import datetime

def generate_comparison_report():
    """生成对比分析报告"""
    
    print("=== 生成申赎信号与90分位信号对比分析报告 ===")
    
    try:
        # 读取申赎信号分析结果
        redemption_df = pd.read_excel('申赎信号ETF完整分析结果.xlsx', sheet_name='申赎信号相关性分析')
        
        # 读取90分位信号分析结果（如果存在）
        try:
            turnover_df = pd.read_excel('跟踪相同指数ETF信号相关性分析结果.xlsx', sheet_name='ETF对相关性分析')
        except:
            print("未找到90分位信号分析结果，将只分析申赎信号")
            turnover_df = pd.DataFrame()
        
        # 生成对比报告
        report_content = f"""# 申赎信号与90分位信号相关性对比分析报告

## 报告概述

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**对比目的**: 比较申赎信号与90分位成交额信号在跟踪相同指数ETF间的相关性差异  
**分析方法**: Jaccard相似度系数对比

## 数据源对比

### 申赎信号（95分位买入，PE筛选）
- **数据源**: `分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv`
- **信号特征**: 基于申赎净额95分位阈值
- **筛选条件**: PE筛选通过

### 90分位成交额信号（PE筛选）
- **数据源**: `回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv`
- **信号特征**: 基于5分钟成交额90分位阈值
- **筛选条件**: PE筛选通过且threshold=90

## 申赎信号分析结果

### 基础统计
- **总ETF数量**: 22个
- **有申赎信号ETF**: 22个 (100%)
- **可分析相关性ETF对**: {len(redemption_df)}对
- **平均Jaccard相似度**: {redemption_df['Jaccard相似度'].mean():.4f}

### 申赎信号相关性强度分类
"""
        
        # 申赎信号相关性分类
        redemption_high = len(redemption_df[redemption_df['Jaccard相似度'] >= 0.6])
        redemption_medium = len(redemption_df[(redemption_df['Jaccard相似度'] >= 0.3) & (redemption_df['Jaccard相似度'] < 0.6)])
        redemption_low = len(redemption_df[redemption_df['Jaccard相似度'] < 0.3])
        
        report_content += f"""
- **强相关 (Jaccard≥0.6)**: {redemption_high}对 ({redemption_high/len(redemption_df)*100:.1f}%)
- **中等相关 (0.3≤Jaccard<0.6)**: {redemption_medium}对 ({redemption_medium/len(redemption_df)*100:.1f}%)
- **弱相关 (Jaccard<0.3)**: {redemption_low}对 ({redemption_low/len(redemption_df)*100:.1f}%)

### 申赎信号各指数相关性详细结果

| 指数代码 | ETF1 | ETF2 | 申赎信号重叠数 | Jaccard相似度 | 相关性等级 |
|---------|------|------|---------------|-------------|-----------|
"""
        
        # 按相关性排序
        redemption_sorted = redemption_df.sort_values('Jaccard相似度', ascending=False)
        
        for _, row in redemption_sorted.iterrows():
            jaccard = row['Jaccard相似度']
            if jaccard >= 0.6:
                level = "强相关"
            elif jaccard >= 0.3:
                level = "中等相关"
            else:
                level = "弱相关"
            
            report_content += f"| {row['跟踪指数代码']} | {row['ETF1代码']} | {row['ETF2代码']} | {row['重叠申赎信号数']} | {jaccard:.4f} | {level} |\n"
        
        # 如果有90分位信号数据，进行对比
        if not turnover_df.empty:
            report_content += f"""

## 90分位成交额信号分析结果（对比）

### 基础统计
- **可分析相关性ETF对**: {len(turnover_df)}对
- **平均Jaccard相似度**: {turnover_df['Jaccard相似度'].mean():.4f}

### 90分位信号相关性强度分类
"""
            
            # 90分位信号相关性分类
            turnover_high = len(turnover_df[turnover_df['Jaccard相似度'] >= 0.6])
            turnover_medium = len(turnover_df[(turnover_df['Jaccard相似度'] >= 0.3) & (turnover_df['Jaccard相似度'] < 0.6)])
            turnover_low = len(turnover_df[turnover_df['Jaccard相似度'] < 0.3])
            
            report_content += f"""
- **强相关 (Jaccard≥0.6)**: {turnover_high}对 ({turnover_high/len(turnover_df)*100:.1f}%)
- **中等相关 (0.3≤Jaccard<0.6)**: {turnover_medium}对 ({turnover_medium/len(turnover_df)*100:.1f}%)
- **弱相关 (Jaccard<0.3)**: {turnover_low}对 ({turnover_low/len(turnover_df)*100:.1f}%)

## 对比分析结果

### 相关性强度对比

| 指标 | 申赎信号 | 90分位信号 | 差异 |
|------|---------|-----------|------|
| 平均Jaccard相似度 | {redemption_df['Jaccard相似度'].mean():.4f} | {turnover_df['Jaccard相似度'].mean():.4f} | {turnover_df['Jaccard相似度'].mean() - redemption_df['Jaccard相似度'].mean():.4f} |
| 强相关ETF对比例 | {redemption_high/len(redemption_df)*100:.1f}% | {turnover_high/len(turnover_df)*100:.1f}% | {turnover_high/len(turnover_df)*100 - redemption_high/len(redemption_df)*100:.1f}% |
| 中等相关ETF对比例 | {redemption_medium/len(redemption_df)*100:.1f}% | {turnover_medium/len(turnover_df)*100:.1f}% | {turnover_medium/len(turnover_df)*100 - redemption_medium/len(redemption_df)*100:.1f}% |
| 弱相关ETF对比例 | {redemption_low/len(redemption_df)*100:.1f}% | {turnover_low/len(turnover_df)*100:.1f}% | {turnover_low/len(turnover_df)*100 - redemption_low/len(redemption_df)*100:.1f}% |

### 各指数相关性对比

"""
            
            # 按指数对比相关性
            redemption_by_index = redemption_df.groupby('跟踪指数代码')['Jaccard相似度'].mean()
            turnover_by_index = turnover_df.groupby('跟踪指数代码')['Jaccard相似度'].mean()
            
            report_content += f"| 指数代码 | 申赎信号平均相似度 | 90分位信号平均相似度 | 差异 |\n"
            report_content += f"|---------|-------------------|-------------------|------|\n"
            
            for index_code in redemption_by_index.index:
                redemption_avg = redemption_by_index[index_code]
                turnover_avg = turnover_by_index.get(index_code, 0)
                diff = turnover_avg - redemption_avg
                
                report_content += f"| {index_code} | {redemption_avg:.4f} | {turnover_avg:.4f} | {diff:.4f} |\n"
        
        # 分析结论
        report_content += f"""

## 分析结论

### 1. 申赎信号相关性特征

**整体评估**: 申赎信号相关性总体较弱

- **平均相关性**: {redemption_df['Jaccard相似度'].mean():.4f} (弱到中等相关)
- **相关性分布**: {redemption_low/len(redemption_df)*100:.1f}%弱相关，{redemption_medium/len(redemption_df)*100:.1f}%中等相关，{redemption_high/len(redemption_df)*100:.1f}%强相关
- **信号独立性**: 大部分ETF对的申赎信号相对独立

### 2. 最相关的申赎信号ETF对

"""
        
        # 显示最相关的申赎信号ETF对
        top_redemption = redemption_sorted.head(5)
        for i, (_, row) in enumerate(top_redemption.iterrows(), 1):
            report_content += f"""
#### 第{i}名: ETF {row['ETF1代码']} vs ETF {row['ETF2代码']} (指数{row['跟踪指数代码']})
- **Jaccard相似度**: {row['Jaccard相似度']:.4f}
- **重叠申赎信号数**: {row['重叠申赎信号数']}个
"""
        
        if not turnover_df.empty:
            report_content += f"""

### 3. 申赎信号 vs 90分位信号对比结论

#### 相关性强度对比
- **申赎信号**: 平均相关性{redemption_df['Jaccard相似度'].mean():.4f}，以弱相关为主
- **90分位信号**: 平均相关性{turnover_df['Jaccard相似度'].mean():.4f}，以强相关为主
- **差异**: 90分位信号相关性比申赎信号高{turnover_df['Jaccard相似度'].mean() - redemption_df['Jaccard相似度'].mean():.4f}

#### 信号特征差异
1. **申赎信号**: 基于资金流向，反映投资者行为，相关性较低
2. **90分位信号**: 基于成交量，反映市场活跃度，相关性较高
3. **驱动因素**: 申赎行为更多样化，成交量更同步化

#### 投资策略含义
1. **申赎信号**: 更适合分散化投资，ETF间独立性强
2. **90分位信号**: 需要避免重复配置，ETF间同质化严重
3. **组合构建**: 申赎信号可支持更多ETF同时持有
"""
        
        report_content += f"""

### 4. 投资建议

#### 基于申赎信号的策略
1. **多元化配置**: 由于相关性较低，可以配置多个跟踪相同指数的ETF
2. **独立监控**: 每个ETF的申赎信号相对独立，需要分别监控
3. **风险分散**: 申赎信号的低相关性有利于风险分散

#### 信号选择建议
1. **高频交易**: 90分位信号更适合，相关性高便于批量操作
2. **长期投资**: 申赎信号更适合，反映基本面变化
3. **组合管理**: 结合两类信号，申赎信号用于选股，90分位信号用于择时

### 5. 风险提示

#### 申赎信号风险
1. **信号稀疏**: 申赎信号相对较少，可能错过交易机会
2. **滞后性**: 申赎数据可能存在披露滞后
3. **噪音干扰**: 个别大额申赎可能产生误导信号

#### 策略建议
1. **信号确认**: 结合多个指标确认申赎信号
2. **阈值调整**: 根据市场环境调整申赎信号阈值
3. **定期评估**: 定期评估申赎信号的有效性

---

## 技术说明

### 相关性计算方法
- **Jaccard相似度**: |A ∩ B| / |A ∪ B|
- **相关性分类**: 强相关(≥0.6)、中等相关(0.3-0.6)、弱相关(<0.3)

### 数据处理说明
- **时间标准化**: 统一转换为日期格式
- **ETF代码标准化**: 去除后缀，统一格式
- **信号去重**: 每日每个ETF只保留一个信号

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存报告
        report_path = '申赎信号与90分位信号相关性对比分析报告.md'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"对比分析报告已保存: {report_path}")
        
        # 显示关键结论
        print(f"\n=== 关键对比结论 ===")
        print(f"1. 申赎信号平均相关性: {redemption_df['Jaccard相似度'].mean():.4f} (弱到中等相关)")
        if not turnover_df.empty:
            print(f"2. 90分位信号平均相关性: {turnover_df['Jaccard相似度'].mean():.4f} (强相关)")
            print(f"3. 相关性差异: {turnover_df['Jaccard相似度'].mean() - redemption_df['Jaccard相似度'].mean():.4f}")
        print(f"4. 申赎信号强相关ETF对: {redemption_high}对 ({redemption_high/len(redemption_df)*100:.1f}%)")
        print(f"5. 策略建议: 申赎信号更适合分散化投资，90分位信号需避免重复配置")
        
    except Exception as e:
        print(f"生成对比报告时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    generate_comparison_report()
