#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证显示脚本
显示ETF净申购数据的计算过程和关键数据
"""

import pandas as pd
import numpy as np

def display_etf_calculation_sample():
    """显示ETF计算过程的示例"""
    
    # 读取一个ETF的详细数据作为示例
    etf_file = "分析结果_20240430_20250430/510050.XSHG_详细数据.csv"
    df = pd.read_csv(etf_file)
    df['date'] = pd.to_datetime(df['date'])
    df = df.set_index('date')
    
    print("=" * 80)
    print("ETF 510050.XSHG 计算过程验证")
    print("=" * 80)
    
    # 显示前20行数据
    print("\n1. 基础数据和计算结果（前20行）:")
    print("-" * 80)
    display_df = df.head(20).copy()
    
    # 格式化数值显示
    for col in ['shares', 'shares_change', 'net_purchase_amount', 'net_purchase_5d']:
        if col in display_df.columns:
            display_df[col] = display_df[col].apply(lambda x: f"{x:,.0f}" if pd.notna(x) else "")
    
    for col in ['nav']:
        if col in display_df.columns:
            display_df[col] = display_df[col].apply(lambda x: f"{x:.4f}" if pd.notna(x) else "")
    
    for col in ['net_purchase_percentile', 'net_purchase_5d_percentile']:
        if col in display_df.columns:
            display_df[col] = display_df[col].apply(lambda x: f"{x:.1f}" if pd.notna(x) else "")
    
    print(display_df.to_string())
    
    # 验证计算公式
    print("\n\n2. 计算公式验证:")
    print("-" * 80)
    
    # 选择几个有数据的日期进行验证
    sample_dates = df.dropna(subset=['shares_change', 'nav']).head(5).index
    
    for date in sample_dates:
        row = df.loc[date]
        shares_change = row['shares_change']
        nav = row['nav']
        net_purchase = row['net_purchase_amount']
        
        calculated_net_purchase = shares_change * nav
        
        print(f"\n日期: {date.strftime('%Y-%m-%d')}")
        print(f"  份额变化: {shares_change:,.0f}")
        print(f"  单位净值: {nav:.4f}")
        print(f"  计算的净申购金额: {shares_change:,.0f} × {nav:.4f} = {calculated_net_purchase:,.2f}")
        print(f"  记录的净申购金额: {net_purchase:,.2f}")
        print(f"  差异: {abs(calculated_net_purchase - net_purchase):.2f}")

def display_signal_summary():
    """显示信号汇总统计"""
    
    # 读取信号数据汇总表
    signal_file = "分析结果_20240430_20250430/信号数据汇总表.csv"
    df = pd.read_csv(signal_file)
    
    print("\n\n3. 信号统计汇总:")
    print("-" * 80)
    
    # 按信号类型统计
    signal_stats = {
        '信号1_买入': df['信号1_买入'].sum(),
        '信号1_卖出': df['信号1_卖出'].sum(),
        '信号2_买入': df['信号2_买入'].sum(),
        '信号2_卖出': df['信号2_卖出'].sum()
    }
    
    print("各类信号总数:")
    for signal_type, count in signal_stats.items():
        print(f"  {signal_type}: {count:,} 次")
    
    # 按ETF统计信号数量
    print("\n按ETF统计信号数量:")
    etf_signals = df.groupby('ETF代码')[['信号1_买入', '信号1_卖出', '信号2_买入', '信号2_卖出']].sum()
    etf_signals['总信号数'] = etf_signals.sum(axis=1)
    etf_signals = etf_signals.sort_values('总信号数', ascending=False)
    
    print(etf_signals.to_string())

def display_percentile_examples():
    """显示分位数计算示例"""
    
    print("\n\n4. 分位数计算示例:")
    print("-" * 80)
    
    # 读取一个ETF的详细数据
    etf_file = "分析结果_20240430_20250430/510050.XSHG_详细数据.csv"
    df = pd.read_csv(etf_file)
    df['date'] = pd.to_datetime(df['date'])
    df = df.set_index('date')
    
    # 找到第一个有分位数数据的日期
    first_percentile_date = df.dropna(subset=['net_purchase_percentile']).index[0]
    
    print(f"以 {first_percentile_date.strftime('%Y-%m-%d')} 为例:")
    
    # 获取该日期前121天的数据（包括当天）
    end_date = first_percentile_date
    start_idx = max(0, df.index.get_loc(end_date) - 120)  # 121天窗口
    window_data = df.iloc[start_idx:df.index.get_loc(end_date)+1]
    
    current_value = df.loc[end_date, 'net_purchase_amount']
    percentile_value = df.loc[end_date, 'net_purchase_percentile']
    
    # 计算分位数
    window_values = window_data['net_purchase_amount'].dropna()
    calculated_percentile = (current_value <= window_values).mean() * 100
    
    print(f"  当日净申购金额: {current_value:,.2f}")
    print(f"  窗口期间: {window_data.index[0].strftime('%Y-%m-%d')} 至 {window_data.index[-1].strftime('%Y-%m-%d')}")
    print(f"  窗口数据点数: {len(window_values)}")
    print(f"  计算的分位数: {calculated_percentile:.1f}%")
    print(f"  记录的分位数: {percentile_value:.1f}%")

def display_signal_trigger_examples():
    """显示信号触发示例"""
    
    print("\n\n5. 信号触发示例:")
    print("-" * 80)
    
    # 读取信号数据
    signal_file = "分析结果_20240430_20250430/信号数据汇总表.csv"
    df = pd.read_csv(signal_file)
    
    # 显示各类信号的前5个例子
    signal_types = ['信号1_买入', '信号1_卖出', '信号2_买入', '信号2_卖出']
    
    for signal_type in signal_types:
        signal_examples = df[df[signal_type] == 1.0].head(5)
        if len(signal_examples) > 0:
            print(f"\n{signal_type} 触发示例:")
            for _, row in signal_examples.iterrows():
                print(f"  {row['ETF代码']} {row['日期']}: "
                      f"净申购分位数={row['净申购金额分位数']:.1f}%, "
                      f"5日净申购分位数={row['连续5日净申购金额分位数']:.1f}%")

if __name__ == "__main__":
    try:
        display_etf_calculation_sample()
        display_signal_summary()
        display_percentile_examples()
        display_signal_trigger_examples()
        
        print("\n\n" + "=" * 80)
        print("验证完成！所有计算结果已保存在 '分析结果_20240430_20250430' 目录中")
        print("主要文件:")
        print("  - 所有ETF数据汇总验证表.csv: 包含所有ETF的完整数据")
        print("  - 信号数据汇总表.csv: 包含所有信号触发的记录")
        print("  - [ETF代码]_详细数据.csv: 每只ETF的详细计算数据")
        print("  - 策略表现汇总.csv: 策略回测结果汇总")
        print("=" * 80)
        
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        print("请先运行 etf_net_purchase_analysis.py 生成数据文件")
    except Exception as e:
        print(f"发生错误: {e}")
