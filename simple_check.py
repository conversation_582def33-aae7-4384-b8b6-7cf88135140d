#!/usr/bin/env python3
import pandas as pd

# 检查Excel文件
excel_path = '超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx'

try:
    df = pd.read_excel(excel_path, sheet_name='超额收益汇总')
    print(f"Excel文件包含 {len(df)} 个ETF")
    print("ETF代码列表:")
    etf_codes = sorted(df['ETF代码'].tolist())
    for code in etf_codes:
        print(f"  {code}")
    
    print(f"\n588050是否存在: {'588050' in etf_codes}")
    print(f"588080是否存在: {'588080' in etf_codes}")
    print(f"588050是否存在(int): {588050 in etf_codes}")
    print(f"588080是否存在(int): {588080 in etf_codes}")

    # 检查数据类型
    print(f"\nETF代码数据类型: {type(etf_codes[0])}")
    print(f"588050在列表中的位置: {etf_codes.index(588050) if 588050 in etf_codes else '不存在'}")
    print(f"588080在列表中的位置: {etf_codes.index(588080) if 588080 in etf_codes else '不存在'}")

    # 显示588050和588080的数据
    if 588050 in etf_codes:
        row = df[df['ETF代码'] == 588050]
        if not row.empty:
            print(f"\n588050数据:")
            print(row.to_string())

    if 588080 in etf_codes:
        row = df[df['ETF代码'] == 588080]
        if not row.empty:
            print(f"\n588080数据:")
            print(row.to_string())
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
