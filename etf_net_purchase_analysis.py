#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF净申购数据分析脚本
计算ETF净申购金额、异动信号和收益率分析
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class ETFNetPurchaseAnalyzer:
    def __init__(self, data_path="ETF净申购数据", start_date="2024-04-30", end_date="2025-04-30"):
        self.data_path = data_path
        self.start_date = pd.to_datetime(start_date)
        self.end_date = pd.to_datetime(end_date)
        self.nav_data = None
        self.shares_data = {}
        self.price_data = None
        self.results = {}

    def load_data(self):
        """加载所有数据文件（先加载全量数据用于计算信号）"""
        print("正在加载全量数据...")

        # 1. 加载ETF净值数据（全量）
        nav_file = os.path.join(self.data_path, "基础数据", "ETF净值数据.csv")
        self.nav_data_full = pd.read_csv(nav_file, index_col=0)
        self.nav_data_full.index = pd.to_datetime(self.nav_data_full.index)
        print(f"全量净值数据加载完成，包含{len(self.nav_data_full.columns)}只ETF，{len(self.nav_data_full)}个交易日")
        print(f"数据范围：{self.nav_data_full.index[0].strftime('%Y-%m-%d')} 至 {self.nav_data_full.index[-1].strftime('%Y-%m-%d')}")

        # 2. 加载ETF份额数据（全量）
        shares_dir = os.path.join(self.data_path, "基础数据")
        shares_files = [f for f in os.listdir(shares_dir) if f.endswith("每日份额.csv")]

        self.shares_data_full = {}
        for file in shares_files:
            etf_code = file.replace("每日份额.csv", "")
            file_path = os.path.join(shares_dir, file)
            df = pd.read_csv(file_path)
            df['date'] = pd.to_datetime(df['date'])
            df = df.set_index('date')[['shares']].sort_index()
            self.shares_data_full[etf_code] = df

        print(f"全量份额数据加载完成，包含{len(self.shares_data_full)}只ETF")

        # 3. 加载行情数据（全量）
        price_file = os.path.join(self.data_path, "行情数据", "行情序列.csv")
        self.price_data_full = pd.read_csv(price_file)
        self.price_data_full['时间'] = pd.to_datetime(self.price_data_full['时间'])

        # 转换代码格式：510050.SH -> 510050.XSHG
        self.price_data_full['标准代码'] = self.price_data_full['代码'].apply(self._convert_price_code)
        print(f"全量行情数据加载完成，包含{len(self.price_data_full)}条记录")
        print(f"回测期间：{self.start_date.strftime('%Y-%m-%d')} 至 {self.end_date.strftime('%Y-%m-%d')}")

    def _convert_price_code(self, code):
        """转换行情数据中的代码格式"""
        if pd.isna(code) or not isinstance(code, str):
            return code
        if code.endswith('.SH'):
            base_code = code.replace('.SH', '')
            return f"{base_code}.XSHG"
        elif code.endswith('.SZ'):
            base_code = code.replace('.SZ', '')
            return f"{base_code}.XSHE"
        return code

    def calculate_share_changes(self):
        """计算每只ETF的每日基金份额变化（使用全量数据）"""
        print("正在计算基金份额变化...")

        # 使用全量数据计算
        for etf_code, shares_df in self.shares_data_full.items():
            # 计算份额变化：T日份额 - T-1日份额
            shares_df['shares_change'] = shares_df['shares'].diff()

        print("基金份额变化计算完成")

    def calculate_net_purchase_amount(self):
        """计算每只ETF的每日净申购金额（使用全量数据）"""
        print("正在计算净申购金额...")

        for etf_code in self.shares_data_full.keys():
            if etf_code not in self.nav_data_full.columns:
                print(f"警告：{etf_code}在净值数据中不存在")
                continue

            shares_df = self.shares_data_full[etf_code]
            nav_series = self.nav_data_full[etf_code]

            # 合并数据
            merged_df = shares_df.join(nav_series.rename('nav'), how='inner')

            # 计算净申购金额：份额变化 * 单位净值
            merged_df['net_purchase_amount'] = merged_df['shares_change'] * merged_df['nav']

            # 更新数据
            self.shares_data_full[etf_code] = merged_df

        print("净申购金额计算完成")

    def calculate_rolling_5day_net_purchase(self):
        """计算连续5个交易日的净申购金额（使用全量数据）"""
        print("正在计算连续5日净申购金额...")

        for etf_code, df in self.shares_data_full.items():
            if 'net_purchase_amount' in df.columns:
                # 计算连续5日净申购金额累计
                df['net_purchase_5d'] = df['net_purchase_amount'].rolling(window=5, min_periods=5).sum()

        print("连续5日净申购金额计算完成")

    def calculate_percentiles(self):
        """计算净申购金额的分位数（使用全量数据，250天窗口）"""
        print("正在计算分位数...")

        # 使用标准的250天窗口（1年交易日）
        window_size = 250
        min_periods = 250  # 需要完整的250天数据

        print(f"使用{window_size}天窗口计算分位数，最少需要{min_periods}天数据")

        for etf_code, df in self.shares_data_full.items():
            if 'net_purchase_amount' not in df.columns:
                continue

            # 计算单日净申购金额的分位数
            # 正确公式：历史值小于等于当前值的比例
            df['net_purchase_percentile'] = df['net_purchase_amount'].rolling(
                window=window_size, min_periods=min_periods
            ).apply(lambda x: (x <= x.iloc[-1]).mean() * 100)

            # 计算5日净申购金额的分位数
            if 'net_purchase_5d' in df.columns:
                df['net_purchase_5d_percentile'] = df['net_purchase_5d'].rolling(
                    window=window_size, min_periods=min_periods
                ).apply(lambda x: (x <= x.iloc[-1]).mean() * 100)

        print("分位数计算完成")

    def generate_signals(self):
        """生成异动信号（使用全量数据）"""
        print("正在生成异动信号...")

        for etf_code, df in self.shares_data_full.items():
            # 信号1：基于单日净申购金额分位数
            if 'net_purchase_percentile' in df.columns:
                df['signal_1_buy'] = (df['net_purchase_percentile'] >= 90).astype(int)
                df['signal_1_sell'] = (df['net_purchase_percentile'] <= 10).astype(int)

            # 信号2：基于5日净申购金额分位数
            if 'net_purchase_5d_percentile' in df.columns:
                df['signal_2_buy'] = (df['net_purchase_5d_percentile'] >= 90).astype(int)
                df['signal_2_sell'] = (df['net_purchase_5d_percentile'] <= 10).astype(int)

        # 现在截取回测期间的数据用于后续分析
        self._filter_backtest_period()

        print("异动信号生成完成")

    def _filter_backtest_period(self):
        """截取回测期间的数据"""
        print(f"正在截取回测期间数据：{self.start_date.strftime('%Y-%m-%d')} 至 {self.end_date.strftime('%Y-%m-%d')}")

        # 截取净值数据
        self.nav_data = self.nav_data_full[(self.nav_data_full.index >= self.start_date) &
                                           (self.nav_data_full.index <= self.end_date)]

        # 截取份额和信号数据
        self.shares_data = {}
        for etf_code, df in self.shares_data_full.items():
            filtered_df = df[(df.index >= self.start_date) & (df.index <= self.end_date)]
            self.shares_data[etf_code] = filtered_df

        # 截取行情数据
        self.price_data = self.price_data_full[(self.price_data_full['时间'] >= self.start_date) &
                                               (self.price_data_full['时间'] <= self.end_date)]

        print(f"回测期间数据截取完成：净值数据{len(self.nav_data)}天，行情数据{len(self.price_data)}条")

    def calculate_returns(self):
        """计算信号后的收益率"""
        print("正在计算收益率...")

        # 准备行情数据字典
        price_dict = {}
        for etf_code in self.shares_data.keys():
            price_subset = self.price_data[self.price_data['标准代码'] == etf_code].copy()
            if len(price_subset) > 0:
                price_subset = price_subset.set_index('时间')[['开盘价(元)', '最高价(元)', '最低价(元)', '收盘价(元)']]
                price_dict[etf_code] = price_subset

        # 持有期列表
        holding_periods = [1, 3, 5, 7, 10, 15, 30, 60]

        # 初始化结果字典
        self.results = {
            'signal_1_buy': [],
            'signal_1_sell': [],
            'signal_2_buy': [],
            'signal_2_sell': []
        }

        for etf_code, df in self.shares_data.items():
            if etf_code not in price_dict:
                continue

            price_df = price_dict[etf_code]

            # 合并信号数据和价格数据
            merged_data = df.join(price_df, how='inner')

            # 计算各种信号的收益率
            for signal_type in ['signal_1_buy', 'signal_1_sell', 'signal_2_buy', 'signal_2_sell']:
                if signal_type not in merged_data.columns:
                    continue

                signal_dates = merged_data[merged_data[signal_type] == 1].index

                for signal_date in signal_dates:
                    returns_row = self._calculate_signal_returns(
                        merged_data, signal_date, holding_periods, etf_code
                    )
                    if returns_row is not None:
                        self.results[signal_type].append(returns_row)

        print("收益率计算完成")

    def _calculate_signal_returns(self, data, signal_date, holding_periods, etf_code):
        """计算单个信号的收益率"""
        try:
            # 找到信号日期之后的第一个交易日
            future_dates = data.index[data.index > signal_date]
            if len(future_dates) == 0:
                return None

            entry_date = future_dates[0]
            entry_price = data.loc[entry_date, '开盘价(元)']

            if pd.isna(entry_price) or entry_price <= 0:
                return None

            returns_row = {
                'etf_code': etf_code,
                'signal_date': signal_date,
                'entry_date': entry_date,
                'entry_price': entry_price
            }

            # 计算不同持有期的收益率
            for period in holding_periods:
                exit_dates = future_dates[future_dates >= entry_date]
                if len(exit_dates) >= period:
                    exit_date = exit_dates[period-1]
                    exit_price = data.loc[exit_date, '开盘价(元)']

                    if not pd.isna(exit_price) and exit_price > 0:
                        returns_row[f'return_{period}d'] = (exit_price - entry_price) / entry_price * 100

                        # 计算区间最高涨幅和最大跌幅
                        period_data = data.loc[entry_date:exit_date]
                        if len(period_data) > 0:
                            max_high = period_data['最高价(元)'].max()
                            min_low = period_data['最低价(元)'].min()

                            if not pd.isna(max_high) and max_high > 0:
                                returns_row[f'max_gain_{period}d'] = (max_high - entry_price) / entry_price * 100
                            if not pd.isna(min_low) and min_low > 0:
                                returns_row[f'max_loss_{period}d'] = (min_low - entry_price) / entry_price * 100

            return returns_row

        except Exception as e:
            print(f"计算收益率时出错：{e}")
            return None

    def analyze_strategy_performance(self):
        """分析策略表现"""
        print("正在分析策略表现...")

        performance_summary = {}
        holding_periods = [1, 3, 5, 7, 10, 15, 30, 60]

        for signal_type, results_list in self.results.items():
            if not results_list:
                continue

            df_results = pd.DataFrame(results_list)
            signal_performance = {
                'total_signals': len(df_results),
                'periods': {}
            }

            for period in holding_periods:
                return_col = f'return_{period}d'
                if return_col in df_results.columns:
                    returns = df_results[return_col].dropna()
                    if len(returns) > 0:
                        signal_performance['periods'][f'{period}d'] = {
                            'count': len(returns),
                            'win_rate': (returns > 0).mean() * 100,
                            'avg_return': returns.mean(),
                            'median_return': returns.median(),
                            'std_return': returns.std(),
                            'max_return': returns.max(),
                            'min_return': returns.min()
                        }

            performance_summary[signal_type] = signal_performance

        self.performance_summary = performance_summary
        print("策略表现分析完成")

        return performance_summary

    def save_results(self, output_dir="分析结果"):
        """保存分析结果"""
        # 创建带日期的输出目录
        date_suffix = f"{self.start_date.strftime('%Y%m%d')}_{self.end_date.strftime('%Y%m%d')}"
        output_dir = f"{output_dir}_{date_suffix}"

        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        print("正在保存结果...")

        # 保存详细的信号和收益率数据
        for signal_type, results_list in self.results.items():
            if results_list:
                df = pd.DataFrame(results_list)
                output_file = os.path.join(output_dir, f"{signal_type}_详细结果.csv")
                df.to_csv(output_file, index=False, encoding='utf-8-sig')

        # 保存策略表现汇总
        summary_data = []
        for signal_type, performance in self.performance_summary.items():
            for period, metrics in performance['periods'].items():
                summary_data.append({
                    '信号类型': signal_type,
                    '持有期': period,
                    '信号数量': metrics['count'],
                    '胜率(%)': round(metrics['win_rate'], 2),
                    '平均收益率(%)': round(metrics['avg_return'], 4),
                    '中位数收益率(%)': round(metrics['median_return'], 4),
                    '收益率标准差(%)': round(metrics['std_return'], 4),
                    '最大收益率(%)': round(metrics['max_return'], 4),
                    '最小收益率(%)': round(metrics['min_return'], 4)
                })

        summary_df = pd.DataFrame(summary_data)
        summary_file = os.path.join(output_dir, "策略表现汇总.csv")
        summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')

        print(f"结果已保存到 {output_dir} 目录")

        # 保存所有ETF的详细数据供验证
        self.save_detailed_etf_data(output_dir)

    def save_detailed_etf_data(self, output_dir):
        """保存所有ETF的详细数据供验证"""
        print("正在保存ETF详细数据...")

        # 为每只ETF保存详细数据
        for etf_code, df in self.shares_data.items():
            if len(df) == 0:
                continue

            # 准备输出数据
            output_df = df.copy()
            output_df.index.name = 'date'

            # 重新排列列的顺序，便于查看
            columns_order = ['shares', 'shares_change', 'nav', 'net_purchase_amount',
                           'net_purchase_5d', 'net_purchase_percentile', 'net_purchase_5d_percentile',
                           'signal_1_buy', 'signal_1_sell', 'signal_2_buy', 'signal_2_sell']

            # 只保留存在的列
            available_columns = [col for col in columns_order if col in output_df.columns]
            output_df = output_df[available_columns]

            # 保存到文件
            output_file = os.path.join(output_dir, f"{etf_code}_详细数据.csv")
            output_df.to_csv(output_file, encoding='utf-8-sig')

        # 创建汇总表格，包含所有ETF的关键数据
        self.create_summary_table(output_dir)

        print("ETF详细数据保存完成")

    def create_summary_table(self, output_dir):
        """创建包含所有ETF关键数据的汇总表格"""
        print("正在创建汇总验证表格...")

        all_data = []

        for etf_code, df in self.shares_data.items():
            if len(df) == 0:
                continue

            # 为每个日期创建一行数据
            for date, row in df.iterrows():
                data_row = {
                    'ETF代码': etf_code,
                    '日期': date.strftime('%Y-%m-%d'),
                    '基金份额': row.get('shares', np.nan),
                    '份额变化': row.get('shares_change', np.nan),
                    '单位净值': row.get('nav', np.nan),
                    '净申购金额': row.get('net_purchase_amount', np.nan),
                    '连续5日净申购金额': row.get('net_purchase_5d', np.nan),
                    '净申购金额分位数': row.get('net_purchase_percentile', np.nan),
                    '连续5日净申购金额分位数': row.get('net_purchase_5d_percentile', np.nan),
                    '信号1_买入': row.get('signal_1_buy', 0),
                    '信号1_卖出': row.get('signal_1_sell', 0),
                    '信号2_买入': row.get('signal_2_buy', 0),
                    '信号2_卖出': row.get('signal_2_sell', 0)
                }
                all_data.append(data_row)

        # 创建汇总DataFrame
        summary_df = pd.DataFrame(all_data)

        # 按ETF代码和日期排序
        summary_df = summary_df.sort_values(['ETF代码', '日期'])

        # 保存汇总表格
        summary_file = os.path.join(output_dir, "所有ETF数据汇总验证表.csv")
        summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')

        # 创建信号汇总表
        signal_summary = summary_df[summary_df[['信号1_买入', '信号1_卖出', '信号2_买入', '信号2_卖出']].sum(axis=1) > 0].copy()
        signal_file = os.path.join(output_dir, "信号数据汇总表.csv")
        signal_summary.to_csv(signal_file, index=False, encoding='utf-8-sig')

        print(f"汇总验证表格已保存，包含{len(summary_df)}条记录")
        print(f"信号数据汇总表已保存，包含{len(signal_summary)}条信号记录")

    def run_analysis(self):
        """运行完整分析流程"""
        print("开始ETF净申购数据分析...")

        self.load_data()
        self.calculate_share_changes()
        self.calculate_net_purchase_amount()
        self.calculate_rolling_5day_net_purchase()
        self.calculate_percentiles()
        self.generate_signals()
        self.calculate_returns()
        self.analyze_strategy_performance()
        self.save_results()

        print("分析完成！")
        return self.performance_summary

if __name__ == "__main__":
    analyzer = ETFNetPurchaseAnalyzer()
    performance = analyzer.run_analysis()

    # 打印简要结果
    print("\n=== 策略表现汇总 ===")
    for signal_type, perf in performance.items():
        print(f"\n{signal_type}:")
        print(f"  总信号数: {perf['total_signals']}")
        for period, metrics in perf['periods'].items():
            print(f"  {period}: 胜率{metrics['win_rate']:.1f}%, 平均收益{metrics['avg_return']:.3f}%")
