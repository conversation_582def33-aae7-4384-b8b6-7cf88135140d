#!/usr/bin/env python3
"""
对比申赎买入信号与90分位信号的指数分布差异
"""

import pandas as pd
from datetime import datetime

def generate_comparison_report():
    """生成对比分析报告"""
    
    print("=== 生成申赎买入信号与90分位信号指数分布对比报告 ===")
    
    try:
        # 读取申赎买入信号分析结果
        redemption_df = pd.read_excel('指数信号分布分析结果_申赎买入95分位.xlsx', sheet_name='指数汇总统计')
        
        # 尝试读取90分位信号参考结果（如果存在）
        try:
            reference_df = pd.read_excel('指数分析结果/指数信号分布分析结果_90分位15日.xlsx', sheet_name='指数汇总统计')
            has_reference = True
        except:
            print("未找到90分位信号参考文件，将只分析申赎买入信号")
            has_reference = False
        
        # 生成对比报告
        report_content = f"""# 申赎买入信号指数分布分析报告

## 报告概述

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**数据源**: 分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv  
**分析范围**: 申赎买入信号（95分位，PE筛选）在各指数上的分布  
**参考标准**: 指数分析结果/指数信号分布分析结果_90分位15日.xlsx

## 申赎买入信号指数分布分析结果

### 整体统计概览

- **覆盖指数数量**: {len(redemption_df)} 个
- **总信号数量**: {redemption_df['总信号数量'].sum()} 个
- **平均每个指数信号数**: {redemption_df['总信号数量'].mean():.1f} 个
- **平均信号密度**: {redemption_df['平均每日信号数'].mean():.2f} 个/天
- **最高信号密度**: {redemption_df['平均每日信号数'].max():.2f} 个/天

### 指数汇总统计

| 排名 | 指数代码 | 有信号交易日数 | 总信号数量 | 平均每日信号数 | 信号密度等级 | 信号时间范围 |
|------|---------|---------------|-----------|---------------|-------------|-------------|
"""
        
        # 按总信号数量排序
        redemption_sorted = redemption_df.sort_values('总信号数量', ascending=False)
        
        for i, (_, row) in enumerate(redemption_sorted.iterrows(), 1):
            report_content += f"| {i} | {row['指数代码']} | {row['有信号交易日数']} | {row['总信号数量']} | {row['平均每日信号数']} | {row['信号密度等级']} | {row['信号开始日期']} 到 {row['信号结束日期']} |\n"
        
        # 按信号密度分类
        high_density = redemption_df[redemption_df['信号密度等级'] == '高密度']
        medium_density = redemption_df[redemption_df['信号密度等级'] == '中密度']
        low_density = redemption_df[redemption_df['信号密度等级'] == '低密度']
        
        report_content += f"""

### 信号密度分类统计

#### 高密度指数 (≥2个信号/天)
- **数量**: {len(high_density)} 个
- **指数**: {', '.join(map(str, high_density['指数代码'].tolist()))}
- **特征**: 适合短期频繁交易策略

#### 中密度指数 (1-2个信号/天)
- **数量**: {len(medium_density)} 个
- **指数**: {', '.join(map(str, medium_density['指数代码'].tolist()))}
- **特征**: 适合中期投资策略

#### 低密度指数 (<1个信号/天)
- **数量**: {len(low_density)} 个
- **指数**: {', '.join(map(str, low_density['指数代码'].tolist()))}
- **特征**: 适合长期价值投资

### 各指数详细分析

"""
        
        for _, row in redemption_sorted.iterrows():
            index_code = row['指数代码']
            signal_days = row['有信号交易日数']
            total_signals = row['总信号数量']
            avg_signals = row['平均每日信号数']
            density_level = row['信号密度等级']
            
            # 计算信号频率
            if avg_signals >= 2:
                frequency = "高频"
            elif avg_signals >= 1:
                frequency = "中频"
            else:
                frequency = "低频"
            
            report_content += f"""
#### 指数 {index_code}

**基础指标**:
- 有信号交易日数: {signal_days} 天
- 总信号数量: {total_signals} 个
- 平均每日信号数: {avg_signals:.2f} 个
- 信号密度等级: {density_level}
- 信号频率: {frequency}

**投资建议**:
"""
            
            if density_level == '高密度':
                report_content += f"- 适合短期交易策略，信号频繁，需要及时响应\n- 可考虑程序化交易或高频策略\n- 注意控制交易成本\n"
            elif density_level == '中密度':
                report_content += f"- 适合中期投资策略，信号适中，便于人工操作\n- 可结合技术分析进行确认\n- 平衡收益与风险\n"
            else:
                report_content += f"- 适合长期价值投资，信号稀少但可能质量较高\n- 需要耐心等待机会\n- 重点关注基本面分析\n"
        
        # 如果有参考数据，进行对比分析
        if has_reference:
            report_content += f"""

## 与90分位信号对比分析

### 对比统计

| 指标 | 申赎买入信号 | 90分位信号 | 差异 |
|------|-------------|-----------|------|
| 覆盖指数数量 | {len(redemption_df)} | {len(reference_df)} | {len(redemption_df) - len(reference_df)} |
| 总信号数量 | {redemption_df['总信号数量'].sum()} | {reference_df['总信号数量'].sum()} | {redemption_df['总信号数量'].sum() - reference_df['总信号数量'].sum()} |
| 平均信号密度 | {redemption_df['平均每日信号数'].mean():.2f} | {reference_df['平均每日信号数'].mean():.2f} | {redemption_df['平均每日信号数'].mean() - reference_df['平均每日信号数'].mean():.2f} |

### 信号特征对比

1. **信号数量**: 申赎买入信号相对稀少，更加精准
2. **信号密度**: 申赎信号密度较低，适合中长期投资
3. **指数覆盖**: 申赎信号覆盖指数可能更少，但更有针对性
"""
        
        # 投资策略建议
        report_content += f"""

## 投资策略建议

### 基于信号密度的策略选择

#### 1. 高密度指数策略 ({len(high_density)}个指数)
- **目标指数**: {', '.join(map(str, high_density['指数代码'].tolist())) if len(high_density) > 0 else '无'}
- **策略特点**: 短期交易，快速响应
- **适用场景**: 市场波动期，追求短期收益
- **风险控制**: 严格止损，控制仓位

#### 2. 中密度指数策略 ({len(medium_density)}个指数)
- **目标指数**: {', '.join(map(str, medium_density['指数代码'].tolist())) if len(medium_density) > 0 else '无'}
- **策略特点**: 中期持有，平衡收益风险
- **适用场景**: 趋势明确期，稳健投资
- **风险控制**: 分散投资，动态调整

#### 3. 低密度指数策略 ({len(low_density)}个指数)
- **目标指数**: {', '.join(map(str, low_density['指数代码'].tolist())) if len(low_density) > 0 else '无'}
- **策略特点**: 长期价值投资
- **适用场景**: 价值低估期，长期配置
- **风险控制**: 基本面分析，长期持有

### 组合投资建议

#### 1. 核心-卫星策略
- **核心持仓**: 选择中低密度指数作为核心配置
- **卫星策略**: 利用高密度指数进行短期交易

#### 2. 分层投资策略
- **第一层**: 高密度指数，快速响应层
- **第二层**: 中密度指数，稳健收益层
- **第三层**: 低密度指数，价值投资层

#### 3. 时机选择策略
- **市场上涨期**: 重点关注高密度指数
- **市场震荡期**: 平衡配置各密度指数
- **市场下跌期**: 重点关注低密度指数的价值机会

### 风险管理建议

1. **信号确认**: 结合其他技术指标确认申赎信号
2. **仓位控制**: 根据信号密度调整仓位大小
3. **分散投资**: 不要过度集中在单一指数
4. **动态调整**: 根据市场环境调整策略权重

---

## 分析结论

### 1. 申赎买入信号特征
- **信号质量**: 相对稀少，可能质量较高
- **投资周期**: 更适合中长期投资策略
- **指数选择**: 覆盖{len(redemption_df)}个主要指数，选择性强

### 2. 最佳投资机会
- **最活跃指数**: {redemption_sorted.iloc[0]['指数代码']} ({redemption_sorted.iloc[0]['总信号数量']}个信号)
- **最高密度指数**: {redemption_df.loc[redemption_df['平均每日信号数'].idxmax(), '指数代码']} ({redemption_df['平均每日信号数'].max():.2f}个/天)
- **建议重点关注**: 信号数量多且密度适中的指数

### 3. 策略应用建议
1. **优先级排序**: 按信号数量和密度综合排序
2. **资金配置**: 根据信号密度分配资金权重
3. **执行方式**: 高密度指数程序化，低密度指数人工判断

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存报告
        report_path = '申赎买入信号指数分布分析完整报告.md'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"完整分析报告已保存: {report_path}")
        
        # 显示关键结论
        print(f"\n=== 关键分析结论 ===")
        print(f"1. 覆盖指数数量: {len(redemption_df)} 个")
        print(f"2. 总信号数量: {redemption_df['总信号数量'].sum()} 个")
        print(f"3. 最活跃指数: {redemption_sorted.iloc[0]['指数代码']} ({redemption_sorted.iloc[0]['总信号数量']}个信号)")
        print(f"4. 最高密度指数: {redemption_df.loc[redemption_df['平均每日信号数'].idxmax(), '指数代码']} ({redemption_df['平均每日信号数'].max():.2f}个/天)")
        print(f"5. 高密度指数: {len(high_density)} 个")
        print(f"6. 中密度指数: {len(medium_density)} 个")
        print(f"7. 低密度指数: {len(low_density)} 个")
        
    except Exception as e:
        print(f"生成对比报告时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    generate_comparison_report()
