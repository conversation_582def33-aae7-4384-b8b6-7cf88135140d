#!/usr/bin/env python3
"""
将申赎买入信号数据重新整理成新的表格格式
输出格式：
- A列：日期（2024-4-30至2025-4-30的全部交易日）
- B列开始：各ETF代码列，有信号填1，无信号为空值
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def standardize_etf_code(code):
    """标准化ETF代码，去除后缀"""
    if pd.isna(code):
        return None
    
    code_str = str(code)
    # 去除常见的后缀
    suffixes = ['.SH', '.SZ', '.XSHE', '.XSHG']
    for suffix in suffixes:
        if code_str.endswith(suffix):
            code_str = code_str[:-len(suffix)]
    
    # 确保是6位数字格式
    try:
        return int(code_str)
    except:
        return None

def generate_trading_days(start_date, end_date):
    """生成交易日列表（排除周末）"""
    print(f"=== 生成交易日列表 ===")
    
    start = pd.to_datetime(start_date)
    end = pd.to_datetime(end_date)
    
    # 生成日期范围
    date_range = pd.date_range(start=start, end=end, freq='D')
    
    # 排除周末（周六=5，周日=6）
    trading_days = [date for date in date_range if date.weekday() < 5]
    
    print(f"日期范围: {start_date} 到 {end_date}")
    print(f"总交易日数量: {len(trading_days)}")
    print(f"起始交易日: {trading_days[0].strftime('%Y-%m-%d')}")
    print(f"结束交易日: {trading_days[-1].strftime('%Y-%m-%d')}")
    
    return [date.date() for date in trading_days]

def load_and_process_redemption_signals():
    """加载并处理申赎买入信号数据"""
    print(f"\n=== 加载申赎买入信号数据 ===")
    
    try:
        # 读取申赎买入信号数据
        signals_df = pd.read_csv('分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv')
        
        print(f"原始数据行数: {len(signals_df)}")
        print(f"列名: {signals_df.columns.tolist()}")
        
        # 转换时间格式
        if 'signal_date' in signals_df.columns:
            signals_df['signal_date'] = pd.to_datetime(signals_df['signal_date'])
            signals_df['date'] = signals_df['signal_date'].dt.date
        else:
            print("未找到signal_date列")
            return pd.DataFrame()
        
        # 标准化ETF代码
        if 'etf_code' in signals_df.columns:
            signals_df['etf_code_std'] = signals_df['etf_code'].apply(standardize_etf_code)
        else:
            print("未找到etf_code列")
            return pd.DataFrame()
        
        signals_df = signals_df.dropna(subset=['etf_code_std'])
        
        print(f"标准化后信号数: {len(signals_df)}")
        
        # 统计各ETF的信号数量
        etf_counts = signals_df['etf_code_std'].value_counts().sort_index()
        print(f"涉及ETF数量: {len(etf_counts)}")
        print(f"ETF列表: {sorted(etf_counts.index.tolist())}")
        
        # 显示各ETF信号数量
        print(f"各ETF信号数量:")
        for etf_code, count in etf_counts.items():
            print(f"  ETF {etf_code}: {count}个")
        
        return signals_df
        
    except Exception as e:
        print(f"加载申赎买入信号数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def get_etf_list_from_mapping():
    """从ETF跟踪指数文件获取22只ETF列表"""
    print(f"\n=== 获取ETF列表 ===")
    
    try:
        etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
        etf_list = sorted(etf_mapping['ETF代码'].tolist())
        
        print(f"ETF映射文件中的ETF数量: {len(etf_list)}")
        print(f"ETF列表: {etf_list}")
        
        return etf_list
        
    except Exception as e:
        print(f"获取ETF列表时出错: {e}")
        # 如果无法读取映射文件，使用信号数据中的ETF
        return None

def create_signal_matrix(trading_days, etf_list, signals_df):
    """创建信号矩阵（0/1格式）"""
    print(f"\n=== 创建信号矩阵 ===")
    
    # 每日每个ETF只保留一个信号记录（去重）
    daily_signals = signals_df.groupby(['date', 'etf_code_std']).first().reset_index()
    daily_signals['has_signal'] = 1  # 有信号标记为1
    
    print(f"每日信号统计行数: {len(daily_signals)}")
    
    # 创建基础矩阵
    result_data = []
    
    for date in trading_days:
        row_data = {'日期': date}
        
        # 获取该日期的所有信号
        date_signals = daily_signals[daily_signals['date'] == date]
        
        # 为每个ETF填充信号标记
        for etf_code in etf_list:
            etf_signals = date_signals[date_signals['etf_code_std'] == etf_code]
            
            if not etf_signals.empty:
                # 如果有信号，标记为1
                row_data[str(etf_code)] = 1
            else:
                # 如果没有信号，保持为空（NaN）
                row_data[str(etf_code)] = np.nan
        
        result_data.append(row_data)
    
    # 转换为DataFrame
    result_df = pd.DataFrame(result_data)
    
    # 将日期列转换为字符串格式
    result_df['日期'] = result_df['日期'].apply(lambda x: x.strftime('%Y-%m-%d'))
    
    print(f"结果矩阵形状: {result_df.shape}")
    print(f"列名: {result_df.columns.tolist()}")
    
    # 统计信号分布
    total_signals = 0
    signal_days = 0
    
    for col in result_df.columns[1:]:  # 跳过日期列
        col_signals = result_df[col].dropna()
        if not col_signals.empty:
            total_signals += len(col_signals)
            signal_days += len(col_signals)
    
    print(f"总信号天数: {total_signals}")
    print(f"有信号的ETF-日期组合: {signal_days}")
    
    return result_df

def save_results(result_df):
    """保存结果"""
    print(f"\n=== 保存结果 ===")
    
    # 保存为Excel文件
    excel_path = '申赎买入信号整理表_2024-2025.xlsx'
    
    with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
        result_df.to_excel(writer, sheet_name='申赎买入信号明细', index=False)
        
        # 获取工作表和工作簿对象
        workbook = writer.book
        worksheet = writer.sheets['申赎买入信号明细']
        
        # 设置列宽
        worksheet.set_column('A:A', 12)  # 日期列
        for i in range(1, len(result_df.columns)):
            worksheet.set_column(i, i, 8)  # ETF列
        
        # 设置数字格式（整数）
        number_format = workbook.add_format({'num_format': '0'})
        for i in range(1, len(result_df.columns)):
            worksheet.set_column(i, i, 8, number_format)
        
        # 添加统计信息sheet
        stats_data = []
        
        # 计算各ETF的统计信息
        for col in result_df.columns[1:]:
            etf_code = col
            etf_signals = result_df[col].dropna()
            
            if not etf_signals.empty:
                signal_days = len(etf_signals)
                total_trading_days = len(result_df)
                signal_rate = signal_days / total_trading_days * 100
                
                # 计算信号分布
                first_signal_date = None
                last_signal_date = None
                
                signal_dates = result_df[result_df[col] == 1]['日期']
                if not signal_dates.empty:
                    first_signal_date = signal_dates.iloc[0]
                    last_signal_date = signal_dates.iloc[-1]
            else:
                signal_days = 0
                total_trading_days = len(result_df)
                signal_rate = 0
                first_signal_date = None
                last_signal_date = None
            
            stats_data.append({
                'ETF代码': etf_code,
                '有信号天数': signal_days,
                '总交易日数': total_trading_days,
                '信号频率(%)': round(signal_rate, 2),
                '首次信号日期': first_signal_date,
                '最后信号日期': last_signal_date
            })
        
        stats_df = pd.DataFrame(stats_data)
        stats_df = stats_df.sort_values('有信号天数', ascending=False)
        stats_df.to_excel(writer, sheet_name='ETF信号统计', index=False)
        
        # 添加日期统计sheet
        date_stats = []
        for _, row in result_df.iterrows():
            date = row['日期']
            active_etfs = 0
            
            for col in result_df.columns[1:]:
                if row[col] == 1:
                    active_etfs += 1
            
            if active_etfs > 0:
                date_stats.append({
                    '日期': date,
                    '发出信号ETF数': active_etfs,
                    '信号ETF列表': ', '.join([col for col in result_df.columns[1:] if row[col] == 1])
                })
        
        if date_stats:
            date_stats_df = pd.DataFrame(date_stats)
            date_stats_df = date_stats_df.sort_values('发出信号ETF数', ascending=False)
            date_stats_df.to_excel(writer, sheet_name='日期信号统计', index=False)
        
        # 添加月度统计sheet
        result_df_copy = result_df.copy()
        result_df_copy['日期'] = pd.to_datetime(result_df_copy['日期'])
        result_df_copy['年月'] = result_df_copy['日期'].dt.to_period('M')
        
        monthly_stats = []
        for month in result_df_copy['年月'].unique():
            month_data = result_df_copy[result_df_copy['年月'] == month]
            
            total_signals = 0
            active_days = 0
            
            for _, row in month_data.iterrows():
                day_signals = sum([1 for col in result_df.columns[1:] if row[col] == 1])
                if day_signals > 0:
                    total_signals += day_signals
                    active_days += 1
            
            monthly_stats.append({
                '年月': str(month),
                '交易日数': len(month_data),
                '有信号天数': active_days,
                '总信号次数': total_signals,
                '平均每日信号ETF数': round(total_signals / len(month_data), 2) if len(month_data) > 0 else 0
            })
        
        monthly_stats_df = pd.DataFrame(monthly_stats)
        monthly_stats_df.to_excel(writer, sheet_name='月度信号统计', index=False)
    
    print(f"Excel文件已保存: {excel_path}")
    
    # 生成简要报告
    report_content = f"""# 申赎买入信号整理报告

## 数据概述

**处理时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**数据源**: 分析结果_买入95分位_卖出5分位_20240430_20250430/signal_1_buy_pe_filtered_详细结果.csv  
**信号类型**: 申赎买入信号（95分位，PE筛选）

## 输出表格结构

### 主表：申赎买入信号明细
- **A列**: 日期（2024-04-30 到 2025-04-30的交易日）
- **B列开始**: 各ETF代码列，有信号填1，无信号为空
- **数据格式**: 二进制格式（1表示有信号，空值表示无信号）

### 统计表格
1. **ETF信号统计**: 各ETF的信号频率和时间分布
2. **日期信号统计**: 各交易日发出信号的ETF数量
3. **月度信号统计**: 按月统计的信号分布情况

## 数据统计

- **交易日总数**: {len(result_df)} 天
- **ETF数量**: {len(result_df.columns) - 1} 个
- **总信号天数**: {sum([len(result_df[col].dropna()) for col in result_df.columns[1:]])} 个
- **有信号的交易日**: {len([row for _, row in result_df.iterrows() if any(row[col] == 1 for col in result_df.columns[1:])])} 天

## 主要ETF信号分布

"""
    
    # 添加前10个ETF的统计
    stats_df = pd.DataFrame(stats_data).sort_values('有信号天数', ascending=False)
    
    for i, (_, row) in enumerate(stats_df.head(10).iterrows(), 1):
        report_content += f"{i}. **ETF {row['ETF代码']}**: {row['有信号天数']}天, 频率{row['信号频率(%)']}%\n"
    
    report_content += f"""

## 信号时间分布

### 最活跃的交易日
"""
    
    # 找出信号最多的日期
    if date_stats:
        date_stats_df = pd.DataFrame(date_stats)
        top_dates = date_stats_df.nlargest(5, '发出信号ETF数')
        
        for _, row in top_dates.iterrows():
            report_content += f"- **{row['日期']}**: {row['发出信号ETF数']}个ETF发出信号\n"
    
    report_content += f"""

## 使用说明

1. **数据格式**: Excel文件，包含4个工作表
2. **主要用途**: 信号同步性分析、ETF选择、时间序列分析
3. **注意事项**: 
   - 1表示当日该ETF发出申赎买入信号
   - 空值表示当日该ETF未发出信号
   - 每个ETF每日最多记录1个信号

## 分析建议

1. **信号频率分析**: 关注信号频率高的ETF
2. **同步性分析**: 研究多个ETF同时发出信号的日期
3. **时间趋势**: 分析信号的季节性和周期性特征
4. **ETF选择**: 基于信号频率和质量选择投资标的

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存报告
    report_path = '申赎买入信号整理报告.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"报告已保存: {report_path}")
    
    return excel_path, report_path

def main():
    """主函数"""
    print("=== 申赎买入信号数据整理 ===")
    print(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 生成交易日列表
    trading_days = generate_trading_days('2024-04-30', '2025-04-30')
    
    # 加载申赎买入信号数据
    signals_df = load_and_process_redemption_signals()
    
    if signals_df.empty:
        print("申赎买入信号数据为空，无法继续处理")
        return
    
    # 获取ETF列表
    etf_list = get_etf_list_from_mapping()
    
    if etf_list is None:
        # 如果无法从映射文件获取，使用信号数据中的ETF
        etf_list = sorted(signals_df['etf_code_std'].unique())
        print(f"使用信号数据中的ETF列表: {etf_list}")
    
    # 创建信号矩阵
    result_df = create_signal_matrix(trading_days, etf_list, signals_df)
    
    # 保存结果
    excel_path, report_path = save_results(result_df)
    
    # 显示完成信息
    print(f"\n=== 处理完成 ===")
    print(f"输出文件: {excel_path}")
    print(f"报告文件: {report_path}")
    print(f"数据维度: {result_df.shape[0]} 个交易日 × {result_df.shape[1]-1} 个ETF")
    
    # 显示数据预览
    print(f"\n=== 数据预览 ===")
    print("前5行数据:")
    print(result_df.head().to_string(index=False))
    
    print(f"\n后5行数据:")
    print(result_df.tail().to_string(index=False))
    
    # 显示信号统计
    signal_summary = {}
    for col in result_df.columns[1:]:
        signal_count = len(result_df[col].dropna())
        if signal_count > 0:
            signal_summary[col] = signal_count
    
    print(f"\n=== 信号统计摘要 ===")
    sorted_signals = sorted(signal_summary.items(), key=lambda x: x[1], reverse=True)
    for etf_code, count in sorted_signals[:10]:
        print(f"ETF {etf_code}: {count}天有信号")

if __name__ == "__main__":
    main()
