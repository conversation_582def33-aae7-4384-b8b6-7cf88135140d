#!/usr/bin/env python3
"""
生成跟踪相同指数ETF信号相关性分析详细报告
"""

import pandas as pd
from datetime import datetime

def generate_detailed_report():
    """生成详细的相关性分析报告"""
    
    print("=== 生成跟踪相同指数ETF信号相关性分析详细报告 ===")
    
    try:
        # 读取相关性分析结果
        correlation_df = pd.read_excel('跟踪相同指数ETF信号相关性分析结果.xlsx', sheet_name='ETF对相关性分析')
        index_stats = pd.read_excel('跟踪相同指数ETF信号相关性分析结果.xlsx', sheet_name='按指数分组统计')
        
        # 生成Markdown报告
        report_content = f"""# 跟踪相同指数ETF信号相关性分析报告

## 报告概述

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**分析目的**: 分析跟踪相同指数的ETF发出90分位成交额信号日期的相关性  
**分析方法**: Jaccard相似度系数  
**信号条件**: pe_filter_passed=True 且 threshold=90

## 数据源说明

### ETF跟踪指数映射
- **数据源**: `市盈率信息/ETF跟踪指数.xlsx`
- **用途**: 确定哪些ETF跟踪相同指数

### 90分位成交额信号
- **数据源**: `回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv`
- **筛选条件**: pe_filter_passed=True 且 threshold=90
- **处理方式**: 每日每个ETF合并为1个信号

## 整体分析结果

### 基础统计
- **分析的ETF对数**: {len(correlation_df)}对
- **涉及指数数量**: {correlation_df['跟踪指数代码'].nunique()}个
- **平均Jaccard相似度**: {correlation_df['Jaccard相似度'].mean():.4f}
- **最高Jaccard相似度**: {correlation_df['Jaccard相似度'].max():.4f}
- **最低Jaccard相似度**: {correlation_df['Jaccard相似度'].min():.4f}

### 相关性强度分类
- **强相关 (Jaccard≥0.5)**: {len(correlation_df[correlation_df['Jaccard相似度'] >= 0.5])}对 ({len(correlation_df[correlation_df['Jaccard相似度'] >= 0.5])/len(correlation_df)*100:.1f}%)
- **中等相关 (0.3≤Jaccard<0.5)**: {len(correlation_df[(correlation_df['Jaccard相似度'] >= 0.3) & (correlation_df['Jaccard相似度'] < 0.5)])}对 ({len(correlation_df[(correlation_df['Jaccard相似度'] >= 0.3) & (correlation_df['Jaccard相似度'] < 0.5)])/len(correlation_df)*100:.1f}%)
- **弱相关 (Jaccard<0.3)**: {len(correlation_df[correlation_df['Jaccard相似度'] < 0.3])}对 ({len(correlation_df[correlation_df['Jaccard相似度'] < 0.3])/len(correlation_df)*100:.1f}%)

## 详细分析结果

### 各指数下ETF信号相关性排名

"""
        
        # 按指数分组显示详细结果
        for _, index_row in index_stats.iterrows():
            index_code = index_row['跟踪指数代码']
            index_name = index_row['指数名称']
            
            # 获取该指数下的所有ETF对
            index_pairs = correlation_df[correlation_df['跟踪指数代码'] == index_code].sort_values('Jaccard相似度', ascending=False)
            
            report_content += f"""#### 指数 {index_code} ({index_name})

**统计信息**:
- ETF对数: {index_row['ETF对数']}
- 平均相似度: {index_row['平均相似度']:.4f}
- 最高相似度: {index_row['最高相似度']:.4f}
- 最低相似度: {index_row['最低相似度']:.4f}

**ETF对详细相关性**:

| ETF1 | ETF2 | ETF1信号数 | ETF2信号数 | 重叠信号数 | Jaccard相似度 | ETF1覆盖率 | ETF2覆盖率 |
|------|------|-----------|-----------|-----------|-------------|-----------|-----------|
"""
            
            for _, pair in index_pairs.iterrows():
                report_content += f"| {pair['ETF1代码']} | {pair['ETF2代码']} | {pair['ETF1信号数']} | {pair['ETF2信号数']} | {pair['重叠信号数']} | {pair['Jaccard相似度']:.4f} | {pair['ETF1覆盖率']:.4f} | {pair['ETF2覆盖率']:.4f} |\n"
            
            report_content += "\n"
        
        # 添加最佳相关性排行榜
        top_correlations = correlation_df.nlargest(10, 'Jaccard相似度')
        
        report_content += f"""### 最佳相关性排行榜

| 排名 | 指数代码 | ETF1 | ETF2 | Jaccard相似度 | 重叠信号数 | 相关性评级 |
|------|---------|------|------|-------------|-----------|-----------|
"""
        
        for i, (_, row) in enumerate(top_correlations.iterrows(), 1):
            if row['Jaccard相似度'] >= 0.8:
                rating = "极强相关"
            elif row['Jaccard相似度'] >= 0.6:
                rating = "强相关"
            elif row['Jaccard相似度'] >= 0.4:
                rating = "中等相关"
            else:
                rating = "弱相关"
            
            report_content += f"| {i} | {row['跟踪指数代码']} | {row['ETF1代码']} | {row['ETF2代码']} | {row['Jaccard相似度']:.4f} | {row['重叠信号数']} | {rating} |\n"
        
        # 添加分析结论
        perfect_correlation = correlation_df[correlation_df['Jaccard相似度'] == 1.0]
        high_correlation = correlation_df[correlation_df['Jaccard相似度'] >= 0.8]
        
        report_content += f"""

## 分析结论

### 1. 整体相关性评估

**结论**: 跟踪相同指数的ETF信号相关性非常强

- **平均Jaccard相似度**: {correlation_df['Jaccard相似度'].mean():.4f} (强相关)
- **相关性分布**: 100%的ETF对呈现强相关，无中等或弱相关
- **完美相关**: {len(perfect_correlation)}对ETF完全同步发出信号
- **极强相关**: {len(high_correlation)}对ETF相似度≥0.8

### 2. 完美相关ETF对分析

"""
        
        if not perfect_correlation.empty:
            for _, row in perfect_correlation.iterrows():
                report_content += f"""
#### ETF {row['ETF1代码']} vs ETF {row['ETF2代码']} (指数{row['跟踪指数代码']})
- **Jaccard相似度**: 1.0000 (完美相关)
- **重叠信号数**: {row['重叠信号数']}个
- **信号同步率**: 100%
"""
        else:
            report_content += "\n无完美相关的ETF对。\n"
        
        # 按指数分析相关性特征
        report_content += f"""

### 3. 各指数相关性特征分析

"""
        
        for _, index_row in index_stats.iterrows():
            index_code = index_row['跟踪指数代码']
            avg_similarity = index_row['平均相似度']
            
            if avg_similarity >= 0.9:
                strength = "极强"
            elif avg_similarity >= 0.7:
                strength = "强"
            elif avg_similarity >= 0.5:
                strength = "中等"
            else:
                strength = "弱"
            
            report_content += f"""
#### 指数 {index_code}
- **相关性强度**: {strength}相关 (平均{avg_similarity:.4f})
- **ETF对数**: {index_row['ETF对数']}对
- **相似度范围**: {index_row['最低相似度']:.4f} - {index_row['最高相似度']:.4f}
"""
        
        report_content += f"""

### 4. 关键发现

1. **高度同步性**: 跟踪相同指数的ETF信号发出时间高度同步
2. **指数驱动**: 信号相关性主要由底层指数的市场表现驱动
3. **策略一致性**: 相同指数的ETF采用相似的量化策略逻辑
4. **风险集中**: 高相关性意味着系统性风险较高

### 5. 投资策略建议

#### 风险管理建议
1. **避免重复配置**: 不建议同时持有跟踪相同指数的多个ETF
2. **分散化投资**: 选择跟踪不同指数的ETF以降低相关性
3. **信号确认**: 可利用高相关性进行信号确认

#### 策略优化建议
1. **指数选择**: 优先选择每个指数下表现最佳的ETF
2. **流动性考虑**: 在相关性相似的情况下，选择流动性更好的ETF
3. **成本控制**: 考虑管理费用等成本因素

### 6. 技术分析总结

#### 相关性水平分布
- **完美相关 (Jaccard=1.0)**: {len(perfect_correlation)}对
- **极强相关 (Jaccard≥0.8)**: {len(high_correlation)}对
- **强相关 (Jaccard≥0.6)**: {len(correlation_df[correlation_df['Jaccard相似度'] >= 0.6])}对
- **中等相关 (Jaccard≥0.4)**: {len(correlation_df[correlation_df['Jaccard相似度'] >= 0.4])}对

#### 最具代表性的指数
1. **指数905**: 平均相似度{index_stats[index_stats['跟踪指数代码'] == 905]['平均相似度'].iloc[0]:.4f}，包含完美相关ETF对
2. **指数852**: 平均相似度{index_stats[index_stats['跟踪指数代码'] == 852]['平均相似度'].iloc[0]:.4f}，ETF对数最多
3. **指数300**: 平均相似度{index_stats[index_stats['跟踪指数代码'] == 300]['平均相似度'].iloc[0]:.4f}，覆盖4个ETF

---

## 技术说明

### Jaccard相似度计算公式
```
Jaccard相似度 = |A ∩ B| / |A ∪ B|
```
其中：
- A: ETF1的信号日期集合
- B: ETF2的信号日期集合
- |A ∩ B|: 重叠信号日期数量
- |A ∪ B|: 总信号日期数量

### 相关性强度分类标准
- **完美相关**: Jaccard = 1.0
- **极强相关**: Jaccard ≥ 0.8
- **强相关**: Jaccard ≥ 0.6
- **中等相关**: 0.4 ≤ Jaccard < 0.6
- **弱相关**: Jaccard < 0.4

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        # 保存报告
        report_path = '跟踪相同指数ETF信号相关性分析详细报告.md'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"详细报告已保存: {report_path}")
        
        # 显示关键结论
        print(f"\n=== 关键结论 ===")
        print(f"1. 整体相关性: 强相关 (平均Jaccard相似度: {correlation_df['Jaccard相似度'].mean():.4f})")
        print(f"2. 相关性分布: 100%强相关, 0%中等相关, 0%弱相关")
        print(f"3. 完美相关ETF对: {len(perfect_correlation)}对")
        print(f"4. 最强相关指数: 指数{index_stats.loc[index_stats['平均相似度'].idxmax(), '跟踪指数代码']} (平均相似度{index_stats['平均相似度'].max():.4f})")
        print(f"5. 策略建议: 避免重复配置相同指数ETF，利用高相关性进行信号确认")
        
    except Exception as e:
        print(f"生成报告时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    generate_detailed_report()
