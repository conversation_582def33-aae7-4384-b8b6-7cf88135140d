#!/usr/bin/env python3
"""
验证现有计算逻辑的问题
"""

import pandas as pd

def check_existing_logic():
    """检查现有计算逻辑"""
    print("=== 检查现有计算逻辑 ===")
    
    # 1. 检查现有脚本的信号筛选条件
    script_path = '超额收益_分钟级成交额_90分位信号/etf_excess_return_analysis.py'
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("现有脚本中的信号筛选条件:")
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if 'threshold' in line and ('>' in line or '=' in line):
            print(f"  第{i+1}行: {line.strip()}")
    
    # 2. 检查原始信号数据
    print(f"\n=== 检查原始信号数据 ===")
    signals_df = pd.read_csv('回测结果/5分钟成交额_PE筛选_近90交易日分位数_不含开市闭市10分钟/所有信号明细_5min_PE筛选.csv')
    signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
    signals_df['date'] = signals_df['datetime'].dt.date
    
    print(f"原始信号总数: {len(signals_df)}")
    
    # 检查threshold分布
    threshold_counts = signals_df['threshold'].value_counts().sort_index()
    print(f"threshold分布:")
    for threshold, count in threshold_counts.items():
        print(f"  {threshold}: {count} 个")
    
    # 检查PE筛选情况
    pe_passed = signals_df['pe_filter_passed'].value_counts()
    print(f"PE筛选情况:")
    for status, count in pe_passed.items():
        print(f"  {status}: {count} 个")
    
    # 检查90分位且PE筛选通过的信号
    target_signals = signals_df[
        (signals_df['pe_filter_passed'] == True) &
        (signals_df['threshold'] == 90)
    ]
    print(f"90分位且PE筛选通过的信号: {len(target_signals)} 个")
    
    # 检查时间范围
    start_date = pd.to_datetime('2024-04-30').date()
    end_date = pd.to_datetime('2025-04-30').date()
    
    period_signals = target_signals[
        (target_signals['date'] >= start_date) &
        (target_signals['date'] <= end_date)
    ]
    print(f"目标时间范围内的信号: {len(period_signals)} 个")
    
    # 按ETF统计
    if not period_signals.empty:
        etf_counts = period_signals['etf_code'].value_counts().sort_index()
        print(f"各ETF信号数量:")
        for etf_code, count in etf_counts.items():
            print(f"  ETF {etf_code}: {count} 个")
    
    # 3. 对比现有结果
    print(f"\n=== 对比现有结果 ===")
    try:
        existing_results = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')
        print(f"现有结果中的ETF数量: {len(existing_results)}")
        print(f"现有结果中的总交易次数: {existing_results['交易次数'].sum()}")
        
        print(f"现有结果前5行:")
        print(existing_results[['ETF代码', '交易次数', '胜率(%)', '超额收益率(%)']].head())
        
    except Exception as e:
        print(f"读取现有结果时出错: {e}")

def check_data_consistency():
    """检查数据一致性"""
    print(f"\n=== 检查数据一致性 ===")
    
    # 检查ETF映射数据
    etf_mapping = pd.read_excel('市盈率信息/ETF跟踪指数.xlsx')
    print(f"ETF映射数据: {len(etf_mapping)} 条")
    print(f"ETF映射列名: {etf_mapping.columns.tolist()}")
    print(f"ETF代码范围: {etf_mapping['ETF代码'].min()} - {etf_mapping['ETF代码'].max()}")
    
    # 检查指数行情数据
    index_data = pd.read_excel('指数行情序列.xlsx')
    print(f"指数行情数据: {len(index_data)} 条")
    print(f"指数行情列名: {index_data.columns.tolist()}")
    
    # 检查时间范围
    index_data['时间'] = pd.to_datetime(index_data['时间'])
    print(f"指数行情时间范围: {index_data['时间'].min()} - {index_data['时间'].max()}")
    
    # 检查指数代码
    if '代码' in index_data.columns:
        unique_codes = index_data['代码'].unique()
        print(f"指数代码数量: {len(unique_codes)}")
        print(f"指数代码示例: {list(unique_codes)[:10]}")

if __name__ == "__main__":
    check_existing_logic()
    check_data_consistency()
