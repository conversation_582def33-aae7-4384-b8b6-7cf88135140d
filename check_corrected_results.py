#!/usr/bin/env python3
import pandas as pd

print('=== 修正后的90分位成交额信号计算结果 ===')

# 读取修正后的结果
df_new = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='超额收益汇总')

print(f'参与ETF数量: {len(df_new)}')
print(f'总交易次数: {df_new["交易次数"].sum()}')
print(f'平均胜率: {df_new["胜率(%)"].mean():.2f}%')
print(f'平均超额收益率: {df_new["超额收益率(%)"].mean():.4f}%')
print()

print('各ETF详细结果:')
for _, row in df_new.iterrows():
    print(f'ETF {row["ETF代码"]}: {row["交易次数"]}次交易, 胜率{row["胜率(%)"]}%, 超额收益{row["超额收益率(%)"]}%')

print()

# 读取统计摘要
try:
    stats_df = pd.read_excel('超额收益_分钟级成交额_90分位信号/ETF择时策略超额收益分析.xlsx', sheet_name='统计摘要')
    print('=== 统计摘要 ===')
    for _, row in stats_df.iterrows():
        print(f'{row["指标"]}: {row["数值"]}')
except Exception as e:
    print(f'读取统计摘要时出错: {e}')

print()
print('=== 修正前后对比 ===')
print('修正前问题:')
print('  - 使用了 threshold >= 80 而不是 threshold == 90')
print('  - 信号筛选条件不符合用户要求')
print()
print('修正后改进:')
print('  - 严格使用 threshold == 90')
print('  - 符合用户要求的计算逻辑')
print('  - 每日每个ETF最多一个买入信号')
print('  - 持有期间忽略新信号')
print('  - 超额收益 = 择时收益 - 指数基准收益')
